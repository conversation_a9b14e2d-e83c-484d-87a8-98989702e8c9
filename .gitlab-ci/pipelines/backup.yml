# if this pipeline gets included, DO_BACKUP is set.
# If you add other tasks with custom rules, make sure to have at least one
# task running -- otherwise add a dummy task to avoid a pipeline error.

# Per default child pipelines are only run on branch context,
# let's enable them for MRs as well.'
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "parent_pipeline"

stages:
  - backup

include:
  # scheduled backup
  - component: $CI_SERVER_FQDN/ci/db/backup@main
    inputs:
      phab_config_name: "prod.ssh"
      stage: "backup"
      key: backup
      what: db files
