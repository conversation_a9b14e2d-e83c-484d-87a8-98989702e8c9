# Per default child pipelines are only run on branch context,
# let's enable them for MRs as well.'
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "parent_pipeline"

stages:
  - lint
  - build
  - test

# Empty job in cases where the child pipeline would be empty.
empty:
  stage: build
  rules:
    - if: $DO_BUILD == "true" && $CI_COMMIT_REF_NAME != "dev"
  script:
    - env | grep "DO_"

include:
  - component: $CI_SERVER_FQDN/ci/docker/lint@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "component-library-support"
      stage: "lint"
      dockerfile: "apps/component-library-support/Dockerfile.miyagi"

  - component: $CI_SERVER_FQDN/ci/php/lint-twig@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "component-library-support"
      stage: "lint"
      build_context: "apps/component-library-support"

  - component: $CI_SERVER_FQDN/ci/npm/lint-miyagi@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "component-library-support"
      stage: "lint"
      build_context: "apps/component-library-support"
      needs:
        - pipeline: $PARENT_PIPELINE_ID
          job: "build-frontend-support"

  - component: $CI_SERVER_FQDN/ci/npm/test-miyagi@main
    rules:
      - if: $DO_TEST == "true"
    inputs:
      key: "component-library-support"
      stage: "test"
      build_context: "apps/component-library-support"
      needs:
        - pipeline: $PARENT_PIPELINE_ID
          job: "build-frontend-support"

  - component: $CI_SERVER_FQDN/ci/npm/playwright-test-base@main
    inputs:
      key: "component-library-support"
      stage: "test"
      working_dir: "apps/component-library-support/tests/vrt"
      script_name: vrt.test.js
      cl_service: "miyagisupport"

  - component: $CI_SERVER_FQDN/ci/docker/build-base@main
    inputs:
      key: miyagi-support
      dockerfile: "Dockerfile.miyagi"
      stage: "build"
      build_context: "apps/component-library-support"
      service: "miyagisupport"

build-docker-image:miyagi-support:
  extends: .build-docker-image:miyagi-support
  needs:
    - pipeline: $PARENT_PIPELINE_ID
      job: "build-frontend-support"
  rules:
    - if: $DO_BUILD == "true" && $CI_COMMIT_REF_NAME == "dev"
    - if: $DO_TEST == "true"

playwright-test-component-library-support:
  extends: .playwright-test-component-library-support
  rules:
    - if: $DO_TEST == "true"
  needs:
    - build-docker-image:miyagi-support
  when: manual
  allow_failure: true
  retry: 2
