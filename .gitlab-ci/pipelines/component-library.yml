# Per default child pipelines are only run on branch context,
# let's enable them for MRs as well.'
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "parent_pipeline"

stages:
  - lint
  - build
  - test

# Empty job in cases where the child pipeline would be empty.
empty:
  stage: build
  rules:
    - if: $DO_BUILD == "true" && ($CI_COMMIT_REF_NAME != "dev" || $CI_COMMIT_TAG != "")
  script:
    - env | grep "DO_"

include:
  - component: $CI_SERVER_FQDN/ci/docker/lint@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "component-library"
      stage: "lint"
      dockerfile: "apps/component-library/Dockerfile.miyagi"

  - component: $CI_SERVER_FQDN/ci/php/lint-twig@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "component-library"
      stage: "lint"
      build_context: "apps/component-library"

  - component: $CI_SERVER_FQDN/ci/npm/lint-miyagi@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "component-library"
      stage: "lint"
      build_context: "apps/component-library"
      needs:
        - pipeline: $PARENT_PIPELINE_ID
          job: "build-frontend"

  - component: $CI_SERVER_FQDN/ci/npm/test-miyagi@main
    rules:
      - if: $DO_TEST == "true"
    inputs:
      key: "component-library"
      stage: "test"
      build_context: "apps/component-library"

  - component: $CI_SERVER_FQDN/ci/npm/playwright-test-base@main
    inputs:
      key: "component-library"
      stage: "test"
      working_dir: "apps/component-library/tests/vrt"
      script_name: vrt.test.js

  - component: $CI_SERVER_FQDN/ci/docker/build-base@main
    inputs:
      key: miyagi
      dockerfile: "Dockerfile.miyagi"
      stage: "build"
      build_context: "apps/component-library"
      service: "miyagi"

build-docker-image:miyagi:
  extends: .build-docker-image:miyagi
  needs:
    - pipeline: $PARENT_PIPELINE_ID
      job: "build-frontend"
  rules:
    - if: $DO_BUILD == "true" && ($CI_COMMIT_TAG || $CI_COMMIT_REF_NAME == "dev")
    - if: $DO_TEST == "true"

playwright-test-component-library:
  extends: .playwright-test-component-library
  rules:
    - if: $DO_TEST == "true"
  needs:
    - build-docker-image:miyagi
  when: manual
  allow_failure: true
  retry: 2
