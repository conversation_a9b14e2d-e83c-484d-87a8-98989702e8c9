# if this pipeline gets included, DO_DEPLOY is set.
# If you add other tasks with custom rules, make sure to have at least one
# task running -- otherwise add a dummy task to avoid a pipeline error.

# Per default child pipelines are only run on branch context,
# let's enable them for MRs as well.'
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "parent_pipeline"

stages:
  - backup
  - deploy

include:
  # Backup before deployment.
  - component: $CI_SERVER_FQDN/ci/db/backup@main
    inputs:
      phab_config_name: "prod.ssh"
      stage: "deploy"
      what: db
      key: prod

  - component: $CI_SERVER_FQDN/ci/db/backup@main
    rules:
      - if: $CI_COMMIT_BRANCH == "stage"
    inputs:
      phab_config_name: "stage.ssh"
      stage: "deploy"
      what: db
      key: stage

  # Deploy to Oterma.
  - component: $CI_SERVER_FQDN/ci/phabalicious/phab@main
    rules:
      - if: $CI_COMMIT_REF_NAME == "dev"
    inputs:
      phab_config_name: finstral.oterma.factorial.io
      stage: "deploy"
      key: "deploy-setup"
      command: "docker login pull rm run --set host.app_tag=$CI_COMMIT_REF_SLUG --arguments user=gitlab-ci-token --arguments password=$CI_JOB_TOKEN"

  - component: $CI_SERVER_FQDN/ci/phabalicious/deploy@main
    rules:
      - if: $CI_COMMIT_REF_NAME == "dev"
    inputs:
      phab_config_name: finstral.oterma.factorial.io
      stage: "deploy"
      needs:
        - run-phab-deploy-setup
      key: "oterma-dev"
      arguments: "--set host.app_tag=$CI_COMMIT_REF_SLUG --arguments user=gitlab-ci-token --arguments password=$CI_JOB_TOKEN"
      environment_url: "https://finstral.oterma.factorial.io"

  # Deploy to Platform.sh stage.
  - component: $CI_SERVER_FQDN/ci/phabalicious/deploy@main
    rules:
      - if: $CI_COMMIT_REF_NAME == "stage"
    inputs:
      phab_config_name: stage
      stage: "deploy"
      key: "platform-stage"
      working_dir: apps/drupal
      environment_url: https://stage-y77w3ti-flnoi6xfio6tw.eu-5.platformsh.site/
      needs:
        - run-phab-backup-stage

  # Deploy to Platform.sh production.
  - component: $CI_SERVER_FQDN/ci/phabalicious/deploy@main
    inputs:
      phab_config_name: prod
      stage: "deploy"
      key: "platform-prod"
      working_dir: apps/drupal
      environment_url: https://multitenant.finstral.cloud/
      needs:
        - run-phab-backup-prod

  # Manual database synchronization jobs after production deployment.
  - component: $CI_SERVER_FQDN/ci/phabalicious/phab@main
    inputs:
      phab_config_name: stage.ssh
      stage: "deploy"
      key: "sync-stage"
      command: "copy-from prod.ssh"
      needs:
        - run-phab-deploy-platform-prod

  - component: $CI_SERVER_FQDN/ci/phabalicious/phab@main
    inputs:
      phab_config_name: finstral.oterma.factorial.io
      stage: "deploy"
      key: "sync-dev"
      command: "copy-from prod.ssh"
      needs:
        - run-phab-deploy-platform-prod

  # Deploy component library.
  - component: $CI_SERVER_FQDN/ci/k8s/prepare-namespace@main
    rules:
      - if: $CI_COMMIT_TAG
    inputs:
      key: "decasteljau"
      stage: "deploy"
      tags:
        - decasteljau
      namespace: "miyagi-finstral-com"
      registry_user: deploy-bot
      registry_password: $DEPLOY_BOT_TOKEN

  - component: $CI_SERVER_FQDN/ci/decasteljau/phab@main
    rules:
      - if: $CI_COMMIT_TAG
    inputs:
      phab_config_name: miyagi.finstral.com
      stage: "deploy"
      key: "deploy-component-library"
      command: "deploy --set app_tag=$CI_COMMIT_TAG"
      environment_url: https://miyagi.finstral.com
      needs:
        - k8s-prepare-namespace-decasteljau

run-phab-backup-prod:
  rules:
    - if: $CI_COMMIT_TAG

run-phab-deploy-platform-prod:
  rules:
    - if: $CI_COMMIT_TAG

run-phab-sync-stage:
  rules:
    - if: $CI_COMMIT_TAG
  when: manual

run-phab-sync-dev:
  rules:
    - if: $CI_COMMIT_TAG
  when: manual
