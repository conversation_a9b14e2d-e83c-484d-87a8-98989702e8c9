# Per default child pipelines are only run on branch context,
# let's enable them for MRs as well.'
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "parent_pipeline"

stages:
  - lint
  - test
  - build

# Empty job in cases where the child pipeline would be empty.
empty:
  stage: build
  rules:
    - if: $DO_BUILD == "true" && $CI_COMMIT_REF_NAME != "dev"
  script:
    - env | grep "DO_"

include:
  - component: $CI_SERVER_FQDN/ci/docker/lint@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "docs"
      stage: "lint"
      dockerfile: "apps/docs/Dockerfile.docs"

  - component: $CI_SERVER_FQDN/ci/docker/build-base@main
    inputs:
      dockerfile: "Dockerfile.docs"
      stage: "build"
      build_context: "apps/docs"
      key: "docs"
      service: "docs"

build-docker-image:docs:
  extends: .build-docker-image:docs
  rules:
    - if: $DO_BUILD == "true" && $CI_COMMIT_REF_NAME == "dev"
