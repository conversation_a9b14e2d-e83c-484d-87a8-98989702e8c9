# Per default child pipelines are only run on branch context,
# let's enable them for MRs as well.'
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "parent_pipeline"

stages:
  - lint
  - build
  - test

variables:
  IMAGE_TAG: $CI_COMMIT_TAG || $CI_COMMIT_REF_SLUG

include:
  # Lint docker files
  - component: $CI_SERVER_FQDN/ci/docker/lint@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "nginx"
      stage: "lint"
      dockerfile: "apps/drupal/Dockerfile.nginx"

  - component: $CI_SERVER_FQDN/ci/docker/lint@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "php"
      stage: "lint"
      dockerfile: "apps/drupal/Dockerfile.php"

  - component: $CI_SERVER_FQDN/ci/docker/lint@main
    rules:
      - if: $DO_LINT == "true"
    inputs:
      key: "cli"
      stage: "lint"
      dockerfile: "apps/drupal/Dockerfile.builder"

  # lint PHP
  - component: $CI_SERVER_FQDN/ci/php/lint-php@main
    inputs:
      stage: "lint"
      key: "drupal"
      php_version: "83"
      build_context: "apps/drupal"

  # build docker files
  - component: $CI_SERVER_FQDN/ci/docker/build-base@main
    inputs:
      dockerfile: "Dockerfile.builder"
      app_tag: auto
      stage: "build"
      build_context: "apps/drupal"
      build_arg: "FACTORIAL_FRIDGE_ACCESS_TOKEN=$FACTORIAL_FRIDGE_ACCESS_TOKEN"
      key: "builder"
      service: "builder"
      before_script:
        - echo "Copying frontend artifacts into theme directory ..."
        - cp apps/component-library/finstral_global.breakpoints.yml apps/drupal/web/themes/custom/finstral_global/finstral_global.breakpoints.yml
        - cp apps/component-library/finstral_global.libraries.yml apps/drupal/web/themes/custom/finstral_global/finstral_global.libraries.yml
        - cp -r apps/component-library/build/ apps/drupal/web/themes/custom/finstral_global/build/
        - cp -r apps/component-library/email/ apps/drupal/web/themes/custom/finstral_global/email/
        - cp -r apps/component-library/src/ apps/drupal/web/themes/custom/finstral_global/src/
        - echo "Copying frontend (support) artifacts into theme directory ..."
        - cp apps/component-library-support/circle_dot.libraries.yml apps/drupal/web/themes/custom/circle_dot/circle_dot.libraries.yml
        - cp -r apps/component-library-support/build/ apps/drupal/web/themes/custom/circle_dot/build/
        - cp -r apps/component-library-support/src/ apps/drupal/web/themes/custom/circle_dot/src/

  - component: $CI_SERVER_FQDN/ci/docker/build-base@main
    inputs:
      dockerfile: "Dockerfile.php"
      needs: [build-docker-image:builder]
      app_tag: auto
      stage: "build"
      build_context: "apps/drupal"
      key: "php"
      service: "php"
      build_arg: "CLI_IMAGE=$CI_REGISTRY_IMAGE/builder:$IMAGE_TAG"

  - component: $CI_SERVER_FQDN/ci/docker/build-base@main
    inputs:
      dockerfile: "Dockerfile.nginx"
      needs: [build-docker-image:builder]
      app_tag: auto
      stage: "build"
      build_context: "apps/drupal"
      key: "nginx"
      service: "nginx"
      build_arg: "CLI_IMAGE=$CI_REGISTRY_IMAGE/builder:$IMAGE_TAG"

build-docker-image:builder:
  extends: .build-docker-image:builder
  needs:
    - pipeline: $PARENT_PIPELINE_ID
      job: "build-frontend"
      artifacts: true
    - pipeline: $PARENT_PIPELINE_ID
      job: "build-frontend-support"
      artifacts: true
  rules:
    - if: $DO_BUILD == "true"
    - if: $DO_TEST == "true"

build-docker-image:php:
  extends: .build-docker-image:php
  rules:
    - if: $DO_BUILD == "true" && $CI_COMMIT_REF_NAME == "dev"

build-docker-image:nginx:
  extends: .build-docker-image:nginx
  rules:
    - if: $DO_BUILD == "true" && $CI_COMMIT_REF_NAME == "dev"

# The lint task needs credentials for the private packages, an easy way is to
# extend the lint-task and add a before_script:
lint-php-drupal:
  before_script:
    - composer config --global gitlab-token.source.factorial.io token "$FACTORIAL_FRIDGE_ACCESS_TOKEN"
  rules:
    - if: $DO_LINT == "true"

# Use custom script for PHPUnit tests.
unit-tests-php-drupal:
  tags:
    - fabalicious-fast-lane
  script:
    - cd apps/drupal
    - phab -cci script docker:login --arguments user=gitlab-ci-token --no-interaction
    - phab -cci script test:backend --arguments tag=$CI_COMMIT_REF_SLUG --no-interaction
  needs:
    - build-docker-image:builder
  rules:
    - if: $DO_TEST == "true"
