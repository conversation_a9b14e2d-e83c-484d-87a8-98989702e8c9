# A special pipeline to run tasks which other pipelines depend on.
# Please note that the while content gets injected by the detect.sh script.

# for some reason rules with <PERSON>O_ do not work for this particular include,
# so that's why it is split up into two parts.
include:
  - component: source.factorial.io/ci/npm/install-and-run-base@main
    inputs:
      key: frontend
      stage: "preparation"
      build_context: "apps/component-library"
      command: build:assets
      artifacts:
        - apps/component-library/build
        - apps/component-library/src
        - apps/component-library/finstral_global.breakpoints.yml
        - apps/component-library/finstral_global.libraries.yml
        - apps/component-library/node_modules

  - component: source.factorial.io/ci/npm/install-and-run-base@main
    inputs:
      key: frontend-support
      stage: "preparation"
      build_context: "apps/component-library-support"
      command: build:assets
      artifacts:
        - apps/component-library-support/build
        - apps/component-library-support/src
        - apps/component-library-support/circle_dot.libraries.yml
        - apps/component-library-support/node_modules

build-frontend:
  extends: .npm-install-and-run-frontend
  script:
    - cd apps/component-library
    - yarn install --frozen-lockfile --ignore-engines && yarn cache clean
    - NODE_ENV=production yarn run build:assets
  rules:
    - if: $DO_BUILD == "true"
    - if: $DO_LINT == "true"
    - if: $DO_TEST == "true"

build-frontend-support:
  extends: .npm-install-and-run-frontend-support
  script:
    - cd apps/component-library-support
    - yarn install --frozen-lockfile --ignore-engines && yarn cache clean
    - NODE_ENV=production yarn run build:assets
  rules:
    - if: $DO_BUILD == "true"
    - if: $DO_LINT == "true"
    - if: $DO_TEST == "true"
