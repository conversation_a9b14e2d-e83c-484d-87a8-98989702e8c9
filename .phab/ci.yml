onePassword:
  factorial:
    endpoint: https://vault.factorial.io

secrets:
  ci-build-token:
    question: Please provide the docker registry password for user `<EMAIL>`
    onePasswordId: tnl2nyal7g4sk2h6ukwobrwgsu
    onePasswordVaultId: bkiebi7i6gkh4cjonj4uys24xe
    tokenId: factorial

hosts:
  ci:
    hidden: true
    needs:
      - git
      - composer
      - local
    scripts:

      docker:login:
        defaults:
          user: gitlab-ci-token
        script:
          - docker login -u %arguments.user% -p %secret.ci-build-token% %settings.gitlab.registry%

      test:backend:
        environment:
          TAG: "%arguments.tag%"
        script:
          - log_message(info, "Install dev dependencies ...")
          - composer install
          - log_message(info, "Running unit-, functional- and kernel-tests ...")
          - su www-data -s /bin/bash hosting/tests/run_test.sh "find web/modules/custom ! -regex \".*/ExistingSite/.*\" -name \"*Test.php\" | ./vendor/liuggio/fastest/fastest -o -r \"vendor/phpunit/phpunit/phpunit {}\" -vvv"
          - log_message(info, "Installing Drupal ...")
          - vendor/bin/drush si --existing-config -y
          # Domain settings are not configured with initial install. Therefore,
          # the configuration is imported again.
          - vendor/bin/drush cim -y
          - log_message(info, "Running existing-site tests ...")
          - su www-data -s /bin/bash hosting/tests/run_test.sh "vendor/bin/phpunit --testsuite existing-site --no-progress --testdox"
        context: docker-compose-run
        rootFolder: ./hosting/tests
        service: php
