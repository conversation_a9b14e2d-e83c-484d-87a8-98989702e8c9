hosts:
  k8s-base:
    executables:
      drush: /app/vendor/bin/drush
    inheritOnly: true
    type: stage
    branch: main
    rootFolder: /app/web
    privateFilesFolder: /app/private-files
    backupFolder: /app/private-files/backups
    replaceSettingsFile: false
    alterSettingsFile: false
    database:
      skipCreateDatabase: true
    needs:
      - k8s
      - files
      - drush
    kube:
      namespace: circle-dot
      serviceName: builder
      podSelector:
        - service_name=%host.kube.serviceName%
      scaffolder:
        baseUrl: https://config.factorial.io/scaffold/kube/drupal
        template: index.yml
      deployments:
        - "%host.kube.parameters.name%-builder"
        - "%host.kube.parameters.name%-nginx"
        - "%host.kube.parameters.name%-php"

      parameters:
        tag: "%host.branch%"
        type: "%host.type%"
        publicFilesStorage: 1Gi
        letsencrypt: true
        cron:
          schedule: "12 * * * *"
          pod: builder
        pods:
          builder:
            name: builder
            image: "%settings.gitlab.imageBaseTag%/builder"
            volume: true
          nginx:
            name: nginx
            image: "%settings.gitlab.imageBaseTag%/nginx"
            port: 8080
            volume: true
            probes: /.lagoonhealthz
            requests:
              cpu: 100m
              memory: 64Mi
            limits:
              memory: 64Mi
          php:
            name: php
            port: 9000
            image: "%settings.gitlab.imageBaseTag%/php"
            volume: true
            requests:
              cpu: 100m
              memory: 64Mi
            limits:
              memory: 256Mi

  miyagi.finstral.com:
    app_tag: prod
    needs:
      - k8s
    kube:
      namespace: miyagi-finstral-com
      kubeconfig: ""
      scaffolder:
        baseUrl: ./hosting/miyagi.finstral.com
        template: index.yml
      podSelector:
        - app=%host.kube.parameters.name%
        - type=%host.type%
      parameters:
        registrySecret: registry-factorial
        name: component-library
        projectSlug: finstral-miyagi
        dockerImage: "%settings.gitlab.imageBaseTag%/miyagi:%host.app_tag%"
        containerPort: 80
        letsencrypt: 1
        replicas: 2
        cluster_issuer: letsencrypt
        hosts:
          - miyagi.finstral.com
          - miyagi.finstral.decasteljau.factorial.io
        limits:
          memory: 100M
