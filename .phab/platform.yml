hosts:
  platform.sh.base:
    inheritOnly: true
    type: stage
    needs:
      - git
      - artifacts--git
      - script
      - files
    backupFolder: /app/private/backups
    branch: stage
    user: flnoi6xfio6tw-stage-y77w3ti--drupal
    host: ssh.eu-5.platform.sh
    artifact:
      gitOptions:
        clone: []
      branch: stage
      baseBranch: release-2.x
      repository: <EMAIL>:flnoi6xfio6tw.git
      useLocalRepository: true
      actions:
        - action: script
          arguments:
            - docker login -u gitlab-ci-token -p %secret.ci-build-token% %settings.gitlab.registry%
        - action: docker-copy
          arguments:
            image: "%settings.gitlab.imageBaseTag%/builder:%host.branch%"
            imageRootPath: /app
            from: "*"
            to: .
        - action: script
          arguments:
            - scaffold(./hosting/platform.sh/%host.type%/index.yml, %context.data.targetDir%)
        - action: delete
          arguments:
            - .ddev/
            - .phab/
            - .tools/
            - hosting/
            - web/.gitignore
            - web/themes/custom/circle_dot/node_modules
            - web/themes/custom/circle_dot/cypress
            - web/themes/custom/circle_dot/tests
            - web/themes/custom/circle_dot/vendor
            - web/themes/custom/circle_dot/.cache
            - web/themes/custom/circle_dot/coverage
            - web/themes/custom/circle_dot/yarn-error.log
            - web/themes/custom/circle_dot/.gitignore
            - web/themes/custom/finstral_global/node_modules
            - web/themes/custom/finstral_global/cypress
            - web/themes/custom/finstral_global/tests
            - web/themes/custom/finstral_global/vendor
            - web/themes/custom/finstral_global/.cache
            - web/themes/custom/finstral_global/coverage
            - web/themes/custom/finstral_global/yarn-error.log
            - web/themes/custom/finstral_global/.gitignore
            - .dockerignore
            - .editorconfig
            - .fabfile.yaml
            - .gitattributes
            - .gitignore
            - Dockerfile.builder
            - Dockerfile.mariadb
            - Dockerfile.nginx
            - Dockerfile.php
            - grumphp.yml
            - phpcs.xml
            - phpstan.neon
            - phpunit.xml.dist
            # Remove after cleanup of the remote repository.
            - .changeset/
            - .gitlab-ci/
            - .husky/
            - .tugboat/
            - apps/
            - docs/
            - .fdocs.js
            - .gitlab-ci.yml
            - .hadolint.yaml
            - .lando.upstream.yml
            - .nvmrc
            - commitlint.config.js
            - composer-manifest.yaml
            - package.json
            - package-lock.json
            - README.md
            - renovate.json
            - test.txt
            - yarn.lock

  platform.sh.ssh:
    inheritOnly: true
    backupFolder: /app/private/backups
    needs:
      - drush
      - ssh
      - files
      - restic
    executables:
      drush: /app/vendor/bin/drush
    user: flnoi6xfio6tw-stage-y77w3ti--drupal
    host: ssh.eu-5.platform.sh
    rootFolder: /app/web
    gitRootFolder: /app

  stage.ssh:
    inheritsFrom: platform.sh.ssh
    copyFromFinished:
      - "execute(script, updateCloudIds)"
      - "execute(script, updateCloudLinks)"
  stage:
    inheritsFrom: platform.sh.base
    environment_indicator: stage
    finstral_sso_config: '%secret.finstral_sso.config.stage%'
    finstral_contacts_api_url: 'https://staging-contacts-api.finstral.cloud'
    ky2help_api_key: '%secret.ky2help.api_key.stage%'
    ky2help_base_url: 'https://servicedesktest.finstral.com'

  prod.ssh:
    inheritsFrom: platform.sh.ssh
    user: flnoi6xfio6tw-release-2-x-bw6yyry--drupal
  prod:
    inheritsFrom: platform.sh.base
    branch: main
    type: prod
    user: flnoi6xfio6tw-release-2-x-bw6yyry--drupal
    artifact:
      branch: release-2.x
    environment_indicator: production
    finstral_sso_config: '%secret.finstral_sso.config.prod%'
    finstral_contacts_api_url: 'https://contacts-api.finstral.cloud'
    ky2help_api_key: '%secret.ky2help.api_key.prod%'
    ky2help_base_url: 'https://servicedesk.finstral.com/'
