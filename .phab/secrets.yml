onePassword:
  factorial:
    endpoint: https://vault.factorial.io
  finstral:
    endpoint: https://vault.factorial.io

secrets:
  algolia.app_id:
    question: What is the Algolia application ID?
    onePasswordId: yjgzsscana37w5tsob2pdbghwe
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  algolia.search_api_key:
    question: What is the Algolia search API key?
    onePasswordId: bphhqhmdapcjmfikr4kfscerui
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  algolia.jobs_search_api_key:
    question: What is the Algolia jobs search API key?
    onePasswordId: s5oapioq6fduubghmculbonmue
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  algolia.write_api_key:
    question: What is the Algolia write API key?
    onePasswordId: x7tkuzm5h5efgkhz2wcpfcvaqm
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  algolia_dev.app_id:
    question: What is the Algolia application ID (Dev environment)?
    onePasswordId: 7elyootbe2vvm4wqwetjm6j734
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  algolia_dev.search_api_key:
    question: What is the Algolia search API key (Dev environment)?
    onePasswordId: ucjx2vuf6nbcucn2ws6w7kgvbi
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  algolia_dev.jobs_search_api_key:
    question: What is the Algolia jobs search API key (Dev environment)?
    onePasswordId: qsdkz4oib53wx3oh6dudtgsf4a
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  algolia_dev.write_api_key:
    question: What is the Algolia write API key (Dev environment)?
    onePasswordId: l27yr3kdli7ovjitf5a7dzg2va
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  vimeo.client_id:
    question: What is the client ID for Vimeo?
    onePasswordId: q755g4vb6ledajohkxxbexoip4
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  vimeo.client_secret:
    question: What is the client secret for Vimeo?
    onePasswordId: o7brt4azu4bf5r5zrutbqtscca
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  vimeo.api_access_token:
    question: What is the API access token for Vimeo?
    onePasswordId: j6b6246vxsq4ur3xzveu3icjeq
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  finstral_erp.ftp.url:
    question: What is the URL Finstral ERP?
    onePasswordId: ohwkevomlv3gbu5t66piokvswi
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  finstral_erp.ftp.username:
    question: What is the username to connect to Finstral ERP?
    onePasswordId: 2ccjbyylg3ebnb2stjsay3uifi
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  finstral_erp.ftp.password:
    question: What is the password to connect to Finstral ERP??
    onePasswordId: xakjkwuvchsfhjowocarcmy6ga
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  finstral_sso.config.prod:
    question: SSO Configuration for Finstral "Prod" environment?
    onePasswordId: ed5lgxl7vz2lydo4tw6bqfrke4
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  finstral_sso.config.stage:
    question: SSO Configuration for Finstral "Stage" environment?
    onePasswordId: rjnwagaozzoxmcf2cpv2pb3hsu
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  finstral_sso.config.oterma:
    question: SSO Configuration for Oterma environment?
    onePasswordId: xskyhtinw52mjgzpl4pidpnmgm
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  finstral_sso.config.ddev:
    question: SSO Configuration for ddev environment?
    onePasswordId: feo4uchrvqv6bqrd5ihozgrlya
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  dbip.api_key:
    question: What is the API key for DB-IP?
    onePasswordId: qvt3qyvzbqvwwathydwjpx7exu
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  onboard.api_key:
    question: What is the API key for Onboard HR API?
    onePasswordId: x4oeeq7zmt7e2yqu4j2dpxiypa
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  ky2help.api_key.prod:
    question: What is the API key for ky2help API?
    onePasswordId: bgrpbkvytoqtftbfpehcygypm4
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
  ky2help.api_key.stage:
    question: What is the API key for ky2help API (Stage)?
    onePasswordId: wrsjjz4wsa7pwsi4vhzherm42a
    onePasswordVaultId: mdc4og73lrab7evn7ulvc2v3rm
    tokenId: finstral
