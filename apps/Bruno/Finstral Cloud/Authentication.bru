meta {
  name: Authentication
  type: http
  seq: 1
}

get {
  url: 
  body: none
  auth: oauth2
}

auth:oauth2 {
  grant_type: authorization_code
  callback_url: {{callback_url}}
  authorization_url: {{authorization_url}}
  access_token_url: {{access_token_url}}
  client_id: FACTORIAL
  client_secret: {{sso.client_secret}}
  scope: openid profile fin_contacts fin_cloud offline_access
  state: 
  pkce: true
}

vars:post-response {
  sso.access_token: res.body.access_token
}
