meta {
  name: UserProfile PUT
  type: http
  seq: 2
}

put {
  url: {{base_url}}/api/UserProfile
  body: json
  auth: bearer
}

auth:bearer {
  token: {{sso.access_token}}
}

body:json {
  {
    "LanguageCode": "de",
    "User": {
      "Id": 55,
      "Login": "<EMAIL>",
      "FirstName": "<PERSON>",
      "LastName": "Tinkhauser",
      "Organisation": "SGF manager",
      "Email": "<EMAIL>",
      "Phone": "+390471296619",
      "LanguageCode": "en",
      "AvatarURL": "https://staging-login.finstral.cloud/ProfileImage/55?ts=1720421947",
      "Available": true,
      "Inactive": false,
      "NotificationsEnabled": false,
      "AppointmentMailEnabled": false,
      "ExternalMailCCEnabled": false,
      "CompletedMailEnabled": false,
      "LeadMailEnabled": false,
      "EnableReporting": true,
      "LastActivity": "2024-06-19T15:30:23Z"
    },
    "AvatarImage": {
      "Name": "gravatar-ftinkhauser.png",
      "MimeType": "image/png",
      "Body": "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",
      "Filedate": "2024-07-08T06:58:43Z"
    },
    "Roles": [
      "Editor (Contacts)",
      "Observer"
    ],
    "Rights": [
      "WebMonitor.Rek",
      "Contacts.Lead.Reject",
      "Contacts.Read.Hierarchy",
      "Extranet.Read",
      "Contacts.Contact.Edit",
      "WebMonitor.Kupo",
      "WebMonitor.OrderAccesories",
      "Contacts.Lead.ConnectContact",
      "Contacts.Lead.Takeover",
      "Contacts.Lead.Create",
      "Contacts.Read",
      "Contacts.Lead.Undo",
      "Contacts.Lead.Forward.Geolocation",
      "Contacts.Lead.Forward",
      "WebMonitor.Architekt",
      "WebMonitor.Read",
      "Contacts.Lead.LimitedStatus",
      "Contacts.Lead.EditAddress",
      "WebMonitor.Auftrag",
      "WebMonitor.Service",
      "Cloud.Read",
      "Contacts.Lead.CreateAppointment",
      "Contacts.Reporting",
      "Contacts.Lead.Process",
      "Contacts.Lead.OfferHandover",
      "Cloud.QuickStart.Center",
      "Contacts.Read.SameContact"
    ],
    "AdditionalOrganisations": [],
    "Absences": []
  }
}
