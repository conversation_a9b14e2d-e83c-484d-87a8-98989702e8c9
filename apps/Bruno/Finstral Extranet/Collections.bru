meta {
  name: Collections
  type: http
  seq: 8
}

get {
  url: {{base_url}}/api/v1/collections?page=1&pageSize=4&responseFields=["id", "createDate", "modifyDate", "name", "description", "isDynamic", "user", "filesQuantity", "filesQuantityWithFilter"]&showSubVersions=true
  body: none
  auth: bearer
}

params:query {
  page: 1
  pageSize: 4
  responseFields: ["id", "createDate", "modifyDate", "name", "description", "isDynamic", "user", "filesQuantity", "filesQuantityWithFilter"]
  showSubVersions: true
}

auth:bearer {
  token: {{api_key}}
}
