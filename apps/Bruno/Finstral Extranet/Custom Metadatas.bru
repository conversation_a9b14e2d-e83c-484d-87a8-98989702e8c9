meta {
  name: Custom Metadatas
  type: http
  seq: 11
}

get {
  url: {{base_url}}/api/v1/metadata/custom?responseFields=["id", "createDate", "modifyDate", "name", "editType", "selectionOptions", "translationState"]&page=1&pageSize=50
  body: none
  auth: bearer
}

params:query {
  responseFields: ["id", "createDate", "modifyDate", "name", "editType", "selectionOptions", "translationState"]
  page: 1
  pageSize: 50
}

auth:bearer {
  token: {{api_key}}
}
