meta {
  name: DownloadFormats
  type: http
  seq: 9
}

get {
  url: {{base_url}}/api/v1/downloadFormats?responseFields=["id", "name", "createDate", "modifyDate", "density", "maxSize", "unit", "quality", "colorspace", "formatConversion", "width", "height", "translationState"]
  body: none
  auth: bearer
}

params:query {
  responseFields: ["id", "name", "createDate", "modifyDate", "density", "maxSize", "unit", "quality", "colorspace", "formatConversion", "width", "height", "translationState"]
}

auth:bearer {
  token: {{api_key}}
}
