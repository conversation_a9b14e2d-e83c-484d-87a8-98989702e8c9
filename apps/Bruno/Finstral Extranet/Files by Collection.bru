meta {
  name: Files by Collection
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/v1/files?responseFields=["id", "subject"]&page=1&pageSize=500&filter={"filterType":"collection","collectionID":"1213577725" }
  body: none
  auth: bearer
}

params:query {
  responseFields: ["id", "subject"]
  page: 1
  pageSize: 500
  filter: {"filterType":"collection","collectionID":"1213577725" }
}

auth:bearer {
  token: {{api_key}}
}
