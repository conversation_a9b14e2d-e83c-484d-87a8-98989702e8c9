meta {
  name: Files by File IDs
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/v1/files?responseFields=["id", "subject"]&page=1&pageSize=500&filter={"filterType":"files","fileIDs":[2124798271, 1074059229]}
  body: none
  auth: bearer
}

params:query {
  responseFields: ["id", "subject"]
  page: 1
  pageSize: 500
  filter: {"filterType":"files","fileIDs":[2124798271, 1074059229]}
}

auth:bearer {
  token: {{api_key}}
}
