meta {
  name: Files by ModifyDate
  type: http
  seq: 4
}

get {
  url: {{base_url}}/api/v1/files?responseFields=["id", "subject"]&page=1&pageSize=500&filter={"filterType":"modifyDate","dateMin":"2024-11-15 13:35:52", "dateMax": null }
  body: none
  auth: bearer
}

params:query {
  responseFields: ["id", "subject"]
  page: 1
  pageSize: 500
  filter: {"filterType":"modifyDate","dateMin":"2024-11-15 13:35:52", "dateMax": null }
}

auth:bearer {
  token: {{api_key}}
}
