meta {
  name: Files by <PERSON><PERSON><PERSON>ack ID
  type: http
  seq: 5
}

get {
  url: {{base_url}}/api/v1/files?responseFields=["id", "subject", "fileExtension", "fileSize",  "fileName"]&page=1&pageSize=500&variantStackID=1650059348&sortBy=createDate&sortDirection=asc
  body: none
  auth: bearer
}

params:query {
  responseFields: ["id", "subject", "fileExtension", "fileSize",  "fileName"]
  page: 1
  pageSize: 500
  variantStackID: 1650059348
  sortBy: createDate
  sortDirection: asc
}

auth:bearer {
  token: {{api_key}}
}
