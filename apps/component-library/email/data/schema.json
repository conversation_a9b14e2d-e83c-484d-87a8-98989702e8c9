{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "email", "type": "object", "properties": {"logo": {"type": "object", "properties": {"src": {"type": "string", "format": "uri"}, "alt": {"type": "string"}, "href": {"type": "string", "format": "uri"}}, "required": ["src", "alt", "href"]}, "email_type": {"type": "string"}, "hero": {"type": "object", "properties": {"src": {"type": "string", "format": "uri"}, "alt": {"type": "string"}}, "required": ["src", "alt"]}, "message": {"type": "object", "properties": {"title": {"type": "string"}, "copy": {"type": "string", "format": "html"}}, "required": ["title", "copy"]}, "info": {"type": "object", "properties": {"title": {"type": "string"}, "position_title": {"type": "string"}, "position_copy": {"type": "string"}, "personal_data_title": {"type": "string"}, "personal_data_copy": {"type": "string", "format": "html"}}, "required": ["title", "personal_data_title", "personal_data_copy"]}, "phone": {"type": "object", "properties": {"title": {"type": "string"}, "copy": {"type": "string"}}, "required": ["title", "copy"]}, "contact": {"type": "object", "properties": {"title": {"type": "string"}, "copy": {"type": "string", "format": "html"}, "company_name": {"type": "string"}}, "required": ["title", "copy"]}, "follow_us": {"type": "object", "properties": {"title": {"type": "string"}, "copy": {"type": "string"}}, "required": ["title", "copy"]}, "social_nav": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "icon_src": {"type": "string", "format": "uri"}}, "required": ["title", "url", "icon_src"]}}, "legal": {"type": "string", "format": "html"}}, "required": ["logo", "email_type", "hero", "message", "info", "phone", "contact", "follow_us", "social_networks", "legal"]}