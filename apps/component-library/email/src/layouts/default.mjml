<mjml>
	<mj-head>
		<mj-title>{{ email_type }}</mj-title>
		<!-- prettier-ignore -->
		<mj-style>
			.footer-contact p {
				margin: 0;
			}

			.footer-contact a {
				color:#000;
				text-decoration: underline;
			}

			@media only screen and (max-width:480px) {
				.mobile-center {
					margin: 0 auto !important;
					float: none !important;
					display: table !important;
				}

				.mobile-padding-top {
					padding-top: 16px !important;
				}
			}
			@media only screen and (min-width:481px) {
				.header-desktop-padding {
					padding: 8px 48px !important;
				}

				.footer-desktop-padding {
					padding-left: 40px;
					padding-right: 40px;
				}

				.main-desktop-padding {
					padding: 16px 40px;
				}

				.greetings-desktop-padding {
					padding: 0 0 16px;
				}

				.information-desktop-padding {
					padding: 0 16px;
					background-color: #EFEAE5;
				}
			}
		</mj-style>
		<mj-attributes>
			<mj-text
				color="#000"
				font-size="18px"
				line-height="1.5"
				font-family="SF Pro, Helvetica, Arial, sans-serif"
			/>
			<mj-all padding="0" />
		</mj-attributes>
	</mj-head>
	<mj-body width="770px">
		<mj-include path="../includes/header.mjml" />

		<mj-section background-color="#fff" padding="0">
			<mj-column padding="0" width="100%">
				<mj-image src="{{ hero.src }}" width="770px" alt="{{ hero.alt }}" />
			</mj-column>
		</mj-section>

		<mj-section
			background-color="#fff"
			padding="32px 24px 0"
			css-class="main-desktop-padding"
		>
			<mj-column
				padding="0 0 0"
				width="100%"
				css-class="greetings-desktop-padding"
			>
				<mj-text padding="0 0 12px" font-size="28px" line-height="1.35"
					>{{ message.title }}</mj-text
				>
				<mj-text padding="0">{{ message.copy|raw }}</mj-text>
			</mj-column>
		</mj-section>

		<mj-section
			background-color="#fff"
			padding="0 24px 48px"
			css-class="main-desktop-padding"
		>
			<mj-column
				width="100%"
				padding="32px 16px 48px"
				background-color="#EFEAE5"
				css-class="information-desktop-padding"
			>
				<mj-text padding="0 0 24px" font-size="24px" line-height="1.35"
					>{{ info.title }}</mj-text
				>
				<mj-divider
					border-width="1px"
					border-style="solid"
					border-color="#BFBFBF"
					padding="0 0 24px"
				/>
				<mj-raw>{% if info.position_title and info.position_copy %}</mj-raw>
				<mj-text padding="0 0 24px"
					><strong>{{ info.position_title }}</strong><br />{{ info.position_copy
					}}</mj-text
				>
				<mj-divider
					border-width="1px"
					border-style="solid"
					border-color="#BFBFBF"
					padding="0 0 24px"
				/>
				<mj-raw>{% endif %}</mj-raw>
				<mj-text padding="0"
					><strong>{{ info.personal_data_title }}</strong><br />{{
					info.personal_data_copy|raw }}</mj-text
				>
			</mj-column>
		</mj-section>
		<mj-include path="../includes/footer.mjml" />
	</mj-body>
</mjml>
