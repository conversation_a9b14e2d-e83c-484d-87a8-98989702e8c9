gzip  on;
gzip_http_version 1.1;
gzip_vary on;
gzip_comp_level 6;
gzip_proxied any;
gzip_types text/plain text/html text/css application/json application/javascript application/x-javascript text/javascript text/xml application/xml application/rss+xml application/atom+xml application/rdf+xml;

# make sure gzip does not lose large gzipped js or css files
# see http://blog.leetsoft.com/2007/07/25/nginx-gzip-ssl.html
gzip_buffers 16 8k;

# Disable gzip for certain browsers.
gzip_disable “MSIE [1-6].(?!.*SV1)”;

server {
    listen       80;
    server_name  localhost;

    root   /usr/share/nginx/html;
    location / {
        try_files $uri $uri/ /index.html;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location ~ \.(css|js|htc|woff|jpg|eot|woff2|ttf|svg)$ {
        root   /usr/share/nginx/html;
        expires     1w;
        add_header  Cache-Control no-cache;
        add_header  Last-Modified "";
        add_header  ETag "";
        log_not_found off;
        access_log off;
        add_header "Access-Control-Allow-Origin"  *;
    }

}
