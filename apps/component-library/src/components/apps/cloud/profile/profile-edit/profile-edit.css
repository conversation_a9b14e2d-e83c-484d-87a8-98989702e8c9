@import "profile-image-editor/profile-image-editor.css";
@import "services/services.css";

/** @define ProfileEdit; */

.ProfileEdit-wrapper {
	background-color: var(--shared-color-surface-primary);
	border: var(--border-subtle);
	display: grid;
	padding: var(--ProfileEdit-wrapper-padding);
}

.ProfileEdit-header {
	grid-row: 1/2;
	margin-block-end: var(--ProfileEdit-spacing);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
profile-edit-form, /* Web Component */
.ProfileEdit-form,
.ProfileEdit-formFields {
	display: contents;
}

.ProfileEdit-profileDetails {
	margin-block-start: var(--size-24);
}

.ProfileEdit-supportText,
.ProfileEdit-services,
.ProfileEdit-formButton {
	margin-block-start: var(--ProfileEdit-spacing);
}

/* MOBILE */
@media (width < 48rem) {
	.ProfileEdit {
		--ProfileEdit-spacing: var(--size-40);
		--ProfileEdit-wrapper-padding: var(--size-16);
	}

	.ProfileEdit-wrapper {
		grid-template-columns: 1fr;
		grid-template-rows: repeat(6, auto);
	}

	.ProfileEdit-header {
		grid-column: 1/2;
	}

	.ProfileEdit-avatarEditor {
		grid-column: 1/2;
		grid-row: 2/3;
	}

	.ProfileEdit-profileDetails {
		grid-column: 1/2;
		grid-row: 3/4;
	}

	.ProfileEdit-languageSelect {
		grid-column: 1/2;
		grid-row: 4/5;
		margin-block-start: var(--ProfileEdit-spacing);
	}

	.ProfileEdit-services {
		grid-column: 1/2;
		grid-row: 6/7;
	}

	.ProfileEdit-formButton {
		grid-column: 1/2;
		grid-row: 5/6;
	}
}

/* DESKTOP */
@media (width >= 48rem) {
	.ProfileEdit {
		--ProfileEdit-spacing: var(--size-48);
		--ProfileEdit-wrapper-padding: var(--size-40);
	}

	.ProfileEdit-wrapper {
		grid-template-columns: 2fr 3fr;
		grid-template-rows: repeat(4, auto);
	}

	.ProfileEdit-header {
		grid-column: 1/3;
	}

	.ProfileEdit-formFields {
		display: grid;
		grid-column: 1/3;
		grid-row: 2/3;
		grid-template-columns: 2fr 3fr;
	}

	.ProfileEdit-profileDetails {
		grid-column: 1/2;
		grid-row: 3/5;
	}

	.ProfileEdit-services {
		grid-column: 2/3;
		grid-row: 4/5;
	}

	.ProfileEdit-formButton {
		grid-column: 2/3;
		grid-row: 3/4;
		justify-self: end;
	}
}
