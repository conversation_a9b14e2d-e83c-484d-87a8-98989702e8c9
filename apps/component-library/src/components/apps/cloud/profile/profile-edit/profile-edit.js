import { doFetch } from "../../../../../js/utils/helper/fetch.js";
import {
	clearMessageStore,
	getLocalizedMessage,
	initMessageStore,
} from "../../../../../js/utils/helper/i18n.js";

export default class ProfileEditForm extends HTMLElement {
	static #selectors = {
		form: ".ProfileEdit-form",
		messageStoreJSON: "#cloud-message-store",
		submitButton: ".ProfileEdit-formButton",
		toast: "#profile-edit-toast",
		userNavigation: "user-navigation",
	};

	#elements;

	#formElements = {
		avatarBase64: "profile-img-base64",
		fileName: "profile-img-filename",
		preferredLanguage: "profile-edit-language-select",
	};

	#state = {
		formProcessing: false,
		initialPreferredLanguage: "",
		profileImageUpdated: false,
	};

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			form: /** @type {HTMLFormElement} */ (
				this.querySelector(ProfileEditForm.#selectors.form)
			),
			messageStoreJSON: /** @type {HTMLScriptElement} */ (
				this.querySelector(ProfileEditForm.#selectors.messageStoreJSON)
			),
			submitButton: /** @type {HTMLButtonElement} */ (
				this.querySelector(ProfileEditForm.#selectors.submitButton)
			),
			toast: /** @type {HTMLElement} */ (
				this.querySelector(ProfileEditForm.#selectors.toast)
			),
			userNavigation: /** @type {HTMLElement} */ (
				document.querySelector(ProfileEditForm.#selectors.userNavigation)
			),
		};
	}

	/**
	 * An instance of the ProfileEditForm component.
	 * @returns {void}
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		const { form, messageStoreJSON } = this.#elements;
		if (form && messageStoreJSON) {
			this.#init();
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#init() {
		const { form, messageStoreJSON } = this.#elements;
		const formData = new FormData(form);

		this.#state.initialPreferredLanguage = formData.get(
			this.#formElements.preferredLanguage,
		);

		initMessageStore(JSON.parse(messageStoreJSON.textContent));

		this.#addEventListerners();
	}

	/**
	 * Toggles the form processing state. This also handles the loading state of the submit button.
	 * @private
	 * @returns {void}
	 */
	#toggleFormProcessingState() {
		const { submitButton } = this.#elements;

		this.#state.formProcessing = !this.#state.formProcessing;
		submitButton.classList.toggle("Button--loading");
		submitButton.disabled = this.#state.formProcessing;
	}

	/**
	 * Handles the profile edit form submission.
	 * @param {HTMLFormElement} form - The form element being submitted.
	 * @private
	 * @async
	 * @returns {void}
	 */
	async #updateProfile(form) {
		const { toast } = this.#elements;

		const formData = new FormData(form);
		const { avatarBase64, fileName, preferredLanguage } = this.#formElements;
		const profileImgBase64 = formData.get(avatarBase64);
		const selectedLanguage = formData.get(preferredLanguage);
		const payload = {
			AvatarBase64: profileImgBase64,
			FileName: formData.get(fileName),
			LanguageCode: selectedLanguage,
		};

		try {
			const jsonResponse = await doFetch(payload, form.action);

			const didPreferredLanguageChange =
				this.#state.initialPreferredLanguage !== selectedLanguage;
			const { message, profile_img_base64, success } = jsonResponse;

			const { userNavigation } = this.#elements;
			if (success && this.#state.profileImageUpdated && userNavigation) {
				// this will notify the user navigation component to update the avatar
				// using the provided base64 image.
				userNavigation.dispatchEvent(
					new CustomEvent("UpdateAvatar", {
						detail: {
							avatarBase64: profile_img_base64,
						},
					}),
				);
			}

			if (success && didPreferredLanguageChange) {
				if (toast) {
					toast.setAttribute("toast-role", "alert");
					toast.showToast(getLocalizedMessage("profile_edit.message.lang"));
				}

				// Because we are switching to a new language, we need to ensure that
				// we clear the message store so that all new strings will be
				// fetched in the new language.
				clearMessageStore();

				setTimeout(() => {
					window.location.href = `${window.location.origin}/profile`;
				}, 2000);
			} else if (success && !didPreferredLanguageChange) {
				toast?.showToast(message);
			} else {
				toast?.setToastStyle("critical").showToast(message);
			}
		} catch {
			toast
				?.setToastStyle("critical")
				.showToast(getLocalizedMessage("global.error.generic"));
		} finally {
			this.#toggleFormProcessingState();
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListerners() {
		const { form, toast } = this.#elements;

		document.addEventListener("ProfileImageUpdated", () => {
			this.#state.profileImageUpdated = true;
		});

		form.addEventListener("submit", async (event) => {
			event.preventDefault();

			if (toast) {
				// close any toasts opened during the previous
				// form submission.
				toast.closeToast();
			}

			this.#toggleFormProcessingState();
			this.#updateProfile(form);
		});
	}
}

customElements.define("profile-edit-form", ProfileEditForm);
