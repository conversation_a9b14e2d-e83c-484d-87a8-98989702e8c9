{{ attach_library('finstral_global/apps-profile-edit') }}

{% set message_store = {
	"global.error.generic": "global.error.generic"|tc,
	"profile_edit.message.lang": "profile_edit.message.lang"|tc,
} %}

<section class="ProfileEdit u-container">
	<div class="ProfileEdit-wrapper">
		<header class="ProfileEdit-header">
			<h1 class="u-typo-HeadlineM" id="profile-edit-section-title">{{ "profile_edit.page_title"|tc }}</h1>
		</header>

		<profile-edit-form>
			<form aria-labelledby="profile-edit-section-title" action="{{ form_action }}" class="ProfileEdit-form" method="post">
				<div class="ProfileEdit-formFields">
					<div class="ProfileEdit-avatarEditor">
						{% include "@apps/cloud/profile/profile-edit/profile-image-editor/profile-image-editor.twig" with {
							profile_image: profile_image,
						}	only %}
					</div>

					<div class="ProfileEdit-languageSelect">
						{% include "@elements/form-element/label/label.twig" with {
							for: "profile-edit-language-select",
							title: "profile_edit.language_select.label"|tc,
						} only %}

						{% include "@elements/select/select.twig" with {
							id: "profile-edit-language-select",
							name: "profile-edit-language-select",
							options: language_select_options,
						} only %}

						<div class="ProfileEdit-supportText">
							<p class="ProfileEdit-supportText u-typo-TextL">
								{{ "profile_edit.change_contact_details_note"|tc({"@support_email": support_email}) -}}
							</p>
						</div>
					</div>
				</div>

				{% include "@elements/button/button.twig" with {
					classes: ["ProfileEdit-formButton"],
					label: "profile_edit.form_button"|tc,
					type: "submit",
				} only %}
			</form>
			{% include "@elements/toast/toast.twig" with {
				id: "profile-edit-toast",
				toast_role: "status",
				dismissible_by: ["dismissable-user"]
			} only %}

			<script id="cloud-message-store" type="application/json">{{ message_store|json_encode()|raw }}</script>
		</profile-edit-form>

		<div class="ProfileEdit-profileDetails">
			{% include "@elements/card-user-profile/card-user-profile.twig" with {
				email: user_profile_details.email,
				first_name: user_profile_details.first_name,
				last_name: user_profile_details.last_name,
				organisation: user_profile_details.organisation,
				phone: user_profile_details.phone,
				card_style: "full",
			}	only %}
		</div>

		<div class="ProfileEdit-services">
			{% include "@apps/cloud/profile/profile-edit/services/services.twig" with {
				services: services,
			} only %}
		</div>
	</div>
</section>
