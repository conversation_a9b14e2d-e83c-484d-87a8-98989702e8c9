/** @define ProfileImageEditor; */

.ProfileImageEditor,
.ProfileImageEditor-container {
	position: relative;
}

.ProfileImageEditor-avatar {
	block-size: var(--ProfileImageEditor-avatar-size);
	border-radius: 50%;
	inline-size: var(--ProfileImageEditor-avatar-size);
	object-fit: cover;
}

.ProfileImageEditor-button {
	background-color: var(--shared-color-surface-brand);
	block-size: var(--ProfileImageEditor-button-size);
	border-radius: 50%;
	color: var(--shared-color-text-invert);
	inline-size: var(--ProfileImageEditor-button-size);
	inset-block-end: 0;
	inset-inline-start: var(--ProfileImageEditor-button-inline-position);
	padding: var(--size-4);
	position: absolute;
}

.ProfileImageEditor-icon {
	block-size: var(--ProfileImageEditor-button-icon-size);
	inline-size: var(--ProfileImageEditor-button-icon-size);
}

@media (width < 63rem) {
	.ProfileImageEditor {
		--ProfileImageEditor-avatar-size: var(--size-96);
		--ProfileImageEditor-button-size: var(--size-24);
		--ProfileImageEditor-button-icon-size: var(--size-16);
		--ProfileImageEditor-button-inline-position: 4.5rem;
	}
}

@media (width >= 63rem) {
	.ProfileImageEditor {
		--ProfileImageEditor-avatar-size: var(--size-128);
		--ProfileImageEditor-button-size: var(--size-32);
		--ProfileImageEditor-button-icon-size: var(--size-24);
		--ProfileImageEditor-button-inline-position: 6rem;
	}
}
