export default class ProfileImageEditor extends HTMLElement {
	static #selectors = {
		fileInput: "input[type='file']",
		filenameInputHidden: "#profile-img-filename",
		base64InputHidden: "#profile-img-base64",
		profileImageEditor: ".ProfileImageEditor-container",
		profileImageEditorFallback: ".ProfileImageEditor-fallback",
		triggerButton: "button[type='button']",
	};

	#elements;

	/**
	 * An instance of the ProfileImageEditor constructor.
	 * @returns {void}
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		if (this.#elements.profileImageEditorFallback) {
			this.#init();
		}
	}

	/**
	 * Retrieves the elements needed for the profile image editor.
	 * @private
	 * @returns {object} An object containing references to the required elements.
	 */
	#getElements() {
		return {
			fileInput: /** @type {HTMLInputElement} */ this.querySelector(
				ProfileImageEditor.#selectors.fileInput,
			),
			filenameInputHidden: /** @type {HTMLInputElement} */ this.querySelector(
				ProfileImageEditor.#selectors.filenameInputHidden,
			),
			base64InputHidden: /** @type {HTMLInputElement} */ this.querySelector(
				ProfileImageEditor.#selectors.base64InputHidden,
			),
			profileImageEditor: /** @type {HTMLDivElement} */ this.querySelector(
				ProfileImageEditor.#selectors.profileImageEditor,
			),
			profileImageEditorFallback:
				/** @type {HTMLDivElement} */ this.querySelector(
					ProfileImageEditor.#selectors.profileImageEditorFallback,
				),
			triggerButton: /** @type {HTMLButtonElement} */ this.querySelector(
				ProfileImageEditor.#selectors.triggerButton,
			),
		};
	}

	/**
	 * Initializes the component by hidding the fallback and showing the JS driven UI.
	 * @private
	 * @returns {void}
	 */
	#init() {
		this.#elements.profileImageEditorFallback.setAttribute("hidden", "");
		this.#elements.profileImageEditor.removeAttribute("hidden");

		if (this.#elements.fileInput) {
			this.#addEventListeners();
		}
	}

	/**
	 * Updates the avatar image based on the selected file. It also sets the hidden input value
	 * to the base64 encoded image, and another with the filename. It then dispatches a custom
	 * event to notify other components.
	 * @param {object} file - The selected File object
	 * @see https://developer.mozilla.org/en-US/docs/Web/API/File
	 * @private
	 * @returns {void}
	 */
	#updateAvatarState(file) {
		const reader = new FileReader();

		const { base64InputHidden, filenameInputHidden, profileImageEditor } =
			this.#elements;

		reader.addEventListener("load", (event) => {
			const avatar = profileImageEditor.querySelector("img");
			avatar.src = event.target.result;

			if (base64InputHidden && filenameInputHidden) {
				base64InputHidden.value = event.target.result;
				filenameInputHidden.value = file.name;
			}

			document.dispatchEvent(new Event("ProfileImageUpdated"));
		});
		reader.readAsDataURL(file);
	}

	/**
	 * Adds event listener to the file input element and calls the #updateAvatarState
	 * when a new image is selected.
	 * @private
	 * @returns {void}
	 */
	#addEventListeners() {
		const { fileInput, triggerButton } = this.#elements;

		triggerButton.addEventListener("click", () => {
			fileInput.click();
		});

		fileInput.addEventListener("change", () => {
			const file = fileInput.files[0];
			if (file) {
				this.#updateAvatarState(file);
			}
		});
	}
}

customElements.define("profile-image-editor", ProfileImageEditor);
