{{ attach_library('finstral_global/apps-profile-edit-profile-image-editor') }}

{% set input_id = "profile-image-editor-input-" ~ random() %}
{% set label_id = "profile-image-editor-label-" ~ random() %}

{% set elements_image_avatar %}
  {% include "@elements/image/image.twig" with {
    alt: profile_image.alt,
    classes: ["ProfileImageEditor-avatar"],
    height: 200,
    uri: profile_image.uri,
    width: 200,
  } only %}
{% endset %}

<profile-image-editor class="ProfileImageEditor">
	<div class="ProfileImageEditor-container" hidden>
		{{ elements_image_avatar }}
		<button aria-labelledby="{{ label_id }}" class="ProfileImageEditor-button" type="button">
			{%- include "@elements/icon/icon.twig" with {
				classes: ["ProfileImageEditor-icon"],
				name: "edit"
			} only -%}
		</button>
		<input id="profile-img-base64" name="profile-img-base64" type="hidden" value="">
		<input id="profile-img-filename" name="profile-img-filename" type="hidden" value="">
	</div>

	<div class="ProfileImageEditor-fallback">
		{{ elements_image_avatar }}
		<label class="visually-hidden" id="{{ label_id }}" for="{{ input_id }}">{{ "profile_image_editor.input_label"|tc }}</label>
		<input accept="{{ accepted_formats|default('image/*') }}" id="{{ input_id }}" name="profile-image-edit-avatar" type="file">
	</div>

</profile-image-editor>
