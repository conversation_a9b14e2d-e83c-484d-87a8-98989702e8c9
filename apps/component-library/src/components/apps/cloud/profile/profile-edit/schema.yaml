$schema: http://json-schema.org/draft-07/schema

type: object

required:
  - form_action
  - language_select_options
  - profile_image
  - services
  - support_email
  - user_profile_details

additionalProperties: false

properties:
  form_action:
    type: string

  language_select_options:
    $ref: "https://finstral.com/apps/cloud/profile#LanguageSelectOptionsSchema"

  profile_image:
    $ref: "https://finstral.com/apps/cloud/profile#ProfileImageSchema"

  services:
    $ref: "https://finstral.com/apps/cloud/profile#ServicesSchema"

  support_email:
    type: string

  user_profile_details:
    $ref: "https://finstral.com/apps/cloud/profile#UserProfileDetailsSchema"
