/** @define ProfileEditServices; */

.ProfileEditServices {
	display: grid;
	gap: var(--ProfileEditServices-gap, var(--size-16));
	grid-template-columns: var(--ProfileEditServices-grid-template-columns);
	justify-content: space-between;
}

@media (width < 48rem) {
	.ProfileEditServices {
		--ProfileEditServices-gap: var(--size-8);
	}

	.ProfileEditServices {
		--ProfileEditServices-grid-template-columns: 1fr;
	}
}

@media (width >= 48rem) {
	.ProfileEditServices {
		--ProfileEditServices-grid-template-columns: repeat(
			auto-fill,
			minmax(12rem, 1fr)
		);
	}
}

.ProfileEditServices-title {
	margin-block-end: var(--size-24);
}

.ProfileEditServices-item {
	border: var(--border-distinct);
	display: flex;
	flex: 1 1 auto;
	flex-direction: column;
	gap: var(--size-24);
	padding: var(--size-16);
}

.ProfileEditServices-status {
	display: flex;
	gap: var(--size-8);
}

.ProfileEditServices-icon--active {
	color: var(--shared-color-text-success);
}

.ProfileEditServices-icon--inactive {
	color: var(--shared-color-text-secondary);
}
