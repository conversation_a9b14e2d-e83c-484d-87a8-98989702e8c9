<h2 class="u-typo-HeadlineS ProfileEditServices-title">{{ "profile_edit.services.title"|tc }}</h2>

<ul class="ProfileEditServices">
	{% for item in services %}
		<li class="ProfileEditServices-item">
			<h3 class="u-typo-HeadlineS">{{ item.service }}</h3>
			<span class="ProfileEditServices-status u-typo-TextS">
				{% include "@elements/icon/icon.twig" with {
					classes: ['ProfileEditServices-icon--' ~ item.status],
					name: item.status == "active" ? "check_circle_filled" : "delete_filled",
					size: "small"
				} only %}
				{{ ("profile_edit_services.status." ~ item.status)|tc }}
			</span>
		</li>
	{% endfor %}
</ul>
