$schema: http://json-schema.org/draft-07/schema
$id: https://finstral.com/apps/cloud/profile

$def:
  language_select_options:
    $anchor: LanguageSelectOptionsSchema

    type: array
    items:
      type: object

      required:
        - label
        - type
        - value

      additionalProperties: false

      properties:
        label:
          type: string

        type:
          type: string
          enum:
            - option
            - optgroup

        value:
          type: string

        selected:
          type: boolean

  profile_image:
    $anchor: ProfileImageSchema

    type: object
    required:
      - alt
      - uri

    additionalProperties: false
    properties:
      alt:
        type: string

      uri:
        type: string
        format: uri-reference

  services:
    $anchor: ServicesSchema

    type: array
    items:
      type: object
      required:
        - service
        - status

      additionalProperties: false
      properties:
        service:
          type: string

        status:
          type: string
          enum:
            - active
            - inactive

  user_profile_details:
    $anchor: UserProfileDetailsSchema

    type: object
    required:
      - first_name
      - last_name

    additionalProperties: false
    properties:
      email:
        type: string

      first_name:
        type: string

      last_name:
        type: string

      organisation:
        type: string

      phone:
        type: string

type: object

required:
  - breadcrumb
  - form_action
  - language_select_options
  - profile_image
  - services
  - support_email
  - user_profile_details

additionalProperties: false

properties:
  breadcrumb:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - text
      properties:
        text:
          type: string
        url:
          type: string
          format: uri-reference

  form_action:
    type: string

  language_select_options:
    $ref: "#LanguageSelectOptionsSchema"

  profile_image:
    $ref: "#ProfileImageSchema"

  services:
    $ref: "#ServicesSchema"

  support_email:
    type: string

  user_profile_details:
    $ref: "#UserProfileDetailsSchema"
