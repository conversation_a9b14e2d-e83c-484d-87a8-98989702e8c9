{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "/contact-support/contact-support-payload", "title": "Contact support form payload", "description": "The payload for the contact support form.", "type": "object", "properties": {"SelectedTopic": {"type": "string", "description": "The selected topic."}, "SelectedCategory": {"type": "string", "description": "The selected category for the topic."}, "Subject": {"type": "string", "description": "The support message subject."}, "Message": {"type": "string", "description": "The support message."}, "Files": {"type": "array", "description": "The attached file ids provided by DropZone.", "items": {"type": "object", "name": {"type": "string", "description": "The file name."}, "base64": {"type": "string", "description": "The base64 encoded file."}}}}}