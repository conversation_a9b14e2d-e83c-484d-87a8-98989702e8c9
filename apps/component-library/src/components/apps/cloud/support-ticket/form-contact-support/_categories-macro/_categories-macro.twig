{% macro categories_fieldset(categories, topic, visible) %}
<fieldset class="ContactSupport-fieldset" data-topic="{{ topic.label|lower }}_fieldset" {% if not visible %} hidden {%- endif -%}
>
	<legend class="ContactSupport-legend">{{ "form_contact_support.fieldset.categories"|tc({
		"@topic": topic.label,
	}) }}</legend>
	<ul class="ContactSupport-options">
		{% for category in categories %}
		{% set category_id = [topic.label, category.name, random()]|join('-') %}
			<li>
				<div class="ContactSupport-option Radio Option">
					{% include "@elements/form-element/option/option.twig" with {
						classes: ["ContactSupport-radio"],
						checked: category.checked,
						id: category_id|lower,
						name: category.name,
						type: "radio",
						value: category.value,
					} only %}
					{% include "@elements/form-element/label/label.twig" with {
						for: category_id|lower,
						title: category.label,
					} only %}
				</div>
			</li>
		{% endfor %}
	</ul>
</fieldset>
{% endmacro %}
