/* stylelint-disable plugin/selector-bem-pattern */

/** @define ContactSupport; */

.ContactSupport-wrapper {
	background-color: var(--shared-color-surface-primary);
	border: var(--border-subtle);
	padding: var(--ContactSupport-wrapper-padding);
}

@media (width < 48rem) {
	.ContactSupport {
		--ContactSupport-wrapper-padding: var(--size-16);
	}

	.ContactSupport-submitButton {
		inline-size: 100%;
	}
}

@media (width >= 48rem) {
	.ContactSupport {
		--ContactSupport-wrapper-padding: var(--size-40);
	}
}

.ContactSupport-formComponent {
	display: block;
}

.ContactSupport-form {
	margin-block-start: var(--size-48);
}

.ContactSupport-fieldset {
	border: 0;
	margin-block-end: var(--ContactSupport-fieldset-spacing, var(--size-48));
	padding: 0;
}

.ContactSupport-fieldGroup {
	margin-block-end: var(--ContactSupport-fieldset-spacing, var(--size-48));

	/** @TODO: This does not work for the textarea. We need something else to solve this challenge */
	input {
		scroll-margin-block-start: calc(
			var(--header-min-block-size) + 2rem
		); /* 1rem is the gap between the header and the form validation errors */
	}
}

.ContactSupport-fieldGroup:has([aria-invalid="true"]) {
	color: var(--shared-color-text-critical);
}

.ContactSupport-fieldGroupError {
	display: none;
}

.ContactSupport-fieldGroup:has([aria-invalid="true"])
	.ContactSupport-fieldGroupError {
	align-items: center;
	display: flex;
	font: var(--typo-TextM);
	gap: var(--size-4);
	margin-block-start: var(--size-8);

	svg {
		margin-block-start: -0.25rem; /* Optically align icon and copy */
	}
}

@media (width >= 48rem) {
	.ContactSupport {
		--ContactSupport-fieldset-spacing: var(--size-64);
	}

	.ContactSupport-fieldset:last-of-type {
		--ContactSupport-fieldset-spacing: var(--size-48);
	}
}

.ContactSupport-legend {
	margin-block-end: var(--size-32);
}

.ContactSupport-options,
.ContactSupport-option {
	display: flex;
}

.ContactSupport-option {
	align-items: baseline;
}

.ContactSupport-radio {
	margin: 0;
}

@media (width < 48rem) {
	.ContactSupport-options {
		flex-direction: column;
		gap: var(--size-32);
	}

	.ContactSupport-option {
		gap: var(--size-8);
	}
}

@media (width >= 48rem) {
	.ContactSupport-options {
		flex-direction: row;
		gap: var(--size-48);
	}

	.ContactSupport-option {
		gap: var(--size-12);
	}
}

.ContactSupport-filesLabel {
	font-size: var(--typo-HeadlineS-font-size);
}
