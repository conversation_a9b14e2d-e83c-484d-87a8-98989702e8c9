import { doFetch } from "../../../../../js/utils/helper/fetch.js";
import { getFilesAsBase64 } from "../../../../../js/utils/helper/helper.js";
import {
	getLocalizedMessage,
	initMessageStore,
} from "../../../../../js/utils/helper/i18n.js";

export default class FormContactSupport extends HTMLElement {
	static #selectors = {
		fileDrop: "finstral-filedrop",
		formContactSupport: ".ContactSupport-form",
		formValidationErrors: "finstral-form-validation-errors",
		messageStoreJSON: "#cloud-message-store",
		supportCategoriesContainer: ".ContactSupport-categories",
		supportTopicsContainer: "#support-topics",
		toast: "#contact-support-toast",
	};

	#elements;

	#formFieldNames = {
		description: "js-contact-support-description",
		files: "js-contact-support-files",
		subject: "js-contact-support-subject",
		topics: "js-contact-support-topics",
	};

	#state = {
		fieldErrors: [],
		formData: {},
		isEmployee: false,
		selectedTopicLabel: "",
	};

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			fileDrop: /** @type {HTMLElement} */ (
				this.querySelector(FormContactSupport.#selectors.fileDrop)
			),
			formContactSupport: /** @type {HTMLFormElement} */ (
				this.querySelector(FormContactSupport.#selectors.formContactSupport)
			),
			formValidationErrors: /** @type {HTMLElement} */ (
				this.querySelector(FormContactSupport.#selectors.formValidationErrors)
			),
			messageStoreJSON: /** @type {HTMLScriptElement} */ (
				this.querySelector(FormContactSupport.#selectors.messageStoreJSON)
			),
			supportCategoriesContainer: /** @type {HTMLDivElement} */ (
				this.querySelector(
					FormContactSupport.#selectors.supportCategoriesContainer,
				)
			),
			supportTopicsContainer: /** @type {HTMLUListElement} */ (
				this.querySelector(FormContactSupport.#selectors.supportTopicsContainer)
			),
			toast: this.querySelector(FormContactSupport.#selectors.toast),
		};
	}

	/**
	 * Represents the FormContactSupport class.
	 * @class
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		this.#state.isEmployee = this.getAttribute("is-employee") === "1"; // PHP return 1 for true

		const {
			formContactSupport,
			messageStoreJSON,
			supportCategoriesContainer,
			supportTopicsContainer,
		} = this.#elements;
		if (
			formContactSupport &&
			messageStoreJSON &&
			supportCategoriesContainer &&
			supportTopicsContainer
		) {
			this.#init();
		}
	}

	/**
	 * Initializes the form by setting it to novalidate and adding event listeners.
	 * @private
	 * @returns {void}
	 */
	#init() {
		const { messageStoreJSON, supportTopicsContainer } = this.#elements;

		this.#state.selectedTopicLabel =
			supportTopicsContainer.querySelector("input:checked").dataset.topicLabel;

		initMessageStore(JSON.parse(messageStoreJSON.textContent));

		this.#addEventListeners();
	}

	/**
	 * Shows the category fieldset with the specified ID and hide all other category fieldsets.
	 * @returns {void}
	 * @private
	 */
	#showCategory() {
		const { supportCategoriesContainer } = this.#elements;
		const nextCategory = supportCategoriesContainer.querySelector(
			`fieldset[data-topic="${this.#state.selectedTopicLabel}_fieldset"]`,
		);
		const visibleFieldset = supportCategoriesContainer.querySelector(
			"fieldset:not([hidden])",
		);
		visibleFieldset.hidden = true;

		const nextCategoryOptions = nextCategory.querySelectorAll("[type=radio]");
		const hasSelectedOption = Array.from(nextCategoryOptions).some(
			(options) => options.checked,
		);

		// When displaying a new category, check the first option if no option is selected.
		if (!hasSelectedOption) {
			nextCategoryOptions[0].checked = true;
		}

		nextCategory.hidden = false;
	}

	/**
	 * Updates the form error state by setting the aria-invalid attribute on the
	 * specified field and adding the error message to error container.
	 * @param {HTMLElement} field - The form field element that has the error.
	 * @param {string} message - The error message.
	 * @private
	 * @returns {void}
	 */
	#updateFormErrorState(field, message) {
		// Get the text of the label which will be translated.
		// Remove the asterisk and trim the whitespace.
		const fieldName = this.querySelector(`[for=${field.id}]`)
			.textContent.replace("*", "")
			.trim();

		field.setAttribute("aria-invalid", true);
		field.nextElementSibling.querySelector("span").textContent =
			message.replace("@field_name", fieldName);

		this.#state.fieldErrors.push({
			fieldName,
			id: field.id,
			message,
		});
	}

	/**
	 * Validates the form data.
	 * @private
	 * @returns {boolean} Returns false if the form is invalid.
	 */
	#isValid() {
		const { formValidationErrors } = this.#elements;
		const fieldRequired = getLocalizedMessage(
			"global.form.errors.js.field.required",
		);
		const fieldsToValidate = ["description", "subject"];

		this.#state.fieldErrors = [];

		let fields = {};
		Object.keys(this.#formFieldNames).forEach((key) => {
			fields[key] = this.querySelector(`[name=${this.#formFieldNames[key]}]`);
		});

		for (const field in fields) {
			if (fieldsToValidate.includes(field)) {
				fields[field].setAttribute("aria-invalid", false);
				fields[field].nextElementSibling.querySelector("span").textContent = "";
			}
		}

		const { description, subject } = fields;

		if (!subject.checkValidity()) {
			this.#updateFormErrorState(subject, fieldRequired);
		}

		if (!description.checkValidity()) {
			this.#updateFormErrorState(description, fieldRequired);
		}

		// Always send field errors as an empty array will cause the
		// formValidationErrors component to clear the error messages
		// and unmount the output container.
		formValidationErrors.setValidationErrors({
			errors: this.#state.fieldErrors,
		});

		return this.#state.fieldErrors.length ? false : true;
	}

	/**
	 * Resets the form state and UI elements.
	 * @param {HTMLFormElement} form - The form element to reset.
	 * @private
	 * @returns {void}
	 */
	#resetState(form) {
		const { fileDrop, supportTopicsContainer } = this.#elements;
		const supportTopics =
			supportTopicsContainer.querySelectorAll("[type=radio]");

		form.reset();
		fileDrop.reset();

		supportTopics[0].checked = true;

		this.#state = {
			fieldErrors: [],
			formData: {},
			selectedTopicLabel: supportTopics[0].dataset.topicLabel,
		};

		this.#showCategory();
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setFormData() {
		const { formContactSupport } = this.#elements;
		const formData = new FormData(formContactSupport);
		const { description, files, topics, subject } = this.#formFieldNames;

		this.#state.formData = {
			description: formData.get(description),
			files: Array.from(
				formContactSupport.querySelector(`[name=${files}]`).files,
			),
			selectedTopic: formData.get(topics),
			selectedCategory: formData.get(
				`${this.#state.selectedTopicLabel}-categories`,
			),
			subject: formData.get(subject),
		};
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	async #submitSupportTicket() {
		const { formContactSupport, fileDrop, toast } = this.#elements;
		const { formData, isEmployee } = this.#state;
		const submitButton = formContactSupport.querySelector("[type=submit]");

		submitButton.classList.add("Button--loading");
		submitButton.disabled = true;

		const payload = {
			SelectedTopic: formData.selectedTopic,
			SelectedCategory: formData.selectedCategory,
			Subject: formData.subject,
			Message: formData.description,
		};

		try {
			if (fileDrop.containsFiles()) {
				payload.Files = await getFilesAsBase64(fileDrop.getFiles());
			}
			const jsonResponse = await doFetch(payload, formContactSupport.action);

			const { attachments, message, ticket_id, ticketUrl, success } =
				jsonResponse;

			if (success) {
				const ticketReferenceMsg = getLocalizedMessage(
					"global.ticket.reference",
				);
				const viewTicketMsg = getLocalizedMessage("global.ticket.view");
				let toastMessage = attachments
					? message
					: getLocalizedMessage("general.error.attachments");
				toastMessage = isEmployee
					? `${toastMessage} ${ticketReferenceMsg.replace("@ticket_id", ticket_id)}`
					: `${toastMessage} <a href="${ticketUrl}">${viewTicketMsg}</a>`;

				this.#resetState(formContactSupport);
				toast?.showToast(toastMessage, /** _dangerouslySetInnerHTML */ true);
			} else {
				toast?.setToastStyle("critical").showToast(message);
			}
		} catch (error) {
			toast
				?.setToastStyle("critical")
				.showToast(getLocalizedMessage("global.error.general"));
			throw new Error(error);
		} finally {
			submitButton.classList.remove("Button--loading");
			submitButton.disabled = false;
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListeners() {
		const { formContactSupport, supportTopicsContainer, toast } =
			this.#elements;

		supportTopicsContainer.addEventListener("change", (event) => {
			const selectedTopic = event.target;

			this.#state.formData.selectedTopic = selectedTopic.dataset.topic;
			this.#state.selectedTopicLabel = selectedTopic.dataset.topicLabel;

			this.#showCategory();
		});

		formContactSupport.addEventListener("submit", (event) => {
			event.preventDefault();

			if (toast) {
				toast.closeToast();
			}

			if (!this.#isValid()) {
				return;
			}

			this.#setFormData();

			this.#submitSupportTicket();
		});
	}
}

customElements.define("form-contact-support", FormContactSupport);
