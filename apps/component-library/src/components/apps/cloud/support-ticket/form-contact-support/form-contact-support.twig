{{ attach_library('finstral_global/apps-form-contact-support') }}

{% set message_store = {
	"general.error.attachments": "general.error.attachments"|tc,
	"global.error.general": "global.error.general"|tc,
	"global.ticket.reference": "global.ticket.reference"|tc,
	"global.ticket.view": "global.ticket.view"|tc,
	"global.form.errors.js.field.required": "global.form.errors.js.field.required"|tc,
} %}

{% import "@apps/cloud/support-ticket/form-contact-support/_categories-macro/_categories-macro.twig" as categories %}

<section class="u-container ContactSupport">
	<div class="ContactSupport-wrapper">
		<header>
			{% include "@elements/page-title/page-title.twig" with {
				heading_id: "contact-suppport-page-title",
				help_text: "global.required_fields_note"|tc,
				sub_text: "form_contact_support.sub_text"|tc,
				title: "form_contact_support.page_title"|tc,
				title_size: "HeadlineM"
			} only %}
		</header>

		<form-contact-support class="ContactSupport-formComponent"
			is-employee="{{ is_employee }}"
		>
			<form aria-labelledby="contact-suppport-page-title" class="ContactSupport-form u-typo-TextM" action="{{ form_action }}" method="post" novalidate>

				{% include "@elements/form-validation-errors/form-validation-errors.twig" %}

				<fieldset class="ContactSupport-fieldset">
					<legend class="ContactSupport-legend">{{ "form_contact_support.fieldset.topics"|tc }}</legend>
					<ul class="ContactSupport-options" id="support-topics">
						{% for topic in topics %}
						{% set topic_id = topic.label ~ random() %}
						{% set data_topic = topic.label|lower %}
							<li class="">
								<div class="ContactSupport-option Radio Option">
									{% include "@elements/form-element/option/option.twig" with {
										additional_attributes: [
											["data-topic", data_topic ~ "_fieldset"],
											["data-topic-label", data_topic],
										],
										classes: ["ContactSupport-radio"],
										checked: topic.checked,
										id: topic_id|lower,
										name: "js-contact-support-topics",
										type: "radio",
										value: topic.value,
									} only %}
									{% include "@elements/form-element/label/label.twig" with {
										for: topic_id|lower,
										title: topic.label,
									} only %}
								</div>
							</li>
						{% endfor %}
					</ul>
				</fieldset>

				<div class="ContactSupport-categories">
					{% for topic in topics %}
						{{ categories.categories_fieldset(topic.categories, topic, loop.first) }}
					{%- endfor -%}
				</div>

				<fieldset class="ContactSupport-fieldset">
					<legend class="visually-hidden">{{ "form_contact_support.fieldset.message"|tc }}</legend>

					<div class="Form-fieldGroup ContactSupport-fieldGroup">
						{% include "@elements/form-element/label/label.twig" with {
							for: "contact-support-subject",
							required: true,
							title: "form_contact_support.subject_label"|tc
						} only %}

						{% include "@elements/form-element/input/input.twig" with {
							additional_attributes: [
								["aria-describedby", "contact-support-subject-error"],
							],
							id: "contact-support-subject",
							name: "js-contact-support-subject",
							required: true,
							type: "text"
						} only %}

						{% include "@elements/form-validation-errors/error-container/error-container.twig" with {
							id: "contact-support-subject-error",
						} only %}
					</div>

					<div class="Form-fieldGroup ContactSupport-fieldGroup">
						{% include "@elements/form-element/label/label.twig" with {
							for: "contact-support-description",
							required: true,
							title: "form_contact_support.description_label"|tc
						} only %}

						{% include "@elements/form-element/textarea/textarea.twig" with {
							additional_attributes: [
								["aria-describedby", "contact-support-description-error"],
							],
							id: "contact-support-description",
							name: "js-contact-support-description",
							required: true,
						} only %}

						{% include "@elements/form-validation-errors/error-container/error-container.twig" with {
							id: "contact-support-description-error",
						} only %}
					</div>

					{% include "@elements/filedrop/filedrop.twig" with {
						attachments_config: attachments_config,
						file_constraints: "form_contact_support.file_constraints_message"|tc,
						field_description: "form_contact_support.file_upload_description"|tc,
						id: "cloud-support-file-uploader",
						label: "form_contact_support.file_upload_label"|tc,
						label_classes: ["ContactSupport-filesLabel"],
						name: "js-contact-support-files",
						toast_id: "contact-support-toast",
					} only %}
				</fieldset>

				{% include "@elements/button/button.twig" with {
					classes: ["ContactSupport-submitButton"],
					label: "form_contact_support.form_button"|tc,
					type: "submit",
				} only %}
			</form>

			{% include "@elements/toast/toast.twig" with {
				id: "contact-support-toast",
				toast_role: "status",
				dismissible_by: ["dismissable-user"]
			} only %}

			<script id="cloud-message-store" type="application/json">{{ message_store|json_encode()|raw }}</script>
		</form-contact-support>
	</div>
</section>
