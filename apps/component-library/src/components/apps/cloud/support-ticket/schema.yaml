$schema: http://json-schema.org/draft-07/schema
$id: https://finstral.com/apps/cloud/support-ticket

$defs:
  attachments_config:
    $anchor: AttachmentsConfigSchema
    accepted_formats:
      type: array
      items:
        type: string

    multiple:
      type: boolean

    max_file_count:
      type: number

    max_filesize_message:
      type: string

    max_filesize:
      additionalProperties: false
      type: object
      properties:
        accumulative:
          type: boolean
        size:
          type: number
        unit:
          type: string
          enum:
            - Bytes
            - KB
            - MB

  topics:
    $anchor: TopicsSchema
    type: array
    items:
      type: object
      additionalProperties: false
      properties:
        categories:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              checked:
                type: boolean
              label:
                type: string
              name:
                type: string
              value:
                type: number
        checked:
          type: boolean
        label:
          type: string
        value:
          type: number

type: object

required:
  - breadcrumb
  - form_action
  - is_employee
  - topics

additionalProperties: false

properties:
  attachments_config:
    $ref: "#AttachmentsConfigSchema"

  breadcrumb:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - text
      properties:
        text:
          type: string
        url:
          type: string
          format: uri-reference

  form_action:
    type: string

  is_employee:
    type: boolean

  topics:
    $ref: "#TopicsSchema"
