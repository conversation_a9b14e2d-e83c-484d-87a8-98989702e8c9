$schema: http://json-schema.org/draft-07/schema
$id: /apps/cloud/ticket-detail/create-comment/response

type: object

required:
  - author
  - create_date_time
  - message_detail
  - success

additionalProperties: false

properties:
  attachments:
    additionalProperties: false
    type: object
    required:
      - success
    properties:
      files:
        type: array
        items:
          additionalProperties: false
          type: object
          required:
            - file_name
            - preview_url
            - source_url
          properties:
            file_name:
              type: string
            preview_url:
              type: string
              format: uri-reference
            source_url:
              type: string
              format: uri-reference
      success:
        type: boolean

  author:
    type: string

  create_date_time:
    type: string

  is_author:
    type: boolean

  message_detail:
    format: html
    type: string

  success:
    type: boolean
