/* stylelint-disable plugin/selector-bem-pattern */
@import url("./attachments-list/attachments-list.css");
@import url("./comment-thread/comment-thread.css");

/** @define TicketDetail; */

.TicketDetail {
	display: block;
}

.TicketDetail-pageTitle {
	margin-block-end: var(--TicketDetail-pageTitle-margin-block-end);
}

.TicketDetail-descriptionTitle {
	margin-block-end: var(--size-8);
}

.TicketDetail-meta {
	align-items: center;
	color: var(--shared-color-text-secondary);
	display: flex;
	flex-wrap: wrap;
	font-size: var(--typo-TextS-font-size);
	gap: var(--TicketDetail-meta-gap);
}

.TicketDetail-meta > li:first-child {
	align-items: center;
	display: flex;
}

.TicketDetail-meta > li:first-child::after {
	content: "|";
	display: block;
	margin-inline-start: var(--TicketDetail-meta-gap);
}

.TicketDetail-reporter,
.TicketDetail-createDate {
	display: inline-block;
}

.TicketDetail-message {
	background-color: var(--shared-color-surface-primary);
	border: var(--border-subtle);
	margin-block: var(--TicketDetail-primary-margin-block);

	pre {
		padding: var(--size-16);
		white-space: break-spaces;
	}
}

/** Comments form */
.TicketDetail-commentForm,
.TicketDetail-commentForm textarea {
	margin-block: var(--TicketDetail-primary-margin-block);
}

.TicketDetail-commentForm form {
	margin-block-start: var(--size-8);
}

.TicketDetail-commentFormLabel {
	color: var(--shared-color-text-secondary);
}

.TicketDetail-commentFormAttachments {
	margin-block: var(--size-16);
}

/** Comments thread */
.TicketDetail-commentThread {
	border-block-start: var(--border-subtle-transparent);
	padding-block-end: var(--size-64);
	padding-block-start: var(--size-32);
}

@media (width < 48rem) {
	.TicketDetail {
		--TicketDetail-pageTitle-margin-block-end: var(--size-24);
		--TicketDetail-meta-gap: var(--size-8);
		--TicketDetail-primary-margin-block: var(--size-24);
	}

	.TicketDetailComment-button {
		inline-size: 100%;
	}
}

@media (width >= 48rem) {
	.TicketDetail {
		--TicketDetail-pageTitle-margin-block-end: var(--size-32);
		--TicketDetail-meta-gap: var(--size-16);
		--TicketDetail-primary-margin-block: var(--size-32);
	}
}
