import { doFetch } from "../../../../../js/utils/helper/fetch.js";
import { getFilesAsBase64 } from "../../../../../js/utils/helper/helper.js";
import { getLocalizedMessage } from "../../../../../js/utils/helper/i18n.js";

export default class CloudTicketDetail extends HTMLElement {
	static #selectors = {
		attachmentsFileListContainer: ".AttachmentsList-container",
		attachmentsFileList: ".AttachmentsList",
		attachmentsFileListEntry: ".AttachmentsList-itemContainer",
		attachmentsFileListTmpl: "#attachments-file-list-tmpl",
		commentDate: ".CommentThread-commentDate",
		commentEntry: ".CommentThread-entry",
		commentField: "#ticket-detail-ticket-comment",
		commentForm: "#ticket-detail-comment-form",
		commentMessage: ".CommentThread-commentMessage",
		commentThread: "#ticket-detail-comment-thread",
		commentThreadLastEntry: ".CommentThread-entry:last-of-type",
		commentTmpl: "#ticket-detail-comment-tmpl",
		fileDrop: "finstral-filedrop",
		toaster: "#cloud-ticket-detail-toaster",
		toastTmpl: "#cloud-ticket-detail-toast-tmpl",
		submitButton: ".TicketDetailComment-button",
	};

	#elements;

	#fieldNames = {
		commentFieldName: "ticket-detail-ticket-comment",
		filesFieldName: "ticket-detail-files",
	};

	#state = {
		fieldErrors: [],
		formData: {},
	};

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			commentField: /** @type {HTMLTextAreaElement} */ (
				this.querySelector(CloudTicketDetail.#selectors.commentField)
			),
			commentForm: /** @type {HTMLFormElement} */ (
				this.querySelector(CloudTicketDetail.#selectors.commentForm)
			),
			fileDrop: /** @type {HTMLElement} */ (
				this.querySelector(CloudTicketDetail.#selectors.fileDrop)
			),
			submitButton: /** @type {HTMLButtonElement} */ (
				this.querySelector(CloudTicketDetail.#selectors.submitButton)
			),
		};
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	constructor() {
		super();

		this.#elements = this.#getElements();

		const { commentForm } = this.#elements;

		if (commentForm) {
			this.#addEventListeners();
		}
	}

	/**
	 * Clones the toast template and attaches it to the DOM.
	 * This also takes care of marking the toast as cloned
	 * and reattaching event listeners.
	 * @private
	 * @returns {HTMLElement} The cloned toast element.
	 */
	#getClonedToast() {
		const toastTmpl = this.querySelector(
			CloudTicketDetail.#selectors.toastTmpl,
		);

		if (!toastTmpl) {
			throw new Error("Toast template not found.");
		}

		const clonedToast = toastTmpl.content.cloneNode(true);
		this.appendChild(clonedToast);

		const toast = this.querySelector(CloudTicketDetail.#selectors.toaster);

		toast.setAttribute("cloned", true);
		toast.reattachEventListeners();

		return toast;
	}

	/**
	 * Generates file list entries.
	 * @param {Array} attachments - The list of attachments.
	 * @private
	 * @returns {Array} An array of `HTMLLIElement` items for all file entries.
	 */
	#getFileListEntries(attachments) {
		const { attachmentsFileListEntry, attachmentsFileListTmpl } =
			CloudTicketDetail.#selectors;
		const fileListTmpl = document.querySelector(attachmentsFileListTmpl);
		const baseIconsPath = fileListTmpl.dataset.iconsPath;

		const entries = [];

		attachments.forEach((attachment) => {
			const fileListItem = fileListTmpl.content
				.querySelector(attachmentsFileListEntry)
				.cloneNode(true);
			const attachmentFigcaption = fileListItem.querySelector("figcaption");
			const attachmentLinkDesktop = fileListItem.querySelector("a");
			const attachmentPreviewImg = fileListItem.querySelector("img");

			const { file_name, preview_url, source_url } = attachment;
			const previewURL = preview_url.startsWith("http")
				? preview_url
				: `${baseIconsPath}/${preview_url}`;

			attachmentFigcaption.textContent = file_name;
			attachmentLinkDesktop.href = source_url;
			attachmentPreviewImg.src = previewURL;

			entries.push(fileListItem);
		});

		return entries;
	}

	/**
	 * Updates the attachments file list in the DOM.
	 * @param {Array} attachments - The list of attachments to attach to the existing list.
	 * @private
	 * @returns {void}
	 */
	#updateAtttachmentsFileList(attachments) {
		const { attachmentsFileListContainer, attachmentsFileList } =
			CloudTicketDetail.#selectors;
		const fileList = document.querySelector(attachmentsFileListContainer);
		const fileListItems = fileList.querySelector(attachmentsFileList);
		const fileEntries = this.#getFileListEntries(attachments);

		fileList.hidden = false;
		fileListItems.append(...fileEntries);
	}

	/**
	 * Appends a comment to the comment thread.
	 * @param {object} comment - The comment object containing message details and creation date.
	 */
	#appendComment(comment) {
		const {
			commentDate,
			commentEntry,
			commentMessage,
			commentThread,
			commentThreadLastEntry,
			commentTmpl,
		} = CloudTicketDetail.#selectors;
		const thread = document.querySelector(commentThread);
		const threadContainer = thread.parentElement;
		const tmpl = document.querySelector(commentTmpl);
		const entry = tmpl.content.querySelector(commentEntry).cloneNode(true);
		const date = entry.querySelector(commentDate);
		const message = entry.querySelector(`${commentMessage} pre`);

		message.textContent = comment.messageDetail;
		date.setAttribute("datetime", comment.createDateTime);
		date.textContent = comment.createDateTime;

		threadContainer.hidden = false;
		thread.appendChild(entry);

		const newEntry = thread.querySelector(commentThreadLastEntry);
		const newMessage = newEntry.querySelector(commentMessage);
		// this makes the message container focusable
		newMessage.setAttribute("tabindex", -1);
		newMessage.focus();
		newMessage.scrollIntoView(false, { behavior: "smooth" });
	}

	/**
	 * Validates the form.
	 * @returns {boolean} True if the form is valid, otherwise false.
	 */
	#isValid() {
		const { commentField } = this.#elements;

		let isValid = true;

		if (!commentField.checkValidity()) {
			isValid = false;

			commentField.setAttribute("aria-invalid", true);
			commentField.nextElementSibling.querySelector("span").textContent =
				getLocalizedMessage("global.field.required");

			commentField.focus();
		}

		return isValid;
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setFormData() {
		const { commentForm } = this.#elements;
		const { commentFieldName } = this.#fieldNames;
		const formData = new FormData(commentForm);

		this.#state.formData = {
			comment: formData.get(commentFieldName),
		};
	}

	/**
	 * Posts a comment to the server.
	 * @private
	 */
	async #postComment() {
		const { commentForm, fileDrop, submitButton } = this.#elements;
		const { formData } = this.#state;

		submitButton.classList.add("Button--loading");
		submitButton.disabled = true;

		const toast = this.#getClonedToast();

		const payload = {
			Comment: formData.comment,
		};

		try {
			if (fileDrop.containsFiles()) {
				payload.Files = await getFilesAsBase64(fileDrop.getFiles());
			}

			const jsonResponse = await doFetch(payload, commentForm.action);
			const {
				attachments,
				create_date_time,
				message,
				message_detail,
				success,
			} = jsonResponse;

			if (success) {
				const comment = {
					createDateTime: create_date_time,
					messageDetail: message_detail,
				};

				if (attachments.success) {
					this.#updateAtttachmentsFileList(attachments.files);
				}

				this.#appendComment(comment);
				toast.showToast(message);

				commentForm.reset();
				fileDrop.reset();
			} else {
				const toast = this.#getClonedToast();
				toast.setToastStyle("critical").showToast(message);
				return;
			}
		} catch (error) {
			toast
				.setToastStyle("critical")
				.showToast(getLocalizedMessage("global.error.general"));
			throw new Error(error);
		} finally {
			submitButton.classList.remove("Button--loading");
			submitButton.disabled = false;
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListeners() {
		const { commentForm } = this.#elements;

		commentForm.addEventListener("submit", (event) => {
			event.preventDefault();

			if (!this.#isValid()) {
				return;
			}

			this.#setFormData();
			this.#postComment();
		});
	}
}

customElements.define("cloud-ticket-detail", CloudTicketDetail);
