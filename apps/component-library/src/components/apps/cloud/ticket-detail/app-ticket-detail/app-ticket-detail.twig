{{ attach_library('finstral_global/apps-app-ticket-detail') }}

{% set message_store = {
	"global.error.general": "global.error.general"|tc,
	"global.field.required": "global.field.required"|tc,
} %}

<cloud-ticket-detail class="TicketDetail">
	<div class="u-container TicketDetail-wrapper">
		<h1 class="u-typo-HeadlineM TicketDetail-pageTitle">{{ title }}</h1>

		<section aria-labelledby="ticket-description-title">
			<h2 class="u-typo-TextL TicketDetail-descriptionTitle" id="ticket-description-title">{{ "ticket_detail.description.title"|tc }}</h2>
			<ul class="TicketDetail-meta">
				<li><span class="TicketDetail-reporter">{{ reporter }}</span></li>
				<li><time class="TicketDetail-createDate" datetime="{{ create_date_time }}">{{ create_date_time }}</time></li>
				<li>
					{% include "@elements/badge-list/badge-list.twig" with {
						badges: status_tags,
					} only %}
				</li>
			</ul>

			<div class="TicketDetail-message">
				<pre class="u-typo-TextM">{{ message_detail }}</pre>
			</div>

			{# conditional logic is defined in the component #}
			{% include "@apps/cloud/ticket-detail/app-ticket-detail/attachments-list/attachments-list.twig" with {
				attachments: attachments.files,
				id: "ticket-detail-message-detail-file-list",
			} only %}
		</section>

		<div class="TicketDetail-commentForm">
			<h2 class="u-typo-TextL" id="ticket-comments-title">{{ "ticket_detail.comments.title"|tc }}</h2>

			<form
				aria-labelledby="ticket-comments-title"
				action="{{ create_comment_api_url }}"
				id="ticket-detail-comment-form"
				method="POST"
				name="ticket-detail-comment-form"
				novalidate
			>
				<div class="Form-fieldGroup ContactSupport-fieldGroup">
					{% include "@elements/form-element/label/label.twig" with {
						for: "ticket-detail-ticket-comment",
						icon: true,
						icon_name: "edit",
						required: true,
						classes: ["TicketDetail-commentFormLabel"],
						title: "ticket_detail.comments.field_label"|tc,
					} only %}

					{% include "@elements/form-element/textarea/textarea.twig" with {
						id: "ticket-detail-ticket-comment",
						name: "ticket-detail-ticket-comment",
						required: true,
					} only %}

					{% include "@elements/form-validation-errors/error-container/error-container.twig" with {
						id: "contact-support-subject-error",
					} only %}
				</div>

				<div class="TicketDetail-commentFormAttachments">
					{% include "@elements/filedrop/filedrop.twig" with {
						attachments_config: attachments_config,
						file_constraints: "form_contact_support.file_constraints_message"|tc,
						field_description: "ticket_detail.file_upload_description"|tc,
						id: "cloud-ticket-detail-file-uploader",
						label: "ticket_detail.file_upload_label"|tc,
						label_classes: ["TicketDetail-filesLabel"],
						name: "ticket-detail-files",
						toast_id: "cloud-ticket-detail-toast-tmpl",
					} only %}
				</div>

				{% include "@elements/button/button.twig" with {
					classes: ["TicketDetailComment-button"],
					label: "ticket_detail.comments.submit"|tc,
					type: "submit",
				} only %}
			</form>
		</div>

		{# conditional logic is defined in the component #}
		{% include "@apps/cloud/ticket-detail/app-ticket-detail/comment-thread/comment-thread.twig" with {
			comments: comments,
		} only %}

		<footer aria-label="ticket-footer">
			{% include "@elements/link/link.twig" with {
				icon: {
						name: "arrow_back",
						position: "start",
					},
				label: "ticket_detail.back_to_overview"|tc,
				url: ticket_overview_url,
			} only %}
		</footer>
	</div>

	<template id="cloud-ticket-detail-toast-tmpl">
		{% include "@elements/toast/toast.twig" with {
			id: "cloud-ticket-detail-toaster",
			toast_role: "alert",
			dismissible_by: ["dismissable-user"]
		} only %}
	</template>

	<script id="cloud-message-store" type="application/json">{{ message_store|json_encode()|raw }}</script>
</cloud-ticket-detail>
