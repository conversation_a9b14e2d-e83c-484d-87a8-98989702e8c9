/** @define AttachmentsList; */

.AttachmentsList {
	display: flex;
	flex-flow: row wrap;
	gap: var(--size-12);
}

.AttachmentsList-title {
	margin-block-end: var(--size-8);
}

.AttachmentsList-item {
	block-size: var(--AttachmentsList-item-block-size);
	display: block;
	inline-size: var(--AttachmentsList-item-inline-size);
	text-decoration: none;

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	figure {
		display: grid;
		gap: var(--size-8);
	}

	.AttachmentsList-image {
		background-color: var(--shared-color-surface-subtle-lighter);
		block-size: 5rem;
		display: grid;
		inline-size: 5rem;
		padding: var(--size-8);
		place-items: center;
	}

	.AttachmentsList-itemImage {
		aspect-ratio: 1 / 1; /* @see https://factorial-io.atlassian.net/browse/FINSTRAL-530 */
	}

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	figcaption {
		inline-size: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

@media (width < 48rem) {
	.AttachmentsList-item {
		--AttachmentsList-item-block-size: 6rem;
		--AttachmentsList-item-inline-size: 4.5rem;
	}
}

@media (width >= 48rem) {
	.AttachmentsList-item {
		--AttachmentsList-item-block-size: 8rem;
		--AttachmentsList-item-inline-size: 6rem;
	}
}

.AttachmentsList-item--small {
	block-size: 4rem;
	inline-size: 3rem;
}
