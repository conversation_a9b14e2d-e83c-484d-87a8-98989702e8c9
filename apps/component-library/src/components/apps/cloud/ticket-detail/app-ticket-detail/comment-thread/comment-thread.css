/** @define CommentThread; */

.CommentThread-commentMessage {
	background-color: var(--shared-color-surface-primary);
	border: var(--border-subtle);
	padding: var(--size-16);

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	pre {
		font: var(--typo-TextS);
		white-space: break-spaces;
	}
}

.CommentThread-entry:not(:first-child) {
	margin-block-start: var(--size-32);
}

.CommentThread-commentDate {
	color: var(--shared-color-text-secondary);
	display: block;
	margin-block-start: var(--size-8);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.CommentThread-entry.CommentThread-entry--author .CommentThread-commentDate {
	text-align: end;
}

@media (width >= 48rem) {
	.CommentThread-comment {
		inline-size: 50%;
	}

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.CommentThread-entry.CommentThread-entry--author {
		display: flex;
		justify-content: end;
	}
}
