<div class="TicketDetail-commentThread" {% if comments is empty %} hidden {% endif %}>
	<ul class="CommentThread" id="ticket-detail-comment-thread">
		{% for comment in comments %}
			<li class="CommentThread-entry {% if comment.is_author %} CommentThread-entry--author {% endif %}">
				<article class="CommentThread-comment">
					<div class="CommentThread-commentMessage"><pre>{{ comment.message_detail }}</pre></div>
					<time datetime="{{ comment.create_date_time }}" class="u-typo-TextS CommentThread-commentDate">
						{{ comment.create_date_time }}
					</time>
				</article>
			</li>
		{% endfor %}
	</ul>
</div>

<template id="ticket-detail-comment-tmpl">
	<li class="CommentThread-entry CommentThread-entry--author">
		<article class="CommentThread-comment">
			<div class="CommentThread-commentMessage"><pre></pre></div>
			<time datetime="" class="u-typo-TextS CommentThread-commentDate"></time>
		</article>
	</li>
</template>
