$schema: http://json-schema.org/draft-07/schema

type: object

required:
  - create_comment_api_url
  - create_date_time
  - message_detail
  - reporter
  - status_tags
  - ticket_overview_url
  - title

additonalProperties: false

properties:
  attachments:
    $ref: "https://finstral.com/apps/cloud/ticket-detail#AttachmentsSchema"

  attachments_config:
    $ref: "https://finstral.com/apps/cloud/ticket-detail#AttachmentsConfigSchema"

  create_comment_api:
    format: uri-reference
    type: string

  create_date_time:
    type: string

  comments:
    $ref: "https://finstral.com/apps/cloud/ticket-detail#CommentsSchema"

  message_detail:
    type: string

  reporter:
    type: string

  status_tags:
    $ref: "https://finstral.com/elements/badge-list#BadgeListSchema"

  ticket_overview_url:
    format: uri-reference
    type: string

  title:
    type: string
