$hidden: true

attachments:
  files:
    - file_name: FIN-Door Flat Frame-Frame 78 Aluminium-Aluminium
      preview_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment001.png
      source_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment001.png
    - file_name: Bildschirmfoto 2024-09-05 um 10.24.56.png
      preview_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment002.png
      source_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment002.png
    - file_name: Bildschirmfoto 2024-09-05 um 10.24.56.png
      preview_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment003.png
      source_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment003.png
    - file_name: Bildschirmfoto 2024-09-05 um 10.24.56.png
      preview_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment004.png
      source_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment004.png
    - file_name: Bildschirmfoto 2024-09-05 um 10.24.56.png
      preview_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment005.png
      source_url: /build/assets/img/dummy/cloud-ticket-detail/ticket-detail-attachment005.png
  total_file_size:
    size: 1.2
    unit: MB

comments:
  - create_date_time: "2024-11-05T15:30:00Z"
    is_author: true
    message_detail: >
      Author: Samuel L.


      This is the initial setup for our project. Let's keep the momentum going!

  - create_date_time: "2024-11-05T16:00:00Z"
    is_author: false
    message_detail: >
      Author: Jane Smith


      I've added a few comments on the document regarding the project scope.

  - create_date_time: "2024-11-05T16:20:00Z"
    is_author: true
    message_detail: >
      Author: Samuel L.


      Good feedback, Jane. I'll make sure to address those points in the next version.

  - create_date_time: "2024-11-05T16:28:00Z"
    is_author: true
    message_detail: >
      Author: Samuel L.


      Good feedback, Jane. I'll make sure to address those points in the next version.
      Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.

  - create_date_time: "2024-11-05T17:00:00Z"
    is_author: false
    message_detail: >
      Author: Lisa Chang


      Could we also include a section on potential risks and mitigations?

  - create_date_time: "2024-11-05T17:30:00Z"
    is_author: true
    message_detail: >
      Author: Samuel L.


      I'll draft a risk analysis section and share it by tomorrow.

  - create_date_time: "2024-11-05T18:00:00Z"
    is_author: false
    message_detail: >
      Author: Emily N.


      Thanks, Tom! I'll review it once it's available.

create_comment_api_url: /ticket-detail/comment/create

create_date_time: 23.05.202, 09:14

message_detail: >
  Dear Finstral employee,


  A change was requested in the roles/rights via the Finstral Cloud:

  Request sent by: <EMAIL>


  For the following user:


  Name of the user: Mauro Filippone

  E-mail of the user: <EMAIL>

  Change: +Architects

  Notes:


  Thank you for processing this request.


  Always with best regards,

  The Finstral Cloud

reporter: Tudor Florin Dragos

title: "CompINC-142236: Enquiry change Cloud user 2sra"

status_tags:
  - $ref: /elements/badge#Neutral
  - $ref: /elements/badge#Success
  - $ref: /elements/badge#Critical
  - $ref: /elements/badge#Activity

ticket_overview_url: /ticket-overview

$variants:
  - $name: Full page
    breadcrumb:
      - text: Dashboard
        url: url
      - text: Ticket Overview
        url: url
      - text: "CompINC-142236: Enquiry change Cloud user 2sra"
