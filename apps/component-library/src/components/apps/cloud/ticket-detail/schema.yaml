$schema: http://json-schema.org/draft-07/schema
$id: https://finstral.com/apps/cloud/ticket-detail

$def:
  attachments:
    $anchor: AttachmentsSchema
    additionalProperties: false
    type: object
    required:
      - files
      - total_file_size
    properties:
      files:
        type: array
        items:
          additionalProperties: false
          type: object
          required:
            - file_name
            - preview_url
            - source_url
          properties:
            file_name:
              type: string
            preview_url:
              type: string
              format: uri-reference
            source_url:
              type: string
              format: uri-reference
      total_file_size:
        additionalProperties: false
        type: object
        required:
          - size
          - unit
        properties:
          size:
            type: number
          unit:
            type: string

  attachments_config:
    $anchor: AttachmentsConfigSchema
    accepted_formats:
      type: array
      items:
        type: string

    multiple:
      type: boolean

    max_file_count:
      type: number

    max_filesize_message:
      type: string

    max_filesize:
      additionalProperties: false
      type: object
      properties:
        accumulative:
          type: boolean
        size:
          type: number
        unit:
          type: string
          enum:
            - Bytes
            - KB
            - MB

  breadcrumb:
    $anchor: BreadcrumbSchema
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - text
      properties:
        text:
          type: string
        url:
          type: string
          format: uri-reference

  comments:
    $anchor: CommentsSchema
    type: array
    items:
      additionalProperties: false
      type: object
      required:
        - create_date_time
        - is_author
        - message_detail
      properties:
        create_date_time:
          type: string
        is_author:
          type: boolean
        message_detail:
          format: html
          type: string

type: object

required:
  - breadcrumb
  - create_comment_api_url
  - create_date_time
  - message_detail
  - reporter
  - status_tags
  - ticket_overview_url
  - title

additionalProperties: false

properties:
  breadcrumb:
    $ref: "#BreadcrumbSchema"

  attachments:
    $ref: "#AttachmentsSchema"

  attachments_config:
    $ref: "#AttachmentsConfigSchema"

  comments:
    $ref: "#CommentsSchema"

  create_comment_api_url:
    format: uri-reference
    type: string

  create_date_time:
    type: string

  message_detail:
    type: string

  reporter:
    type: string

  status_tags:
    $ref: "https://finstral.com/elements/badge-list#BadgeListSchema"

  ticket_overview_url:
    format: uri-reference
    type: string

  title:
    type: string
