{{ attach_library('finstral_global/apps-app-ticket-overview') }}

<article class="TicketOverview u-container">
	<h1 class="TicketOverview-title u-typo-HeadlineM">{{ "ticket_overview.title"|tc }}</h1>

	{% if is_employee %}
		<div class="TicketOverview-general">
			<p>{{ "ticket_overview.is_employee"|tc({"@portal": ky2help_url}) }}</p>
		</div>
	{% else %}
		<section class="TicketOverview-openTickets">
			{% include "@apps/cloud/ticket-overview/app-ticket-overview/ticket-status/ticket-status.twig" with {
				is_open: true,
				status: "open",
				title: "ticket_status.title.open"|tc,
				tickets: open_tickets,
				org_name: org_name,
			} only %}
		</section>

		<section class="TicketOverview-closedTickets">
			{% include "@apps/cloud/ticket-overview/app-ticket-overview/ticket-status/ticket-status.twig" with {
				is_open: false,
				status: "completed",
				title: "ticket_status.title.closed"|tc,
				tickets: completed_tickets,
			} only %}
		</section>
	{% endif %}
</article>
