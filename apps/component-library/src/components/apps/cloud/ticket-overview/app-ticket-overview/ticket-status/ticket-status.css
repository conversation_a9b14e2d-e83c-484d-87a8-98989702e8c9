/** @define TicketStatus; */

.TicketStatus {
	background-color: var(--shared-color-surface-primary);
	border: var(--border-subtle);
	padding: var(--TicketStatus-padding);
}

.TicketStatus-subTitle {
	color: var(--shared-color-text-secondary);
	margin-block-start: var(--size-8);
}

.TicketStatus-sectionTitle {
	align-items: center;
	cursor: pointer;
	display: flex;
	gap: var(--size-8);
	justify-content: space-between;
	margin-block: var(--TicketStatus-section-title-margin-block);
	padding-block: var(--TicketStatus-section-title-padding-block);
}

.TicketStatus-titleIcon {
	flex-shrink: 0;
}

.TicketStatus-details[open] .TicketStatus-titleIcon {
	rotate: 180deg;
}

.TicketStatus-emptyResults {
	margin-block-start: var(--size-16);
}

@media (width < 64em) {
	.TicketStatus {
		--TicketStatus-padding: var(--size-16) var(--size-16) var(--size-24);
		--TicketStatus-section-title-padding-block: var(--size-8);
		--TicketStatus-section-title-margin-block: var(--size-32);
	}
}
@media (width >= 64em) {
	.TicketStatus {
		--TicketStatus-padding: var(--size-40);
		--TicketStatus-section-title-padding-block: var(--size-12);
		--TicketStatus-section-title-margin-block: var(--size-36);
	}
}
