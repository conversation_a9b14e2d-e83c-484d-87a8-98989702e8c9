{% set no_tickets_message = status == "open" ? "ticket_overview.no_open_tickets"|tc : "ticket_overview.no_completed_tickets"|tc %}

<div class="TicketStatus">
	<h2 class="u-typo-HeadlineS">{{ title }}</h2>
	{% if org_name is defined %}
		<span class="TicketStatus-subTitle">{{ org_name }}</span>
	{% endif %}

	{% if tickets %}
		<details class="TicketStatus-details" {% if is_open %}open{% endif %}>
			<summary class="TicketStatus-sectionTitle">
				{{ "ticket_status.section.title"|tc({"@ticket_amount": tickets|length}) }}
				{% include "@elements/icon/icon.twig" with {
					classes: ["TicketStatus-titleIcon"],
					name: "chevron_down",
				} only %}
			</summary>
				{% include "@apps/cloud/ticket-overview/app-ticket-overview/ticket-table/ticket-table.twig" with {
					tickets: tickets,
				} only %}
		</details>
	{% else %}
		<p class="TicketStatus-emptyResults">{{ no_tickets_message }}</p>
	{% endif %}
</div>
