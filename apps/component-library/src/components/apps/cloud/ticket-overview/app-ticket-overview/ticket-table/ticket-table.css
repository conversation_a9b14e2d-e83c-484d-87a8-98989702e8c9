/** @define TicketTable; weak */

.TicketTable-subjectLink {
	display: inline;
	font-weight: 500;
}

.TicketTable-subject {
	max-inline-size: 16rem; /* magic number from design */
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.TicketTable-tags {
	display: flex;
	flex-wrap: wrap;
	gap: var(--size-4);
}

.TicketTable-date {
	color: var(--shared-color-text-secondary);
}

/* mobile layout */
@media (width < 64rem) {
	.TicketTable-id {
		display: block;
	}

	.TicketTable :is(table, thead, thead th, td, tbody) {
		display: block;
	}

	.TicketTable thead {
		border: 0;
		clip: rect(0 0 0 0);
		clip-path: inset(100%);
		display: block;
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		white-space: nowrap;
		width: 1px;
	}

	.TicketTable-name::before,
	.TicketTable-name::after {
		content: "|";
		margin-inline: var(--size-4);
	}

	.TicketTable tr {
		padding-block: var(--size-24);
	}

	.TicketTable tr:first-child {
		border-block-start: var(--border-subtle);
	}

	.TicketTable tbody tr {
		border-block-end: var(--border-subtle);
		display: grid;
		grid-template-areas:
			"subject subject subject"
			"id      name    service"
			"tags    tags    tags"
			"date    date    date";
		grid-template-columns: max-content max-content 1fr max-content;
		row-gap: var(--size-16);
	}

	.TicketTable th,
	.TicketTable td {
		border: none;
		padding: 0;
	}

	.TicketTable td:nth-child(1) {
		grid-area: subject;
	}

	.TicketTable td:nth-child(2) {
		grid-area: id;
	}

	.TicketTable td:nth-child(3) {
		grid-area: name;
	}

	.TicketTable td:nth-child(4) {
		grid-area: service;
	}

	.TicketTable td:nth-child(5) {
		grid-area: tags;
	}

	.TicketTable td:nth-child(6) {
		grid-area: date;
	}
}
