{% set head = [
	{label: "ticket_table.label_subject"|tc},
	{label: "ticket_table.label_id"|tc},
	{label: "ticket_table.label_name"|tc},
	{label: "ticket_table.label_service"|tc},
	{label: "ticket_table.label_tags"|tc},
	{label: "ticket_table.label_date"|tc},
] %}

{% set body = [] %}

{% for ticket in tickets %}
	{% set subject %}
	<div class="TicketTable-subject">
		{% include "@elements/link/link.twig" with {
			label: ticket.subject,
			url: ticket.url,
			tone: "neutral",
			classes: ["TicketTable-subjectLink", "u-typo-TextS"]
		} only %}
	</div>
	{% endset %}

	{% set id %}
		{% include "@elements/link/link.twig" with {
			label: ticket.id,
			url: ticket.url,
			tone: "neutral",
			classes: ["TicketTable-id", "u-typo-TextS"]
		} only %}
	{% endset %}

	{% set name %}
		<div class="TicketTable-name u-typo-TextS">
			{{ ticket.name }}
		</div>
	{% endset %}

	{% set service %}
		<div class="TicketTable-service u-typo-TextS">
			{{ ticket.service }}
		</div>
	{% endset %}

	{% set status_tags %}
		<ul class="TicketTable-tags u-typo-TextS">
			{% for tag in ticket.status_tags|slice(0, 3) %}
				<li>
					{% include "@elements/badge/badge.twig" with tag only %}
				</li>
			{% endfor %}
		</ul>
	{% endset %}

	{% set date %}
		<div class="TicketTable-date u-typo-TextS">
			{{ ticket.date }}
		</div>
	{% endset %}

	{% set row = [
		{content: subject},
		{content: id},
		{content: name},
		{content: service},
		{content: status_tags},
		{content: date},
	] %}

	{% set body = body|merge([row]) %}
{% endfor %}


<div class="TicketTable">
	{% include "@elements/table/table.twig" with {
		head: head,
		body: body,
	} only %}
</div>
