$schema: http://json-schema.org/draft-07/schema
$id: https://finstral.com/apps/cloud/ticket-overview

$def:
  status_tag:
    $anchor: StatusTagSchema
    type: object

    required:
      - label
      - color

    additionalProperties: false

    properties:
      label:
        type: string
      color:
        type: string

  tickets:
    $anchor: TicketSchema
    type: array
    items:
      type: object

      required:
        - subject
        - id
        - name
        - service
        - status_tags
        - date
        - url

      additionalProperties: false

      properties:
        date:
          type: string

        id:
          type: string

        name:
          type: string

        service:
          type: string

        status_tags:
          type: array
          items:
            $ref: "#StatusTagSchema"

        subject:
          type: string

        url:
          type: string
          format: uri-reference

type: object

required:
  - breadcrumb
  - is_employee

additionalProperties: false

properties:
  breadcrumb:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - text
      properties:
        text:
          type: string
        url:
          type: string
          format: uri-reference

  completed_tickets:
    $ref: "#TicketSchema"

  is_employee:
    type: boolean

  open_tickets:
    $ref: "#TicketSchema"

  org_name:
    type: string
