/** @define UserManagementDialog; */

.UserManagementDialog {
	border: none;
	max-block-size: 100svh;
	max-inline-size: 100%;
	overflow-y: auto;
	padding: var(--size-24);
}

/* Backdrop */

.UserManagementDialog::backdrop {
	background: var(--shared-color-surface-overlay-dialog);
}

/* Header */

.UserManagementDialog-header {
	margin-block-end: var(--size-24);
	position: relative;
}

/* Close */

.UserManagementDialog-close {
	inset-block-start: 0;
	inset-inline-end: 0;
	position: absolute;
}

/* Profile */

.UserManagementDialog-profile {
	column-gap: var(--size-12);
	display: grid;
	grid-template-areas:
		"image name"
		"image unit";
	grid-template-columns: var(--size-64) 1fr;
	row-gap: var(--size-8);
}

.UserManagementDialog-profileImage {
	border-radius: 50%;
	grid-area: image;
	overflow: clip;
}

.UserManagementDialog-profileName {
	align-self: end;
	grid-area: name;
}

.UserManagementDialog-profileUnit {
	align-self: start;
	color: var(--shared-color-text-secondary);
	grid-area: unit;
}
