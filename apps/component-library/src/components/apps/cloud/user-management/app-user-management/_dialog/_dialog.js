import { getEntryFromStorage } from "../../../../../../js/utils/helper/store.js";

class UserManagementDialog extends HTMLElement {
	static #selectors = {
		dialog: ".js-UserManagementDialog-dialog",
		profileImg: ".CardUserProfile-avatar img",
		profileName: ".js-CardUserProfile-name",
		profileUnit: ".CardUserProfile-organisation",
	};

	#elements;

	/**
	 * Opens the user management dialog.
	 * @public
	 * @returns {void}
	 */
	openUserManagementDialog = () => {
		this.#open();
	};

	/**
	 * Closes the user management dialog.
	 * @public
	 * @returns {void}
	 */
	closeUserManagementDialog = () => {
		this.#elements.dialog.close();
	};

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			dialog: /** @type {HTMLDialogElement} */ (
				this.querySelector(UserManagementDialog.#selectors.dialog)
			),
			profileImg: /** @type {HTMLImageElement} */ (
				this.querySelector(UserManagementDialog.#selectors.profileImg)
			),
			profileName: /** @type {HTMLSpanElement} */ (
				this.querySelector(UserManagementDialog.#selectors.profileName)
			),
			profileUnit: /** @type {HTMLSpanElement} */ (
				this.querySelector(UserManagementDialog.#selectors.profileUnit)
			),
		};
	}

	/**
	 * Create User Management dialog
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		this.#addEventListeners();
	}

	/**
	 * Add event listeners
	 */
	#addEventListeners() {
		this.#elements.dialog.addEventListener("close", this.#onClose.bind(this));
	}

	/**
	 * Sets the user details in the dialog.
	 *
	 * This method retrieves the user details from `localStorage` and updates the
	 * profile image, name, and unit in the dialog. If no user details are found in
	 * the storage, the method returns without making any changes.
	 * @private
	 * @returns {void}
	 */
	#setUserDetail() {
		const { profileImg, profileName, profileUnit } = this.#elements;

		const userManagementState = getEntryFromStorage("userManagementState");
		const { userDetail } = userManagementState;

		if (!userDetail || !profileImg) {
			return;
		}

		profileImg.src = userDetail.avatar_url || "";
		profileName.textContent = userDetail.name || "";
		profileUnit.textContent = userDetail.unit || "";
	}

	/**
	 * Open dialog
	 */
	#open() {
		this.#setUserDetail();
		this.#elements.dialog.showModal();
		document.documentElement.style.overflow = "hidden";
	}

	/**
	 * Dialog close event callback
	 */
	#onClose() {
		const dialogId = this.id;

		if (dialogId) {
			const customEvent = new CustomEvent("dialog-closed", {
				detail: {
					dialogId,
				},
			});
			const parent = document.querySelector("cloud-user-management");
			parent.dispatchEvent(customEvent);
		}

		document.documentElement.style.overflow = "";
	}
}

customElements.define("user-management-dialog", UserManagementDialog);
