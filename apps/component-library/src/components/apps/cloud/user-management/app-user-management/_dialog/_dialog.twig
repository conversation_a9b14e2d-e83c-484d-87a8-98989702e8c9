{% set avatar_fallback = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUTSURBVHgB7Z2xcttGEIZ/KU1KulNnqEtnunMXsEsn+QlMPYHjMpXJJ4j8BKa6dJY7d0a6dKGfIOeOXejO7rxLLGQIBIkDcHc4UvvNLAFyYGsG3+wecDjcnSByxuPxiDYpRULxWLYco1KUWUsY+b6k+CzbJbFGxJwgMkhAQptLiif4IcIlBrmc98gFLRERUQghCSltLpCLSBAWQ5FR3JCbDAMzmBApRS+RSxgjDgzFnCIjOQYDEFwIieCT/4Jiiu36HxMLinloMcGESNvwFnm7cEgsEFCMdyFSml5T/I7DZoEAYn6CR0gGtxG3OLysqINL7eXZ2dmX1Wrl7crMS4YccHmyJaO48pEtzjNEsuIvil9wvCQUU8qWb5Qt/8AhzjLkiNqKtlxTpryCI5wIkRL1DvHcT4TGUExclLDeQkTGR4S/w44NAwdSegmRmzyWEfMNXki443LSp3+ssxCVsZNeUjoJURmNdJbSWoi0Gf9CZTTBUp62bVNO2xxcasBVRjN8jj7KObOmlRDo1VRbEop3co9mhbUQ+k//hMroAre3r20Ptuo6ke6QGZSuPJNOycZulsZGXRtxZ1g18jYli3ttVUZ/+By+bTpob8mi7Jji4XUW+iRpKl07S5b2UXmDS9f5rvFh+0oWXxkkUFxTPKaopTZDJDv+g+KT87oGfleGWF83K52pbeC3MkSzIyhPqx2QdRmi2RGOafWHexmi2RGcrSuuaoakUELCV1z37vOqQrRcheei/OVOiLwSkEAJzVjO/YZyhryAMhSXxU5ZSAplKO7K1kaIDFpIoAxFUjzqLTLkoY44jIlN2SqEXEAZGn7JVTMkIlL+OJEREf9DiYFHnCGaHfGQqpC4SFhIAiUWNkKeQImFpO1QUsUvj7VkxcWIhegguHhQIZEx0jYkMlRIZKiQyGAhayixsFYhcaFCIsOwEAMlGljIZyix8EkzJC60ZEXGkoVkUGJheSojrw2UoTHsorhT/xvK0Hzij0JI5wm3FGfc8sdp+YsyKJukuHuDajwe85tTCZQh4PbjnHfKvb3voQxFVuyUhWjZGo6bYqf60ifP+qMD58JyV66Y6gMqLVvhmZe/VIVcQ7vjQ5OVv9wTInftN1BCsajOd1L3TH0BJRTz6g9bQmTujQyKbxZtZgO6guKbed2PtULE3Bsovljsmgxz37isGfSKywcGO7KD2TkJ5mq1+spL+tDub1Bc8mrfiqI28/byRJgpFBfwCqKTfQfYDCXlBl5LV3/4HDZeLDVONU6la62lywl/UHZ8aDrIau53nviXpDyi3WdQuvCGZMxsDmwz+n0GfdTbBe7NtZ4d3FqI9HM9h45QaYOhmLT5B7rkkT/8L3nEyB+YQK+89lEsCmbQEl02zz3hl80rUClb9JLB6NKr7jBwsPRq75c+S22KwcOFMyKOxYnLULbwM/mXeFjwY4rZrgVa2uJ0gXu6o//AS/ogv6P/GccNC+DukBn3jMMRTjOkQNoVXh8jxXGSUVy5KFFVvAgpkEXFjmnpJM6KOYm4hieclqwqlMpLKmE8+I47Jg99RCS3Fc/3PVxygdcMKSNlbIbDm2M+g6fyVEcwIQUHIqYYMLjoc5PXheBCCkRMirjaGD75XGKvXV3GtmUwIWVk/Ywpxa8IL8cgl3Dru32wIQohZaR/jONCtgncYpC3C/yS5W2otsGW6IRUkanQC0kJfkxry/sjbHdsrkthkE8dYiSyoUqRLd8BuQ6j6uB8ZqcAAAAASUVORK5CYII=" %}

<user-management-dialog {% if dialog_id %}id="{{ dialog_id }}"{% endif %}>
	{%- if trigger -%}
		{{ trigger }}
	{%- endif -%}

	<dialog class="UserManagementDialog js-UserManagementDialog-dialog {{ classes|join(" ") }}">
		<header aria-label="{{ dialog_title }}" class="UserManagementDialog-header">
			<form class="UserManagementDialog-close" method="dialog">
				{% set close_button_label_id = "close-button-label-" ~ random() %}
				<button type="submit" aria-labelledby="{{ close_button_label_id }}">
					<span id="{{ close_button_label_id }}" hidden>{{ "global.close_button"|tc }}</span>
					{% include "@elements/icon/icon.twig" with {name: "close"} only %}
				</button>
			</form>

			{% set avatar %}
				{% include "@elements/image/image.twig" with {
					alt: "",
					height: 128,
					uri: avatar_url|default(avatar_fallback),
					width: 128,
				} only %}
			{% endset %}
			{% if show_profile ?? true %}
				{# Values here are just placeholders, data comes from js. #}
				{% include "@elements/card-user-profile/card-user-profile.twig" with {
					avatar: avatar,
					first_name: name|default(""),
					last_name: "",
					organisation: unit|default(""),
				} only %}
			{% endif %}
		</header>

		{{ content }}
	</dialog>
</user-management-dialog>
