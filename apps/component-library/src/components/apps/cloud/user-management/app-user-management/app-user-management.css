@import url("./_dialog/_dialog.css");
@import url("./list/list.css");
@import url("./table/table.css");

/** @define UserManagement; */

.UserManagement {
	display: block;
}

.UserManagement-wrapper {
	background-color: var(--shared-color-surface-primary);
	border: var(--border-subtle);
	display: flex;
	flex-direction: column;
}

.UserManagement-section {
	display: flex;
	flex-direction: column;
}

.UserManagement-headingWrapper {
	display: flex;
	gap: var(--size-24);
}

@media (width < 64em) {
	.UserManagement-wrapper {
		gap: var(--size-40);
		padding: var(--size-16);
	}

	.UserManagement-section {
		gap: var(--size-24);
	}

	.UserManagement-headingWrapper {
		flex-direction: column;
	}

	.UserManagement-table {
		display: none;
	}
}

@media (width >= 64em) {
	.UserManagement-wrapper {
		gap: var(--size-48);
		padding: var(--size-40);
	}

	.UserManagement-section {
		gap: var(--size-32);
	}

	.UserManagement-headingWrapper {
		align-items: center;
		justify-content: space-between;
	}

	.UserManagement-list {
		display: none;
	}
}
