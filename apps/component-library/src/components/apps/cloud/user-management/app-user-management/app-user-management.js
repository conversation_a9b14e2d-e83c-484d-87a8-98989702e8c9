import {
	getLocalizedMessage,
	initMessageStore,
} from "../../../../../js/utils/helper/i18n.js";
import {
	hasPropertyForKey,
	setItemInStorage,
	updateStoredPropsForKey,
} from "../../../../../js/utils/helper/store.js";
import "./_dialog/_dialog.js";

export default class CloudUserManagement extends HTMLElement {
	static #selectors = {
		createUserDialog: "#user-management-create-user-form-dialog",
		createUserDialogTmpl: "#new-user-dialog-tmpl",
		createUserDialogTrigger: "#user-management-create-user-form-button",
		deactivateUserDialog: "#user-management-deactivate-user-form-dialog",
		deactivateUserForm: "finstral-deactivate-user",
		deactivateUserFormTitle: ".DeactivationForm-title",
		deactivateUserDialogTmpl: "#deactivation-dialog-tmpl",
		json: "#cloud-user-management-data",
		messageStoreJSON: "#cloud-message-store",
		permissionsDialog: "#user-management-permissions-form-dialog",
		permissionsDialogTmpl: "#permissions-dialog-tmpl",
		permissionsForm: "cloud-permissions-form",
		readOnlyTmpl: "#user-management-read-only-tmpl",
		toast: "#cloud-user-management-toast",
		userManagementToastTmpl: "#user-management-toast-tmpl",
		userManagementToaster: "#cloud-user-management-toaster",
		userManagementDialog: "user-management-dialog",
		userManagementOpenDialog: "user-management-dialog:has(dialog[open])",
	};

	#elements;

	#storageKey = "userManagementState";

	#state = {
		isEmployee: false,
		isReadOnly: false,
		rawData: {},
		users: [],
	};

	getUser = (userLogin) => {
		return this.#state.users.find((user) => user.login === userLogin);
	};

	/**
	 * Retrieves a specific service for a user based on the user's login and the service name.
	 * @param {string} userLogin - The login identifier of the user.
	 * @param {string} service - The label of the service to retrieve.
	 * @returns {object|string} The service object if found, otherwise a string indicating the service was not found.
	 * @example
	 * // Returns the service object for the specified user and service.
	 * {
	 *   id: 1,
	 *   label: "Service Name",
	 *   permissions: [
	 *     {
	 *       active: true,
	 *       label: "Permission 1",
	 *       value: 10,
	 *     },
	 *     {
	 *       active: false,
	 *       label: "Permission 2",
	 *       value: 20,
	 *     },
	 *   ],
	 * }
	 */
	getServiceForUser = (userLogin, service) => {
		const user = this.getUser(userLogin);
		const serviceForUser = user.services.find(
			(currentService) =>
				currentService.label.toLowerCase() === service.toLowerCase(),
		);
		return serviceForUser || "Service not found";
	};

	/**
	 * Displays a toast message.
	 * @param {string} message - The message to display.
	 * @param {string} [style] - The style of the toast message. If no value is set, the message is rendered as HTML. (optional)
	 * @example
	 * // Displays a success message.
	 * ```
	 * showToast(message);
	 * ```
	 * @example
	 * // Displays a critical message.
	 * ```
	 * showToast(message, "critical");
	 * ```
	 * @public
	 * @returns {void}
	 */
	showToast(message, style) {
		const toastTmpl = document.querySelector(
			CloudUserManagement.#selectors.userManagementToastTmpl,
		);

		if (!toastTmpl) {
			return;
		}

		const clonedToast = toastTmpl.content.cloneNode(true);
		this.appendChild(clonedToast);

		const toast = this.querySelector(
			CloudUserManagement.#selectors.userManagementToaster,
		);
		toast.setAttribute("cloned", true);
		toast.reattachEventListeners();

		// If no value is set for the style property, this is a success
		// message and therefore it needs to be rendered as HTML.
		if (!style) {
			toast.showToast(message, /** _dangerouslySetInnerHTML */ true);
		} else {
			toast.setToastStyle(style).showToast(message);
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			createUserDialog: /** @type {HTMLElement} */ (
				this.querySelector(CloudUserManagement.#selectors.createUserDialog)
			),
			createUserDialogTmpl: /** @type {HTMLTemplateElement} */ (
				this.querySelector(CloudUserManagement.#selectors.createUserDialogTmpl)
			),
			createUserDialogTrigger: /** @type {HTMLButtonElement} */ (
				this.querySelector(
					CloudUserManagement.#selectors.createUserDialogTrigger,
				)
			),
			deactivateUserDialog: /** @type {HTMLElement} */ (
				this.querySelector(CloudUserManagement.#selectors.deactivateUserDialog)
			),
			deactivateUserDialogTmpl: /** @type {HTMLTemplateElement} */ (
				this.querySelector(
					CloudUserManagement.#selectors.deactivateUserDialogTmpl,
				)
			),
			json: /** @type {HTMLScriptElement} */ (
				document.querySelector(CloudUserManagement.#selectors.json)
			),
			messageStoreJSON: /** @type {HTMLScriptElement} */ (
				document.querySelector(CloudUserManagement.#selectors.messageStoreJSON)
			),
			permissionsDialog: /** @type {HTMLElement} */ (
				this.querySelector(CloudUserManagement.#selectors.permissionsDialog)
			),
			permissionsDialogTmpl: /** @type {HTMLTemplateElement} */ (
				this.querySelector(CloudUserManagement.#selectors.permissionsDialogTmpl)
			),
			permissionsForm: /** @type {HTMLElement} */ (
				this.querySelector(CloudUserManagement.#selectors.permissionsForm)
			),
			toast: this.querySelector(CloudUserManagement.#selectors.toast),
		};
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	constructor() {
		super();

		this.#elements = this.#getElements();

		const { json, messageStoreJSON } = this.#elements;
		if (json && messageStoreJSON) {
			this.#init();
		}
	}

	/**
	 * Initializes the user management component.
	 *
	 * This function parses the JSON data from the specified element and populates the state with user data.
	 * It also sets up the necessary event listeners for the component.
	 * @returns {void}
	 * @private
	 */
	#init() {
		const { json, messageStoreJSON } = this.#elements;

		this.#state.rawData = JSON.parse(json.textContent);
		this.#state.users = [...this.#state.rawData.self];

		if (this.#state.rawData.team) {
			this.#state.users.push(...this.#state.rawData.team);
		}

		this.#state.isReadOnly = this.#state.rawData.isReadOnly;
		this.#state.isEmployee = this.#state.rawData.isEmployee;

		setItemInStorage(this.#storageKey, {
			isReadOnly: this.#state.isReadOnly,
			isEmployee: this.#state.isEmployee,
		});
		initMessageStore(JSON.parse(messageStoreJSON.textContent));

		this.#addEventListeners();
	}

	/**
	 * Appends a dialog to the CloudUserManagement component.
	 * @param {HTMLTemplateElement} dialogTmpl - The template element containing the dialog to be appended.
	 * @returns {void}
	 * @private
	 */
	#appendDialog(dialogTmpl) {
		const dialogClone = dialogTmpl.content.cloneNode(true);
		this.appendChild(dialogClone);
	}

	/**
	 * Mounts the new user dialog to the current document.
	 * @returns {void}
	 * @private
	 */
	#mountNewUserDialog() {
		const { createUserDialogTmpl } = this.#elements;

		if (!createUserDialogTmpl) {
			return;
		}

		this.#appendDialog(createUserDialogTmpl);

		const createUserDialog = this.querySelector(
			CloudUserManagement.#selectors.createUserDialog,
		);

		createUserDialog.openUserManagementDialog();
	}

	/**
	 * Mounts the form if it is not already mounted.
	 * @param {HTMLElement} form - The form element to be mounted.
	 * @param {HTMLElement} target - The target element to mount the form after.
	 * @param {string} [titleSelector] - The selector for the form title. (default: "deactivateUserFormTitle")
	 * @param {object} userDetail - The user details object. (optional)
	 * @returns {void}
	 * @private
	 */
	#mountFormIfNeeded(
		form,
		target,
		titleSelector = "deactivateUserFormTitle",
		userDetail,
	) {
		const nextSibling = target.nextElementSibling;

		if (nextSibling && nextSibling.hasAttribute("data-mounted")) {
			return;
		}

		const formClone = form.cloneNode(true);

		if (titleSelector && userDetail) {
			const formTitle = formClone.querySelector(
				CloudUserManagement.#selectors[titleSelector],
			);

			this.#setFormTitle(formTitle, userDetail);
		}

		formClone.setAttribute("data-mounted", true);
		target.insertAdjacentElement("afterend", formClone);
	}

	/**
	 * Sets the title of the form with the user's name.
	 * @param {HTMLElement} title - The title element of the deactivate user form.
	 * @param {object} userDetail - The user details object.
	 * @returns {void}
	 * @private
	 */
	#setFormTitle(title, userDetail) {
		if (title) {
			title.textContent = title.textContent.replace("@name", userDetail.name);
		}
	}

	/**
	 * Clones the toast template and attaches it to the DOM.
	 * This also takes care of marking the toast as cloned
	 * and reattaching event listeners.
	 * @private
	 * @returns {HTMLElement} The cloned toast element.
	 */
	#getClonedToast() {
		const toastTmpl = this.querySelector(
			CloudUserManagement.#selectors.userManagementToastTmpl,
		);

		if (!toastTmpl) {
			throw new Error("Toast template not found.");
		}

		const clonedToast = toastTmpl.content.cloneNode(true);
		this.appendChild(clonedToast);

		const toast = this.querySelector(
			CloudUserManagement.#selectors.userManagementToaster,
		);

		toast.setAttribute("cloned", true);
		toast.reattachEventListeners();

		return toast;
	}

	/**
	 * Displays a toast message with a link to the ticket.
	 * @param {object} ticket - The ticket object containing the URL.
	 * @returns {void}
	 * @private
	 */
	#showTicketToast(ticket) {
		const { isEmployee } = this.#state;

		const processingMessage = getLocalizedMessage("global.ticket.processing");
		const ticketReference = getLocalizedMessage(
			"global.ticket.reference",
		).replace("@ticket_id", ticket.id);
		const viewMesssage = getLocalizedMessage("global.ticket.view");

		let toastMessage = isEmployee
			? `${processingMessage} ${ticketReference}`
			: `${processingMessage} <a href="${ticket.url}">${viewMesssage}</a>.`;

		const toast = this.#getClonedToast();
		toast.showToast(toastMessage, /** _dangerouslySetInnerHTML */ !isEmployee);
	}

	/**
	 * Displays a toast message indicating that user management is in read-only mode.
	 * @private
	 * @returns {void}
	 */
	#showReadOnlyToast() {
		const readOnlyMessageTmpl = this.querySelector(
			CloudUserManagement.#selectors.readOnlyTmpl,
		);

		if (!readOnlyMessageTmpl) {
			throw new Error("Read-only message template not found.");
		}

		// In order to get the full string, we wrapped the message in a paragraph
		// tag in the template. However, it is not valid to have a paragraph
		// tag inside an output element. We therefore use `innerHTML`
		// on the message property to ensure we only send the
		// content of the paragraph to the toast.
		const message =
			readOnlyMessageTmpl.content.cloneNode(true).firstElementChild;
		const toast = this.#getClonedToast();
		toast.showToast(message.innerHTML, /** _dangerouslySetInnerHTML */ true);
	}

	/**
	 * Mounts the deactivate user dialog to the current document.
	 * @param {object} dataset - The dataset containing the user login.
	 * @param {boolean} [isMobile] - A flag indicating if the user is on a mobile (default: false).
	 * @param {HTMLElement} [target] - The target element that triggered the function call.
	 * @example
	 * {
	 *  login: "<EMAIL>",
	 * }
	 * @returns {void}
	 * @private
	 */
	#mountDeactivateUser(dataset, isMobile = false, target) {
		const { deactivateUserDialogTmpl } = this.#elements;
		const { isEmployee } = this.#state;
		const { login, status } = dataset;

		if (!deactivateUserDialogTmpl || (isMobile && status === "pending")) {
			return;
		}

		const userDetail = this.getUser(login);
		const deactivateUserForm = deactivateUserDialogTmpl.content.querySelector(
			CloudUserManagement.#selectors.deactivateUserForm,
		);

		deactivateUserForm.setAttribute("is-employee", isEmployee);
		deactivateUserForm.setAttribute("login", login);
		deactivateUserForm.setAttribute("userid", userDetail.id);

		if (isMobile && target && deactivateUserForm) {
			this.#mountFormIfNeeded(
				deactivateUserForm,
				target,
				"deactivateUserFormTitle",
				userDetail,
			);
			return;
		}

		if (status === "pending") {
			this.#showTicketToast(userDetail.user_status.ticket, target);
			return;
		}

		this.#appendDialog(deactivateUserDialogTmpl);

		const deactivateUserDialog = this.querySelector(
			CloudUserManagement.#selectors.deactivateUserDialog,
		);
		const deactivateUserFormTitle = deactivateUserDialog.querySelector(
			CloudUserManagement.#selectors.deactivateUserFormTitle,
		);

		this.#setFormTitle(deactivateUserFormTitle, userDetail);

		deactivateUserDialog.openUserManagementDialog();
	}

	/**
	 * Mounts the permissions dialog for a user to the current document.
	 * @param {boolean} [isMobile] - A flag indicating if the user is on a mobile (default: false).
	 * @param {HTMLElement} [target] - The target element that triggered the function call.
	 * @example
	 * {
	 *  login: "<EMAIL>",
	 * 	service: "contacts",
	 * }
	 * @returns {void}
	 * @private
	 */
	#mountPermissions(isMobile = false, target) {
		const { permissionsDialogTmpl } = this.#elements;

		if (!permissionsDialogTmpl) {
			return;
		}

		const permissionsForm = permissionsDialogTmpl.content.querySelector(
			CloudUserManagement.#selectors.permissionsForm,
		);

		if (isMobile && target) {
			this.#mountFormIfNeeded(permissionsForm, target);
			return;
		}

		this.#appendDialog(permissionsDialogTmpl);

		const permissionsDialog = this.querySelector(
			CloudUserManagement.#selectors.permissionsDialog,
		);

		permissionsDialog.openUserManagementDialog();
	}

	/**
	 * Persists the user and service details in local storage.
	 * @param {string} login - The login identifier for the user.
	 * @param {string} [service] - The service name to be stored. (optional)
	 * @private
	 * @throws {Error} Throws an error if saving the state to local storage fails.
	 * @returns {void}
	 */
	#persistStateInLocalStorage(login, service) {
		const userDetail = this.getUser(login);

		try {
			const hasUserDetailInStorage = hasPropertyForKey(
				this.#storageKey,
				"userDetail",
			);
			const hasServiceInStorage = hasPropertyForKey(
				this.#storageKey,
				"service",
			);

			let success = false;

			if (userDetail) {
				const storageContainer = {
					userDetail,
				};

				success = hasUserDetailInStorage
					? updateStoredPropsForKey(this.#storageKey, storageContainer)
					: setItemInStorage(
							this.#storageKey,
							storageContainer,
							/** preserveExisting */ true,
						);
			}

			if (service) {
				const storageContainer = {
					service,
				};

				success = hasServiceInStorage
					? updateStoredPropsForKey(this.#storageKey, storageContainer)
					: setItemInStorage(
							this.#storageKey,
							storageContainer,
							/** preserveExisting */ true,
						);
			}

			if (!success) {
				throw new Error(
					`Failed to save state to local storage for ${this.#storageKey}.`,
				);
			}
		} catch (error) {
			console.error("Error while saving state:", error);
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListeners() {
		const { createUserDialogTrigger } = this.#elements;

		// Because there is only a single create user button,
		// we do not use event delgation here but attach
		// the event listener directly to the button.
		if (createUserDialogTrigger) {
			createUserDialogTrigger.addEventListener("click", () => {
				if (this.#state.isReadOnly) {
					this.#showReadOnlyToast();
					return;
				}

				this.#mountNewUserDialog();
			});
		}

		this.addEventListener("click", (event) => {
			const target = event.target;
			const { login, service } = target.dataset;

			if (login) {
				// We can safely pass along service even if undefined
				// as it is optional and will be checked before
				// being used in persistStateInLocalStorage
				this.#persistStateInLocalStorage(login, service);
			}

			if (target.hasAttribute("data-mobile")) {
				target.parentElement.openUserManagementDialog();
			}

			if (target.dataset.dialog === "service-permissions") {
				this.#mountPermissions();
			}

			if (target.dataset.form === "service-permissions") {
				this.#mountPermissions(/** isMobile */ true, target);
			}

			if (target.dataset.dialog === "deactivate-user") {
				if (this.#state.isReadOnly) {
					this.#showReadOnlyToast();
					return;
				}

				this.#mountDeactivateUser(
					target.dataset,
					/** isMobile */ false,
					target,
				);
			}

			if (
				target.dataset.form === "deactivate-user" &&
				!this.#state.isReadOnly
			) {
				this.#mountDeactivateUser(target.dataset, /** isMobile */ true, target);
			}
		});

		// Because we create and mount the dialog when the user selects one
		// of the interactive elemets, we remove the dialog from the DOM
		// when it is closed. This ensures that we will always have
		// only a single instance of the relevant dialog in the DOM.
		this.addEventListener("dialog-closed", (event) => {
			const closedDialogId = event.detail.dialogId;
			const closedDialog = this.querySelector(`#${closedDialogId}`);
			closedDialog.remove();
		});
	}
}

customElements.define("cloud-user-management", CloudUserManagement);
