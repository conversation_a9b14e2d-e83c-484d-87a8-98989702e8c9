{{ attach_library('finstral_global/apps-user-management-app-user-management') }}

{% set read_only = form_create_user_api is empty and form_deactivate_user_api is empty and form_update_permissions_api is empty %}

{% set data = {
	formCreateUserAPI: form_create_user_api,
	formDeactivateUserAPI: form_deactivate_user_api,
	formUpdatePermissionsAPI: form_update_permissions_api,
	isEmployee: is_employee ? true : false,
	isReadOnly: read_only,
	self: self,
	team: team,
} %}

{% set message_store = {
	"global.form.errors.email.invalid": "global.form.errors.email.invalid"|tc,
	"global.form.errors.option.required.generic": "global.form.errors.option.required.generic"|tc,
	"global.form.errors.js.field.option.required": "global.form.errors.js.field.option.required"|tc,
	"global.form.errors.js.field.required": "global.form.errors.js.field.required"|tc,
	"global.error.general": "global.error.general"|tc,
	"global.message.readonly": "global.message.readonly"|tc({"@support_email": support_email}),
	"global.ticket.processing": "global.ticket.processing"|tc,
	"global.ticket.reference": "global.ticket.reference"|tc,
	"global.ticket.view": "global.ticket.view"|tc,
	"deactivation_form.message.readonly": "deactivation_form.message.readonly"|tc({"@support_email": support_email}),
} %}

<cloud-user-management class="UserManagement" id="cloud-user-management">
	<section class="u-container">
		<div class="UserManagement-wrapper">
			<h1 class="u-typo-HeadlineM">
				{{ "user_management.title"|tc }}
			</h1>

			<section
				class="UserManagement-section"
				aria-labelledby="user-management-self-heading"
			>
				<div class="UserManagement-headingWrapper">
					<h2 class="u-typo-HeadlineS" id="user-management-self-heading">
						{{ "user_management.self_heading"|tc }}
					</h2>

					{% if not team %}
						<div>
							{% include "@elements/button/button.twig" with {
								label: "user_management.create_user_button"|tc,
								id: "user-management-create-user-form-button",
								variant: "secondary",
								icon: {
									name: "user_create",
								},
							} only %}
						</div>
					{% endif %}
				</div>

				{# Small screens #}
				<div class="UserManagement-list">
					{% include "@apps/cloud/user-management/app-user-management/list/list.twig" with {
						read_only: read_only,
						support_email: support_email,
						users: self,
					} only %}
				</div>

				{# Large screens #}
				<div class="UserManagement-table">
					{% include "@apps/cloud/user-management/app-user-management/table/table.twig" with {
						users: self,
					} only %}
				</div>
			</section>

			{% if team %}
				<section
					class="UserManagement-section"
					aria-labelledby="user-management-team-heading"
				>
					<div class="UserManagement-headingWrapper">
						<h2 class="u-typo-HeadlineS" id="user-management-team-heading">
							{{ "user_management.team_heading"|tc }}
						</h2>

						<div>
							{% include "@elements/button/button.twig" with {
								label: "user_management.create_user_button"|tc,
								id: "user-management-create-user-form-button",
								variant: "secondary",
								icon: {
									name: "user_create",
								},
							} only %}
						</div>
					</div>

					{# Small screens #}
					<div class="UserManagement-list">
						{% include "@apps/cloud/user-management/app-user-management/list/list.twig" with {
								read_only: read_only,
								support_email: support_email,
								users: team,
						} only %}
					</div>

					{# Large screens #}
					<div class="UserManagement-table">
						{% include "@apps/cloud/user-management/app-user-management/table/table.twig" with {
								users: team,
						} only %}
					</div>
				</section>
			{% endif %}
		</div>
	</section>

	{% if not read_only %}
		<template id="new-user-dialog-tmpl">
			{% set create_user_form %}
				{% include "@apps/cloud/user-management/app-user-management/create-user-form/create-user-form.twig" with {
					form_action: form_create_user_api|default(''),
					services: services,
				} only %}
			{% endset %}

			{% include "@apps/cloud/user-management/app-user-management/_dialog/_dialog.twig" with {
				classes: ["CreateUserForm-dialog"],
				content: create_user_form,
				dialog_id: "user-management-create-user-form-dialog",
				dialog_title: "create_user_form.dialog.title"|tc,
				show_profile: false,
			} only %}
		</template>
	{% endif %}

	<template id="permissions-dialog-tmpl">
		{% set permissions_form %}
			{% include "@apps/cloud/user-management/app-user-management/permissions-form/permissions-form.twig" with {
				form_action: form_update_permissions_api,
				support_email: support_email,
			} only %}
		{% endset %}

		{% include "@apps/cloud/user-management/app-user-management/_dialog/_dialog.twig" with {
			classes: ["UserManagementTable-dialog"],
			content: permissions_form,
			dialog_id: "user-management-permissions-form-dialog",
			dialog_title: "permissions_form.dialog.title"|tc,
		} only %}
	</template>

	<template id="deactivation-dialog-tmpl">
		{% set deactivation_form %}
			{% include "@apps/cloud/user-management/app-user-management/deactivation-form/deactivation-form.twig" with {
				form_action: form_deactivate_user_api,
				deactivation_reasons: deactivation_reasons,
			} only %}
		{% endset %}

		{% include "@apps/cloud/user-management/app-user-management/_dialog/_dialog.twig" with {
			classes: ["UserManagementTable-dialog"],
			content: deactivation_form,
			dialog_id: "user-management-deactivate-user-form-dialog",
			dialog_title: "deactivation_form.dialog.title"|tc,
		} only %}
	</template>

	{% if read_only %}
		<template id="user-management-read-only-tmpl">
			<p>{{ "global.message.readonly"|tc({"@support_email": support_email}) }}</p>
		</template>

		<template id="deactivate-user-read-only-tmpl">
			<p>{{ "global.message.readonly"|tc({"@support_email": support_email}) }}</p>
		</template>
	{% endif %}

	<template id="user-management-toast-tmpl">
		{% include "@elements/toast/toast.twig" with {
			id: "cloud-user-management-toaster",
			toast_role: "alert",
			dismissible_by: ["dismissable-user"]
		} only %}
	</template>

	{% include "@elements/toast/toast.twig" with {
		id: "cloud-user-management-toast",
		toast_role: "status",
		dismissible_by: ["dismissable-user"]
	} only %}

	{% include "@elements/toast/toast.twig" with {
		id: "create-user-toast",
		toast_role: "status",
		dismissible_by: ["dismissable-user"]
	} only %}
</cloud-user-management>

<script id="cloud-user-management-data" type="application/json">{{ data|json_encode()|raw }}</script>
<script id="cloud-message-store" type="application/json">{{ message_store|json_encode()|raw }}</script>
