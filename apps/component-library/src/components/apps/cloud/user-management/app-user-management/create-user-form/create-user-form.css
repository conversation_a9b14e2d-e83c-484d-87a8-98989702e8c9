/** @define CreateUserForm; */

.CreateUserForm {
	display: block;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.CreateUserForm-form > *:not(:first-child) {
	margin-block-start: var(--size-40);
}

.CreateUserForm-copy {
	color: var(--shared-color-text-secondary);
	margin-block-start: var(--size-16);
}

.CreateUserForm-userInfo,
.CreateUserForm-permissions {
	border: none;
	padding: 0;
}

.CreateUserForm-permissions {
	margin-inline-start: var(--size-24);
}

.CreateUserForm-permissions {
	display: none;
}

.CreateUserForm-service:has([type="checkbox"]:checked)
	+ .CreateUserForm-permissions {
	display: block;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.CreateUserForm-permissionList > *,
.CreateUserForm-service {
	margin-block-start: var(--size-24);
}

.CreateUserForm-button {
	inline-size: 100%;
}

@media (width < 48em) {
	.CreateUserForm-userInfoInput:not(:nth-child(2)) {
		margin-block-start: var(--size-24);
	}

	.CreateUserForm-dialog {
		block-size: 100vh;
		inline-size: 100vw;
	}
}

@media (width >= 48em) {
	.CreateUserForm-userInfo {
		display: grid;
		gap: var(--size-24);
		grid-template-columns: 1fr 1fr;
	}

	.CreateUserForm-dialog {
		block-size: 90svh;
		inline-size: 750px;
	}
}
