import { doFetch } from "../../../../../../js/utils/helper/fetch";
import { getLocalizedMessage } from "../../../../../../js/utils/helper/i18n.js";
import { resetFormErrorState } from "../../../../../../js/utils/helper/forms";

export default class FinstralCreateUser extends HTMLElement {
	static #selectors = {
		cloudUserManagement: "cloud-user-management",
		dialog: "#user-management-create-user-form-dialog",
		fieldEmail: "#create-user-email",
		fieldFirstName: "#create-user-first-name",
		fieldSurname: "#create-user-surname",
		form: ".CreateUserForm-form",
		formValidationErrors: "finstral-form-validation-errors",
		servicesHeading: "#create-user-services-heading",
		submitButton: ".CreateUserForm-button",
	};

	#fieldNames = {
		email: "create-user-email",
		firstName: "create-user-first-name",
		note: "create-user-note",
		position: "create-user-position",
		service: "js-service",
		surname: "create-user-surname",
	};

	#state = {
		fieldErrors: [],
		formData: {},
		selectedServicesAndPermissions: [],
	};

	#elements;

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			cloudUserManagement: /** @type {HTMLElement} */ (
				document.querySelector(
					FinstralCreateUser.#selectors.cloudUserManagement,
				)
			),
			dialog: document.querySelector(FinstralCreateUser.#selectors.dialog),
			emailField: this.querySelector(FinstralCreateUser.#selectors.fieldEmail),
			firstNameField: this.querySelector(
				FinstralCreateUser.#selectors.fieldFirstName,
			),
			form: this.querySelector(FinstralCreateUser.#selectors.form),
			formValidationErrors: /** @type {HTMLElement} */ (
				this.querySelector(FinstralCreateUser.#selectors.formValidationErrors)
			),
			servicesHeading: this.querySelector(
				FinstralCreateUser.#selectors.servicesHeading,
			),
			submitButton: this.querySelector(
				FinstralCreateUser.#selectors.submitButton,
			),
			surnameField: this.querySelector(
				FinstralCreateUser.#selectors.fieldSurname,
			),
		};
	}

	/**
	 * Constructs a new instance of FinstralCreateUser.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		if (this.#elements.form) {
			this.#addEventListeners();
		}
	}

	/**
	 * Updates the form error state by setting the aria-invalid attribute on the
	 * specified field and adding the error message to error container.
	 * @param {HTMLElement} field - The form field element that has the error.
	 * @param {string} message - The error message.
	 * @param {boolean} [markInvalid] - Whether to mark the field as invalid. (default=true)
	 * @private
	 * @returns {void}
	 */
	#updateFormErrorState(field, message, markInvalid = true) {
		const fieldName = this.querySelector(`[for=${field.id}]`)
			.textContent.replace("*", "")
			.trim(); // get the text of the label which will be translated

		if (markInvalid) {
			field.setAttribute("aria-invalid", true);
		}

		field.nextElementSibling.querySelector("span").textContent =
			message.replace("@fieldname", fieldName);

		this.#state.fieldErrors.push({
			fieldName,
			id: field.id,
			message,
		});
	}

	/**
	 * Validates the form fields and checks if the required fields are filled and valid.
	 * @private
	 * @returns {boolean} - Returns true if the form is valid, false otherwise.
	 */
	#isValid() {
		const {
			emailField,
			firstNameField,
			formValidationErrors,
			servicesHeading,
			surnameField,
		} = this.#elements;

		resetFormErrorState(this);
		this.#state.fieldErrors = [];

		if (!firstNameField.checkValidity()) {
			this.#updateFormErrorState(
				firstNameField,
				getLocalizedMessage("global.form.errors.js.field.required"),
			);
		}

		if (!surnameField.checkValidity()) {
			this.#updateFormErrorState(
				surnameField,
				getLocalizedMessage("global.form.errors.js.field.required"),
			);
		}

		if (!emailField.checkValidity()) {
			const { typeMismatch, valueMissing } = emailField.validity;

			if (typeMismatch) {
				this.#updateFormErrorState(
					emailField,
					getLocalizedMessage("global.form.errors.email.invalid"),
				);
			}
			if (valueMissing) {
				this.#updateFormErrorState(
					emailField,
					getLocalizedMessage("global.form.errors.js.field.required"),
				);
			}
		}

		const serviceFields = Array.from(
			this.querySelectorAll(`[name*=${this.#fieldNames.service}]`),
		);
		const hasSelectedService = serviceFields.some(
			(serviceField) => serviceField.checked,
		);

		if (!hasSelectedService) {
			const fieldName = servicesHeading.textContent;
			const validationMsg = getLocalizedMessage(
				"global.form.errors.js.field.option.required",
			).replace("@fieldname", fieldName);

			servicesHeading.nextElementSibling.querySelector("span").textContent =
				validationMsg;
			servicesHeading.nextElementSibling.classList.toggle(
				"Form-fieldGroupError--show",
			);

			this.#state.fieldErrors.push({
				fieldName,
				id: servicesHeading.id,
				message: getLocalizedMessage(
					"global.form.errors.js.field.option.required",
				),
			});
		}

		if (hasSelectedService) {
			const selectedServices = serviceFields.filter(
				(serviceField) => serviceField.checked,
			);

			selectedServices.forEach((selectedService) => {
				const fieldsetId = selectedService.getAttribute("aria-controls");
				const permissionsForService = this.querySelector(`#${fieldsetId}`);
				const permissionFields = Array.from(
					permissionsForService.querySelectorAll("[type=checkbox]"),
				);
				const hasSelectedPersmissions = permissionFields.some(
					(permissionField) => permissionField.checked,
				);

				if (!hasSelectedPersmissions) {
					const errorOutput = permissionsForService.querySelector(
						".Form-fieldGroupError",
					);
					const validationMsg = getLocalizedMessage(
						"global.form.errors.option.required.generic",
					);

					errorOutput.querySelector("span").textContent = validationMsg;
					errorOutput.classList.toggle("Form-fieldGroupError--show");

					this.#state.fieldErrors.push({
						fieldName: `${selectedService.dataset.service} permission`,
						id: fieldsetId,
						message: validationMsg,
					});
					return;
				}
			});
		}

		formValidationErrors.setValidationErrors({
			errors: this.#state.fieldErrors,
		});

		return this.#state.fieldErrors.length ? false : true;
	}

	/**
	 * Sets the form data and selected services and permissions in the state.
	 */
	#setFormData() {
		const { form } = this.#elements;
		const { firstName, surname, email, position, service, note } =
			this.#fieldNames;
		const formData = new FormData(form);

		this.#state.formData = {
			firstName: formData.get(firstName),
			surname: formData.get(surname),
			email: formData.get(email),
			note: formData.get(note),
		};

		if (formData.get(position)) {
			this.#state.formData.position = formData.get(position);
		}

		const serviceFields = Array.from(
			this.querySelectorAll(`[name*=${service}]`),
		);
		const selectedServices = serviceFields.filter(
			(serviceField) => serviceField.checked,
		);

		Array.from(selectedServices).forEach((service) => {
			const fieldsetId = service.getAttribute("aria-controls");
			const permissionsForService = this.querySelector(`#${fieldsetId}`);
			const permissionFields = Array.from(
				permissionsForService.querySelectorAll("[type=checkbox]"),
			);
			const selectedPermissions = permissionFields.filter(
				(permissionField) => permissionField.checked,
			);

			const [serviceName, serviceId] = service.value.split(":");
			this.#state.selectedServicesAndPermissions.push({
				service: {
					name: serviceName,
					id: serviceId,
				},
				permissions: selectedPermissions.map((permission) => {
					const [permissionName, permissionId] = permission.value.split(":");
					return {
						name: permissionName,
						id: permissionId,
					};
				}),
			});
		});

		this.#state.formData.services = this.#state.selectedServicesAndPermissions;
	}

	/**
	 * Creates a new user by sending a POST request to the form's action URL.
	 * @private
	 * @returns {void}
	 */
	async #createUser() {
		const { cloudUserManagement, form, submitButton, dialog } = this.#elements;
		const { formData } = this.#state;

		submitButton.classList.add("Button--loading");
		submitButton.disabled = true;

		const payload = {
			FirstName: formData.firstName,
			Surname: formData.surname,
			Email: formData.email,
			Position: formData.position,
			Note: formData.note,
			Services: formData.services,
		};

		try {
			const jsonResponse = await doFetch(payload, form.action);

			const { message, ticket_url, success } = jsonResponse;

			if (success) {
				cloudUserManagement.showToast(`<a href="${ticket_url}">${message}</a>`);
				dialog.closeUserManagementDialog();
			} else {
				cloudUserManagement.showToast(message, "critical");
			}
		} catch {
			cloudUserManagement.showToast(
				getLocalizedMessage("global.error.general"),
				"critical",
			);
		} finally {
			submitButton.classList.remove("Button--loading");
			submitButton.disabled = false;
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListeners() {
		const { form } = this.#elements;
		const { service } = this.#fieldNames;

		form.addEventListener("change", (event) => {
			const target = event.target;
			const isChecked = target.checked;

			target.setAttribute("aria-expanded", isChecked);

			if (target.name === service && !isChecked) {
				const fieldsetId = target.getAttribute("aria-controls");
				const permissionsFieldset = document.getElementById(fieldsetId);
				const permissions = Array.from(
					permissionsFieldset.querySelectorAll("[type=checkbox]"),
				);

				permissions.forEach((permission) => {
					permission.checked = false;
				});
			}
		});

		form.addEventListener("submit", async (event) => {
			event.preventDefault();
			const isValid = this.#isValid();

			if (!isValid) {
				return;
			}

			this.#setFormData();

			await this.#createUser();
		});
	}
}

customElements.define("finstral-create-user", FinstralCreateUser);
