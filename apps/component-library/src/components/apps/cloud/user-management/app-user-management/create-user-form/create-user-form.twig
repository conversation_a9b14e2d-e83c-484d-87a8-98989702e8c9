{{ attach_library('finstral_global/apps-user-management-create-user-form') }}

{% set user_info = [
	{
		label: "create_user_form.label.first_name"|tc,
		field_name: "create-user-first-name",
		type: "text",
	},
	{
		label: "create_user_form.label.surname"|tc,
		field_name: "create-user-surname",
		type: "text",
	},
	{
		label: "create_user_form.label.email"|tc,
		field_name: "create-user-email",
		type: "email",
	},
	{
		label: "create_user_form.label.position"|tc,
		field_name: "create-user-position",
		type: "text",
	},
] %}

<finstral-create-user class="CreateUserForm">
	<form
		class="CreateUserForm-form"
		action="{{ form_action }}"
		method="POST"
		novalidate
	>
		<div class="CreateUserForm-header">
			<h2 class="u-typo-TextL" id="create-user-form-header-title">{{ "create_user_form.title"|tc }}</h2>
			<p class="CreateUserForm-copy u-typo-TextM">{{ "create_user_form.copy"|tc }}</p>
		</div>

		{% include "@elements/form-validation-errors/form-validation-errors.twig" %}

		<fieldset class="CreateUserForm-userInfo">
			<legend>
				<span class="u-hiddenVisually">{{ "create_user_form.user_info"|tc }}</span>
			</legend>
			{% for item in user_info %}
				<div class="Form-fieldGroup CreateUserForm-userInfoInput">
					{% include "@elements/form-element/label/label.twig" with {
						for: item.field_name,
						title: item.label,
						required: loop.last ? false : true,
					} only %}

					{% include "@elements/form-element/input/input.twig" with {
						additional_attributes: loop.last ? [] : [
							["aria-describedby", item.field_name ~ "-error"],
						],
						id: item.field_name,
						name: item.field_name,
						type: item.type,
						required: loop.last ? false : true,
					} only %}

					{% if not loop.last %}
						{% include "@elements/form-validation-errors/error-container/error-container.twig" with {
							id: item.field_name ~ "-error",
						} only %}
					{% endif %}
				</div>
			{% endfor %}
		</fieldset>

		<section>
			<h3 class="u-typo-TextL" id="create-user-services-heading">{{ "create_user_form.services_title"|tc }}*</h3>
			{% include "@elements/form-validation-errors/error-container/error-container.twig" with {
				id: "cloud-services-error",
			} only %}
			<ul>
				{% for service in services %}
					{% set normalized_service = service.label|lower|split(" ")|join("-") %}
					{% set fieldset_id = normalized_service ~ "-" ~ service.id ~ "-fieldset" %}

					<li class="FormElement Option Checkbox">
						<div class="CreateUserForm-service">
							{% include "@elements/form-element/option/option.twig" with {
								id: normalized_service ~ "-" ~ service.id,
								name: normalized_service ~ "-js-service",
								type: "checkbox",
								value: service.label ~ ':' ~ service.id,
								additional_attributes: [
									["aria-controls", fieldset_id],
									["aria-expanded", false],
									["data-service", normalized_service],
								],
							} only %}

							{% include "@elements/form-element/label/label.twig" with {
								for: normalized_service ~ "-" ~ service.id,
								title: service.label,
							} only %}
						</div>

						<fieldset class="CreateUserForm-permissions" id="{{ fieldset_id }}">
							<legend>
								<span class="u-hiddenVisually">{{ "create_user_form.permissions"|tc }}</span>
							</legend>
							{% include "@elements/form-validation-errors/error-container/error-container.twig" with {
								id: fieldset_id ~ "-error",
							} only %}
							<ul class="CreateUserForm-permissionList">
								{% for permission in service.permissions %}

									{% set normalized_permission = permission.label|lower|split(" ")|join("-") %}
									{% set input_id = "permission-" ~ service.id ~ "-" ~ normalized_permission %}

									<li class="FormElement Option Checkbox">
										{% include "@elements/form-element/option/option.twig" with {
											id: input_id,
											name: normalized_service ~ "-" ~ normalized_permission,
											value: permission.label ~ ':' ~ permission.value,
											type: "checkbox",
										} only %}

										{% include "@elements/form-element/label/label.twig" with {
											for: input_id,
											title: permission.label,
										} only %}
									</li>
								{% endfor %}
							</ul>
						</fieldset>
					</li>
				{% endfor %}
			</ul>
		</section>
		<div>
			{% include "@elements/form-element/label/label.twig" with {
				for: "textarea",
				title: "create_user_form.label.note"|tc,
			} only %}

			{% include "@elements/form-element/textarea/textarea.twig" with {
				id: "textarea",
				name: "create-user-note",
				classes: ["CreateUserForm-textarea"],
			} only %}
		</div>

		{% include "@elements/button/button.twig" with {
			classes: ["CreateUserForm-button"],
			label: "create_user_form.submit_button"|tc,
			type: "submit",
		} only %}
	</form>
</finstral-create-user>
