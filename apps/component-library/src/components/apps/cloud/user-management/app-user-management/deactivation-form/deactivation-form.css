/** @define DeactivationForm; */

.DeactivationForm-form {
	margin-block-start: var(--size-40);
}

.DeactivationForm-copy {
	color: var(--shared-color-text-secondary);
	margin-block-start: var(--size-16);
}

.DeactivationForm-button {
	inline-size: 100%;
	margin-block-start: var(--size-24);
}

.DeactivationForm-fieldGroup:first-child {
	margin-block-end: var(--size-24);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.DeactivationForm-fieldGroup label {
	margin-block-end: var(--size-8);
}
