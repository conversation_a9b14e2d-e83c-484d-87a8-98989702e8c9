import { doFetch } from "../../../../../../js/utils/helper/fetch";
import { getEntryFromStorage } from "../../../../../../js/utils/helper/store.js";
import { getLocalizedMessage } from "../../../../../../js/utils/helper/i18n.js";
import { resetFormErrorState } from "../../../../../../js/utils/helper/forms";

export default class FinstralDeactivateUser extends HTMLElement {
	static #selectors = {
		cloudUserManagement: "cloud-user-management",
		form: ".DeactivationForm-form",
		submitButton: ".DeactivationForm-button",
		select: "#deactivation-form-select",
		userManagementOpenDialog: "user-management-dialog:has(dialog[open])",
		userManagementToastTmpl: "#user-management-toast-tmpl",
		userManagementToaster: "#cloud-user-management-toaster",
	};

	#elements;

	#fieldNames = {
		deactivationReasons: "deactivation-reasons",
		deactivationNote: "deactivation-note",
	};

	#localState;

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			cloudUserManagement: /** @type {HTMLElement} */ (
				document.querySelector(
					FinstralDeactivateUser.#selectors.cloudUserManagement,
				)
			),
			form: /** @type {HTMLFormElement} */ (
				this.querySelector(FinstralDeactivateUser.#selectors.form)
			),
			select: /** @type {HTMLSelectElement} */ (
				this.querySelector(FinstralDeactivateUser.#selectors.select)
			),
			submitButton: /** @type {HTMLButtonElement} */ (
				this.querySelector(FinstralDeactivateUser.#selectors.submitButton)
			),
		};
	}

	/**
	 * Creates an instance of FinstralDeactivateUser.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		if (this.#elements.form) {
			this.#init();
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#init() {
		const userManagementState = getEntryFromStorage("userManagementState");
		this.#localState = userManagementState;

		this.#addEventListener();
	}

	/**
	 * Validates the form.
	 * @returns {boolean} True if the form is valid, otherwise false.
	 */
	#isValid() {
		const { select } = this.#elements;

		resetFormErrorState(this);

		let isValid = true;

		if (!select.checkValidity()) {
			isValid = false;

			select.setAttribute("aria-invalid", true);
			select.nextElementSibling.querySelector("span").textContent =
				getLocalizedMessage("global.form.errors.option.required.generic");

			select.focus();
		}

		return isValid;
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setFormData() {
		const { form, select } = this.#elements;
		const { deactivationNote } = this.#fieldNames;
		const formData = new FormData(form);

		this.#localState.formData = {};

		this.#localState.formData.deactivationReasons = {
			id: select.value,
			label: select.selectedOptions[0].textContent,
		};

		if (formData.get(deactivationNote)) {
			this.#localState.formData.deactivationNote =
				formData.get(deactivationNote);
		}
	}

	/**
	 * Deactivates the user.
	 * @private
	 * @returns {Promise<void>}
	 */
	async #deactivateUser() {
		const { cloudUserManagement, form, submitButton } = this.#elements;
		const { isEmployee, formData, userDetail } = this.#localState;
		const dialog = /** @type {HTMLDialogElement} */ document.querySelector(
			FinstralDeactivateUser.#selectors.userManagementOpenDialog,
		);

		submitButton.classList.add("Button--loading");
		submitButton.disabled = true;

		let payload = {
			Email: userDetail.email,
			Fullname: userDetail.name,
			Login: userDetail.login,
			Reason: formData.deactivationReasons,
			UserID: userDetail.id,
		};

		if (formData.deactivationNote) {
			payload.Note = formData.deactivationNote;
		}

		try {
			const jsonResponse = await doFetch(payload, form.action);
			const { message, ticket_id, ticket_url, success } = jsonResponse;

			const ticketIdMsg = getLocalizedMessage(
				"global.ticket.reference",
			).replace("@ticket_id", ticket_id);
			const employeeSuccessMsg = `${message} ${ticketIdMsg}`;

			const toastSuccessMsg = isEmployee
				? employeeSuccessMsg
				: `<a href="${ticket_url}">${message}</a>`;

			if (success) {
				cloudUserManagement.showToast(toastSuccessMsg);
				dialog.closeUserManagementDialog();
			} else {
				cloudUserManagement.showToast(message, "critical");
			}
		} catch {
			cloudUserManagement.showToast(
				getLocalizedMessage("global.error.general"),
				"critical",
			);
		} finally {
			submitButton.classList.remove("Button--loading");
			submitButton.disabled = false;
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListener() {
		const { form } = this.#elements;

		form.addEventListener("submit", async (event) => {
			event.preventDefault();

			const isValid = this.#isValid();

			if (!isValid) {
				return;
			}

			this.#setFormData();

			await this.#deactivateUser();
		});
	}
}

customElements.define("finstral-deactivate-user", FinstralDeactivateUser);
