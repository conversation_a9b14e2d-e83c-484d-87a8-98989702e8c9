{{ attach_library('finstral_global/apps-user-management-deactivation-form') }}

<finstral-deactivate-user class="DeactivationForm">
	<h3 class="u-typo-TextL DeactivationForm-title">{{ "deactivation_form.title"|tc }}</h3>
	<p class="DeactivationForm-copy u-typo-TextM">{{ "deactivation_form.copy"|tc }}</p>
	<form
		class="DeactivationForm-form"
		action="{{ form_action }}"
		method="POST"
		novalidate
	>
		<div class="Form-fieldGroup DeactivationForm-fieldGroup">
			{% include "@elements/form-element/label/label.twig" with {
				for: "deactivation-form-select",
				title: "deactivation_form.select_label"|tc,
				required: true,
			} only %}

			{% include "@elements/select/select.twig" with {
				id: "deactivation-form-select",
				name: "deactivation-reasons",
				options: deactivation_reasons,
				required: true,
			} only %}

			{% include "@elements/form-validation-errors/error-container/error-container.twig" with {
				id: "deactivate-reasons-error",
			} only %}
		</div>

		<div class="DeactivationForm-fieldGroup">
			{% include "@elements/form-element/label/label.twig" with {
				for: "textarea",
				title: "Note",
			} only %}

			{% include "@elements/form-element/textarea/textarea.twig" with {
				classes: ["DeactivationForm-textarea"],
				id: "textarea",
				name: "deactivation-note",
			} only %}
		</div>

		{% include "@elements/button/button.twig" with {
			classes: ["DeactivationForm-button"],
			label: "deactivation_form.submit_button"|tc,
			type: "submit",
		} only %}
	</form>
</finstral-deactivate-user>
