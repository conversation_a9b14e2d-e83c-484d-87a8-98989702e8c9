/** @define UserManagementListDetails; */

/* Properties */

.UserManagementListDetails-properties {
	align-items: center;
	column-gap: var(--size-8);
	display: grid;
	grid-template-columns: min-content 1fr;
	margin-block-end: var(--size-24);
	row-gap: var(--size-24);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.UserManagementListDetails-pendingMessage a:link {
	text-decoration: underline;
}

/* Disclosure */

.UserManagementListDetails-disclosure {
	border-block-start: var(--border-distinct);
}

.UserManagementListDetails-disclosure[open] {
	padding-block-end: var(--size-24);
}

.UserManagementListDetails-disclosureSummary {
	align-items: center;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	padding-block: var(--size-24);
}

.UserManagementListDetails-disclosureArrow {
	pointer-events: none;
}

.UserManagementListDetails-disclosure[open]
	.UserManagementListDetails-disclosureArrow {
	rotate: 180deg;
}

.UserManagementListDetails-disclosureSwitch {
	block-size: var(--size-24);
	inline-size: var(--size-40);
	pointer-events: none;
}
