<dl class="UserManagementListDetails-properties u-typo-TextM">
	<dt>
		<span class="u-hiddenVisually">{{ "user_management.label_email"|tc }}</span>
		{% include "@elements/icon/icon.twig" with {
			name: "email"
		} only %}
	</dt>
	<dd>
		<a class="u-link" href="mailto:{{ email }}" target="_blank" rel="noopener noreferrer">
			{{ user.email }}
		</a>
	</dd>

	<dt>
		<span class="u-hiddenVisually">{{ "user_management.label_login"|tc }}</span>
		{% include "@elements/icon/icon.twig" with {
			name: "login"
		} only %}
	</dt>
	<dd>
		{{ user.login }}
	</dd>

	<dt>
		<span class="u-hiddenVisually">{{ "user_management.label_role"|tc }}</span>
		{% include "@elements/icon/icon.twig" with {
			name: "job"
		} only %}
	</dt>
	<dd>
		{{ user.role }}
	</dd>
</dl>

{% for service in user.services %}
	<details class="UserManagementListDetails-disclosure">
		<summary
			class="UserManagementListDetails-disclosureSummary u-typo-TextM u-outline"
			data-form="service-permissions"
			data-login="{{ user.login }}"
			data-service="{{ service.id ~ ':' ~ service.label }}">
			{{ service.label }}

			{% include "@elements/icon/icon.twig" with {
				name: "chevron_down",
				classes: ["UserManagementListDetails-disclosureArrow"],
			} only %}
		</summary>
	</details>
{% endfor %}

<details class="UserManagementListDetails-disclosure">
	<summary
		class="UserManagementListDetails-disclosureSummary u-typo-TextM u-outline"
		data-form="deactivate-user"
		data-login="{{ user.login }}"
		data-status="{{ user.user_status.status }}">

		{{ "user_management.label_active"|tc }}

		{% set status_icons = {
			active: "switch_on",
			pending: "switch_pending",
		} %}

		{% include "@elements/icon/icon.twig" with {
			classes: ["UserManagementListDetails-disclosureSwitch"],
			name: status_icons[user.user_status.status],
		} only %}
	</summary>

	{% if user.user_status.status == "pending" %}
		<p class="UserManagementListDetails-pendingMessage">
			{{ "global.ticket.processing"|tc }}
			<a href="{{ user.user_status.ticket.url }}">{{ "global.ticket.view"|tc }}</a>.
		</p>
	{% elseif read_only %}
		<p class="UserManagementListDetails-message">
			{{ "global.message.readonly"|tc({"@support_email": support_email}) }}
		</p>
	{% endif %}
</details>
