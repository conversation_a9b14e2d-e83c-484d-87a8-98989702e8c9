@import url("./_details/_details.css");

/** @define UserManagementList; */

.UserManagementList {
	display: flex;
	flex-direction: column;
}

/* Item */

.UserManagementList-item {
	border-block-start: var(--border-subtle);
}

.UserManagementList-item:last-child {
	border-block-end: var(--border-subtle);
}

/* Button */

.UserManagementList-button {
	align-items: center;
	display: grid;
	gap: var(--size-12);
	grid-template-columns: var(--size-64) 1fr;
	inline-size: 100%;
	padding-block: var(--size-12);
	text-align: start;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.UserManagementList-button > * {
	pointer-events: none;
}

.UserManagementList-buttonImage {
	border-radius: 50%;
	overflow: clip;
}

/* Dialog */

.UserManagementList-dialog {
	inline-size: 100%;
	inset-block-end: 0;
	inset-block-start: auto;
	padding-block-end: 0;
}
