<ul class="UserManagementList">
	{% for user in users %}
		<li class="UserManagementList-item">
			{% set avatar %}
				{% include "@elements/image/image.twig" with {
					alt: "",
					height: 128,
					uri: user.avatar_url,
					width: 128,
				} only %}
			{% endset %}

			{% set trigger %}
				<button
					class="UserManagementList-button"
					type="button"
					data-login="{{ user.login }}"
					data-mobile
				>
					<span class="UserManagementList-buttonImage">{{ avatar }}</span>
					<span class="u-typo-TextM">{{ user.name }}</span>
				</button>
			{% endset %}

			{% set content %}
				{% include "@apps/cloud/user-management/app-user-management/list/_details/_details.twig" with {
					read_only: read_only,
					support_email: support_email,
					user: user,
				} only %}
			{% endset %}

			{% include "@apps/cloud/user-management/app-user-management/_dialog/_dialog.twig" with {
				classes: ["UserManagementList-dialog"],
				trigger: trigger,
				content: content,
				avatar_url: user.avatar_url,
				name: user.name,
				unit: user.unit,
			} only %}
		</li>
	{% endfor %}
</ul>
