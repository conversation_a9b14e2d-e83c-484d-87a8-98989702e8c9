read_only: false

users:
  - avatar_url: /build/assets/img/dummy/avatar/2.webp
    id: 2
    name: <PERSON>
    unit: Admin SICURCASA
    email: jess<PERSON>@sicurcasa.it
    login: <EMAIL>
    role: <PERSON><PERSON><PERSON>, Composer
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: false
            label: Admin
            value: 1
          - active: false
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: false
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: true
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: true
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: true
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: true
            label: Delete
            value: 12

    user_status:
      status: active

  - avatar_url: /build/assets/img/dummy/avatar/3.webp
    id: 3
    name: <PERSON>
    unit: Admin SICURCASA
    email: <EMAIL>
    login: <EMAIL>
    role: <PERSON><PERSON><PERSON>, Composer
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: false
            label: Admin
            value: 1
          - active: true
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: false
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: false
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: false
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: false
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: false
            label: Delete
            value: 12

    user_status:
      status: pending
      ticket:
        id: 123456
        url: /ticket-overview/123456

  - avatar_url: /build/assets/img/dummy/avatar/4.webp
    id: 4
    name: Patrizia Ciampolini
    unit: Admin SICURCASA
    email: <EMAIL>
    login: <EMAIL>
    role: Techniker, Composer
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: false
            label: Admin
            value: 1
          - active: true
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: false
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: false
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: false
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: false
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: false
            label: Delete
            value: 12

    user_status:
      status: pending
      ticket:
        id: 789012
        url: /ticket-overview/789012

  - avatar_url: /build/assets/img/dummy/avatar/5.webp
    id: 5
    name: Mouen Silianane
    unit: Admin SICURCASA
    email: <EMAIL>
    login: 2SRA1MF
    role: Verkauf
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: true
            label: Admin
            value: 1
          - active: true
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: true
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: true
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: true
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: true
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: true
            label: Delete
            value: 12

    user_status:
      status: pending
      ticket:
        id: 677589
        url: /ticket-overview/677589

support_email: <EMAIL>
