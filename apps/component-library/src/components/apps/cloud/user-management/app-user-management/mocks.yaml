deactivation_reasons:
  - label: Reason
    selected: true
  - label: Employee changes roles
    value: 1
  - label: Employee leaves the company
    value: 2

form_create_user_api: user-management/user/create
form_deactivate_user_api: user-management/user/deactivate
form_update_permissions_api: user-management/permissions/update

is_employee: false

services:
  - id: 1
    label: Contacts
    permissions:
      - label: Admin
        value: 1
      - label: Read and Write
        value: 2
      - label: Read
        value: 3
      - label: Delete
        value: 4

  - id: 2
    label: Composer
    permissions:
      - label: Admin
        value: 5
      - label: Read and Write
        value: 6
      - label: Read
        value: 7
      - label: Delete
        value: 8

  - id: 3
    label: Webmonitor
    permissions:
      - label: Admin
        value: 9
      - label: Read and Write
        value: 10
      - label: Read
        value: 11
      - label: Delete
        value: 12
      - label: Office staff
        value: 13
      - label: View Order
        value: 14
      - label: Customer mail
        value: 15

support_email: <EMAIL>

self:
  - avatar_url: /build/assets/img/dummy/avatar/1.webp
    id: 1
    name: <PERSON><PERSON><PERSON>
    unit: Admin SICURCASA
    email: tlagoma<PERSON><PERSON>@finstral.com
    login: tlagoma<PERSON><PERSON>@finstral.com
    role: <PERSON>niker, Composer
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: true
            label: Admin
            value: 1
          - active: true
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: true
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: true
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: true
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: true
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: true
            label: Delete
            value: 12

    user_status:
      status: active

team:
  - avatar_url: /build/assets/img/dummy/avatar/2.webp
    id: 2
    name: Jessica Lo Presti
    unit: Admin SICURCASA
    email: <EMAIL>
    login: <EMAIL>
    role: Techniker, Composer
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: false
            label: Admin
            value: 1
          - active: false
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: false
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: true
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: true
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: true
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: true
            label: Delete
            value: 12

    user_status:
      status: active

  - avatar_url: /build/assets/img/dummy/avatar/3.webp
    id: 3
    name: Claudia Ricci
    unit: Admin SICURCASA
    email: <EMAIL>
    login: <EMAIL>
    role: Techniker, Composer
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: false
            label: Admin
            value: 1
          - active: true
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: false
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: false
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: false
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: false
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: false
            label: Delete
            value: 12

    user_status:
      status: pending
      ticket:
        id: 123456
        url: /ticket-overview/123456

  - avatar_url: /build/assets/img/dummy/avatar/4.webp
    id: 4
    name: Patrizia Ciampolini
    unit: Admin SICURCASA
    email: <EMAIL>
    login: <EMAIL>
    role: Techniker, Composer
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: false
            label: Admin
            value: 1
          - active: true
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: false
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: false
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: false
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: false
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: false
            label: Delete
            value: 12

    user_status:
      status: pending
      ticket:
        id: 789012
        url: /ticket-overview/789012

  - avatar_url: /build/assets/img/dummy/avatar/5.webp
    id: 5
    name: Mouen Silianane
    unit: Admin SICURCASA
    email: <EMAIL>
    login: 2SRA1MF
    role: Verkauf
    services:
      - id: 1
        label: Contacts
        permissions:
          - active: true
            label: Admin
            value: 1
          - active: true
            label: Read and Write
            value: 2
          - active: true
            label: Read
            value: 3
          - active: true
            label: Delete
            value: 4

      - id: 2
        label: Composer
        permissions:
          - active: true
            label: Admin
            value: 5
          - active: true
            label: Read and Write
            value: 6
          - active: true
            label: Read
            value: 7
          - active: true
            label: Delete
            value: 8

      - id: 3
        label: Webmonitor
        permissions:
          - active: true
            label: Admin
            value: 9
          - active: true
            label: Read and Write
            value: 10
          - active: true
            label: Read
            value: 11
          - active: true
            label: Delete
            value: 12

    user_status:
      status: pending
      ticket:
        id: 677589
        url: /ticket-overview/677589
