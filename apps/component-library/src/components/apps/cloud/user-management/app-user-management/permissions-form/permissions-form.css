/** @define PermissionsForm; */

.PermissionsForm {
	display: block;
}

.PermissionsForm-title {
	margin-block-end: var(--PermissionForm-spacing);
}

.PermissionsForm-option {
	padding-block: var(--PermissionForm-spacing);
}

.PermissionsForm-button {
	inline-size: 100%;
	margin-block-start: var(--PermissionForm-spacing);
}

@media (width < 64em) {
	.PermissionsForm {
		--PermissionForm-spacing: var(--size-12);
	}
}

@media (width >= 64em) {
	.PermissionsForm {
		--PermissionForm-spacing: var(--size-24);
	}

	.PermissionsForm-option:not(:last-child) {
		border-block-end: var(--border-distinct);
	}

	.PermissionsForm-option:first-child {
		padding-block-start: var(--size-16);
	}

	.PermissionsForm-option:last-child {
		padding-block-end: var(--size-16);
	}
}

.PermissionsForm-lead {
	color: var(--shared-color-text-secondary);
	margin-block-end: var(--size-24);
	margin-block-start: var(--size-16);
}
