import { doFetch } from "../../../../../../js/utils/helper/fetch";
import { getEntryFromStorage } from "../../../../../../js/utils/helper/store.js";
import { getLocalizedMessage } from "../../../../../../js/utils/helper/i18n.js";

export default class CloudPermissionsForm extends HTMLElement {
	static #selectors = {
		cloudUserManagement: "#cloud-user-management",
		form: "cloud-permissions-form form",
		permissionsList: "#permissions-list",
		submitButton: ".PermissionsForm-button",
		userManagementOpenDialog: "user-management-dialog:has(dialog[open])",
	};

	#elements;

	#localState;

	// eslint-disable-next-line jsdoc/require-jsdoc
	constructor() {
		super();

		this.#elements = this.#getElements();

		if (this.#elements.form && this.#elements.permissionsList) {
			this.#init();
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#init() {
		const userManagementState = getEntryFromStorage("userManagementState");
		const [serviceid, service] = userManagementState.service.split(":");

		userManagementState.service = service;
		userManagementState.serviceid = serviceid;

		this.#localState = userManagementState;

		const { permissions } = this.#getPermisions();

		permissions.forEach((permission) => {
			this.#appendPermission(permission);
		});

		this.#addEventListeners();
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			cloudUserManagement: /** @type {HTMLElement} */ document.querySelector(
				CloudPermissionsForm.#selectors.cloudUserManagement,
			),
			form: /** @type {HTMLFormElement} */ this.querySelector(
				CloudPermissionsForm.#selectors.form,
			),
			permissionsList: /** @type {HTMLUListElement} */ this.querySelector(
				CloudPermissionsForm.#selectors.permissionsList,
			),
			submitButton: /** @type {HTMLButtonElement} */ this.querySelector(
				CloudPermissionsForm.#selectors.submitButton,
			),
		};
	}

	/**
	 * Appends a permission field (checkbox and label) to the permissions list in the form.
	 * @param {object} permission - The permission entry data.
	 * @example
	 * ```js
	 * {
	 * 	active: boolean,
	 *  id: number,
	 *  label: string,
	 * }
	 * ```
	 * @returns {void}
	 * @private
	 */
	#appendPermission(permission) {
		const { permissionsList } = this.#elements;
		const { isReadOnly } = this.#localState;
		const { active, label, value } = permission;

		const checked = active ? "checked" : "";
		const disabled = isReadOnly ? "disabled" : "";
		const formattedPermission = label.toLowerCase().split(" ").join("-");
		const permissionId = `${formattedPermission}-${value}`;

		const html = `
			<li class="FormElement Option Checkbox PermissionsForm-option">
				<input id="${this.#localState.userDetail.id}-${permissionId}" name="${formattedPermission}" type="checkbox" value="${label}" ${checked} ${disabled}>
				<label class="FormElementLabel u-typo-TextM" for="${this.#localState.userDetail.id}-${permissionId}">${label}</label>
			</li>
		`;

		permissionsList.insertAdjacentHTML("beforeend", html);
	}

	/**
	 * Retrieves the permissions for a user from the cloud user management service.
	 * @returns {object} The permissions for the specified user and service.
	 * @private
	 */
	#getPermisions() {
		const { cloudUserManagement } = this.#elements;
		const { service, userDetail } = this.#localState;

		return cloudUserManagement.getServiceForUser(userDetail.login, service);
	}

	/**
	 * Updates the service permissions for the user.
	 * @private
	 * @returns {Promise<void>}
	 */
	async #updatePermissions() {
		const { cloudUserManagement, form, submitButton } = this.#elements;
		const { isEmployee, serviceid, userDetail } = this.#localState;

		const dialog = document.querySelector(
			CloudPermissionsForm.#selectors.userManagementOpenDialog,
		);

		submitButton.classList.add("Button--loading");
		submitButton.disabled = true;

		const deselectedFields = Array.from(
			form.querySelectorAll("[type=checkbox]:not(:checked)"),
		);
		const selectedFields = Array.from(
			form.querySelectorAll("[type=checkbox]:checked"),
		);

		let payload = {
			Email: userDetail.email,
			Fullname: userDetail.name,
			Login: userDetail.login,
			Permissions: {
				add: selectedFields.map((field) => field.value),
				remove: deselectedFields.map((field) => field.value),
			},
			Service: serviceid,
			UserID: userDetail.id,
		};

		try {
			const jsonResponse = await doFetch(payload, form.action);
			const { message, ticket_id, ticket_url, success } = jsonResponse;

			const ticketIdMsg = getLocalizedMessage(
				"global.ticket.reference",
			).replace("@ticket_id", ticket_id);
			const employeeSuccessMsg = `${message} ${ticketIdMsg}`;

			const toastSuccessMsg = isEmployee
				? employeeSuccessMsg
				: `<a href="${ticket_url}">${message}</a>`;

			if (success) {
				cloudUserManagement.showToast(toastSuccessMsg);
				dialog.closeUserManagementDialog();
			} else {
				cloudUserManagement.showToast(message, "critical");
			}
		} catch {
			cloudUserManagement.showToast(
				getLocalizedMessage("global.error.general"),
				"critical",
			);
		} finally {
			submitButton.classList.remove("Button--loading");
			submitButton.disabled = false;
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListeners() {
		const { form } = this.#elements;

		form.addEventListener("submit", async (event) => {
			event.preventDefault();

			await this.#updatePermissions();
		});
	}
}

customElements.define("cloud-permissions-form", CloudPermissionsForm);
