{{ attach_library('finstral_global/apps-user-management-permissions-form') }}

{% set is_read_only = form_action is empty %}

<cloud-permissions-form class="PermissionsForm">
	<form
		{% if not is_read_only %} action="{{ form_action }}" {% endif %}
		method="POST"
	>
		<h3 class="PermissionsForm-title u-typo-TextM">{{ "permissions_form.title"|tc }}</h3>

		{% if is_read_only %}
			<p class="PermissionsForm-lead u-typo-TextM">{{ "user_management.read_only.permissions_message"|tc({"@support_email": support_email}) }}</p>
		{% endif %}

		<ul id="permissions-list"></ul>

		{% if not is_read_only %}
			{% include "@elements/button/button.twig" with {
				classes: ["PermissionsForm-button"],
				label: "permissions_form.submit_button"|tc,
				type: "submit",
			} only %}
		{% endif %}
	</form>
</cloud-permissions-form>
