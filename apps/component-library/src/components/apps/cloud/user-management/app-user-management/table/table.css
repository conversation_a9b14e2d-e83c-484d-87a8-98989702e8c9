/** @define UserManagementTable; */

/**
 * The table already aligns the cell content with `vertical-align: middle`.
 * This creates an issue when some cell content uses flex layout for alignment,
 * because `vertical-align` aligns relative to text.
 *
 * To avoid a slight mis-alignment between cell content that uses flex layout,
 * all content needs to use flex layout for alignment.
 */
.UserManagementTable-item {
	align-items: center;
	display: flex;
	gap: var(--size-12);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.UserManagementTable-item--name img {
	block-size: var(--size-40);
	border-radius: 50%;
	inline-size: var(--size-40);

	/* Reset `max-inline-size` to avoid table cell collapsing. */
	max-inline-size: none;
}

/* Button */

.UserManagementTable-button {
	display: block;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.UserManagementTable-button svg {
	pointer-events: none;
}

.UserManagementTable-buttonSwitch {
	block-size: var(--size-24);
	inline-size: var(--size-40);
}

/* Dialog */

.UserManagementTable-dialog {
	inline-size: 23.4375rem;
}
