{% set head = [
	{label: "user_management.label_name"|tc},
	{label: "user_management.label_email"|tc},
	{label: "user_management.label_login"|tc},
	{label: "user_management.label_role"|tc},
] %}

{% for service in users[0].services %}
	{% set head = head|merge([
		{label: service.label},
	]) %}
{% endfor %}

{% set head = head|merge([
	{label: "user_management.label_active"|tc},
]) %}

{% set body = [] %}

{% for user in users %}
	{% set avatar %}
		{% include "@elements/image/image.twig" with {
			alt: "",
			height: 128,
			uri: user.avatar_url,
			width: 128,
		} only %}
	{% endset %}

	{% set name %}
		<div class="UserManagementTable-item UserManagementTable-item--name u-typo-TextS">
			{{ avatar }}
			{{ user.name }}
		</div>
	{% endset %}

	{% set email %}
		<div class="UserManagementTable-item u-typo-TextS">
			<a class="u-link" href="mailto:{{ user.email }}" target="_blank" rel="noopener noreferrer">
				{{ user.email }}
			</a>
		</div>
	{% endset %}

	{% set login %}
		<div class="UserManagementTable-item u-typo-TextS">
			{{ user.login }}
		</div>
	{% endset %}

	{% set role %}
		<div class="UserManagementTable-item u-typo-TextS">
			{{ user.role }}
		</div>
	{% endset %}

	{% set row = [
		{content: name},
		{content: email},
		{content: login},
		{content: role},
	] %}

	{% for service in user.services %}
		{% set content %}
			{% set service_label_id = service.label|lower ~ "-trigger-label-" ~ random() %}
			<div class="UserManagementTable-item">
				<button
					class="UserManagementTable-button"
					type="button"
					aria-labelledby="{{ service_label_id }}"
					data-dialog="service-permissions"
					data-login="{{ user.login }}"
					data-service="{{ service.id ~ ':' ~ service.label }}"
				>
					<span id="{{ service_label_id }}" hidden>
						{{ "user_management.permissions_button"|tc({"@service": service.label, "@user": user.name}) }}
					</span>

					{% include "@elements/icon/icon.twig" with {
						name: "settings"
					} only %}
				</button>
			</div>
		{% endset %}

		{% set row = row|merge([
			{content: content},
		]) %}
	{% endfor %}

	{% set active %}
		<div class="UserManagementTable-item">
			{% set active_label_id = "active-trigger-label-" ~ random() %}
			{% set user_status = user.user_status.status %}
			{% set status_icons = {
				active: "switch_on",
				pending: "switch_pending",
				inactive: "switch_off",
			} %}

			<button
				class="UserManagementTable-button"
				type="button"
				data-dialog="deactivate-user"
				aria-labelledby="{{ active_label_id }}"
				data-login="{{ user.login }}"
				data-status="{{ user_status }}"
			>
				<span id="{{ active_label_id }}" hidden>
					{{ "user_management.active_button"|tc }}
				</span>

				{% include "@elements/icon/icon.twig" with {
					classes: ["UserManagementTable-buttonSwitch"],
					name: status_icons[user_status],
				} only %}
			</button>
		</div>
	{% endset %}

	{% set row = row|merge([
		{content: active},
	]) %}

	{% set body = body|merge([row]) %}
{% endfor %}

<div class="UserManagementTable">
	{% include "@elements/table/table.twig" with {
		head: head,
		body: body,
	} only %}
</div>
