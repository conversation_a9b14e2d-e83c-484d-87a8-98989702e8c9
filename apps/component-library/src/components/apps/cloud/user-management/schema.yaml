$schema: http://json-schema.org/draft-07/schema
$id: https://finstral.com/apps/cloud/user-management

$defs:
  user_data:
    $anchor: UserDataSchema
    type: array
    items:
      type: object
      required:
        - avatar_url
        - id
        - name
        - unit
        - email
        - login
        - services
        - user_status
      additionalProperties: false
      properties:
        avatar_url:
          type: string
          format: uri-reference
        id:
          type: number
        name:
          type: string
        unit:
          type: string
        email:
          type: string
        login:
          type: string
        role:
          type: string
        services:
          type: array
          items:
            additionalProperties: false
            type: object
            required:
              - id
              - label
              - permissions
            properties:
              id:
                type: number
              label:
                type: string
              permissions:
                type: array
                items:
                  additionalProperties: false
                  type: object
                  required:
                    - active
                    - label
                    - value
                  properties:
                    active:
                      type: boolean
                    label:
                      type: string
                    value:
                      type: number
        user_status:
          additionalProperties: false
          type: object
          required:
            - status
          properties:
            status:
              type: string
              enum:
                - active
                - pending
            ticket:
              additionalProperties: false
              type: object
              required:
                - id
                - url
              properties:
                id:
                  type: number
                url:
                  type: string
                  format: uri-reference

  deactivation_reasons:
    $anchor: DeactivationReasonsSchema
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - label
      properties:
        label:
          type: string
        selected:
          type: boolean
        value:
          type: number

  services:
    $anchor: ServicesSchema
    type: array
    items:
      service:
        type: object
        additionalProperties: false
        required:
          - id
          - label
          - permissions
        properties:
          id:
            type: number
          label:
            type: string
          permissions:
            type: array
            items:
              additionalProperties: false
              type: object
              required:
                - label
                - value
              properties:
                label:
                  type: string
                value:
                  type: string

type: object

required:
  - breadcrumb
  - deactivation_reasons
  - is_employee
  - services
  - self
  - support_email

additionalProperties: false

properties:
  breadcrumb:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - text
      properties:
        text:
          type: string
        url:
          type: string
          format: uri-reference

  deactivation_reasons:
    $ref: "#DeactivationReasonsSchema"

  form_create_user_api:
    format: uri-reference
    type: string

  form_deactivate_user_api:
    format: uri-reference
    type: string

  form_update_permissions_api:
    format: uri-reference
    type: string

  is_employee:
    type: boolean

  services:
    $ref: "#ServicesSchema"

  support_email:
    type: string

  self:
    $ref: "#UserDataSchema"

  team:
    $ref: "#UserDataSchema"
