@import "./viewer/viewer.css";

/** @define AppAssetDetail; */
.AppAssetDetail-main {
	display: flex;
	gap: var(--AppAssetDetail-main-gap-size);
}

/*
 * Because we have titles such as the one below, we MUST `break-all`
 * and not `break-word` to avoid overflow issues.
 * Marketing_Werbemaßnahmen_Fahrzeugbeklebung
 */
.AppAssetDetail-title {
	word-break: break-all;
}

.AppAssetDetail-details,
.AppAssetDetail-downloads {
	margin-block-start: var(--size-40);
}

.AppAssetDetail-detailsLabel {
	color: var(--shared-color-text-secondary);
	margin-block-start: var(--size-24);
}

.AppAssetDetail-detailsValue {
	margin-block-start: var(--size-4);
}

.AppAssetDetail-downloadsItem {
	display: flex;
	gap: var(--size-16);
	margin-block-start: var(--size-24);

	.AppAssetDetail-downloadLink {
		align-self: center;
		border: 0;
		margin-inline-start: auto;
	}
}

/* nested selector to increase specificity and overwrite default icon size */
.AppAssetDetail .AppAssetDetail-downloadsIcon {
	flex-shrink: 0;
	inline-size: var(--size-48);
}

.AppAssetDetail-downloadsItemLabel {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.AppAssetDetail-downloadsItemMeta {
	color: var(--shared-color-text-secondary);
	display: block;
	font: var(--typo-TextS);
	margin-block-start: var(--size-4);
	text-transform: uppercase;
}

.AppAssetDetail-downloadsItemMeta :first-child::after {
	content: "|";
	margin-inline: var(--size-8);
}

.AppAssetDetail-backLink {
	margin-block-start: var(--shared-spacing-sections-md);
}

/* mobile */
@media (width < 48rem) {
	.AppAssetDetail-main {
		--AppAssetDetail-main-gap-size: var(--size-24);

		flex-direction: column;
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.AppAssetDetail-main {
		--AppAssetDetail-main-gap-size: var(--size-48);
	}

	.AppAssetDetail-data {
		inline-size: 50%;
		min-inline-size: 0;
	}

	.AppAssetDetail-detailsList {
		column-gap: var(--size-48);
		display: grid;
		grid-template-columns: 1fr 1fr;
	}
}
