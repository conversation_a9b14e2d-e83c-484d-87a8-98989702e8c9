{{ attach_library('finstral_global/apps-app-asset-detail') }}

<div class="AppAssetDetail u-container">
	<div class="AppAssetDetail-main">
		{% include "@apps/extranet/asset-detail/app-asset-detail/viewer/viewer.twig" with {
			height: asset.height,
			type: asset.type,
			url: asset.url,
			width: asset.width,
		} only %}
		<section class="AppAssetDetail-data">
			<h1 class="AppAssetDetail-title u-typo-HeadlineM">{{ title }}</h1>

		{% if details %}
			<section class="AppAssetDetail-details">
				<h2 class="u-typo-HeadlineS">{{ "asset_detail.details.heading"|tc }}</h2>
				<dl class="AppAssetDetail-detailsList">
					{% for detail in details %}
						<div>
							<dt class="AppAssetDetail-detailsLabel">{{ detail.label }}</dt>
							{% for value in detail.values %}
								<dd class="AppAssetDetail-detailsValue">{{ value }}</dd>
							{% endfor %}
						</div>
					{% endfor %}
				</dl>
			</section>
		{% endif %}

			<section class="AppAssetDetail-downloads">
				<h2 class="u-typo-HeadlineS">{{ "asset_detail.downloads.heading"|tc }}</h2>
				<ul class="AppAssetDetail-downloadsList">
					{% for file in downloads %}
						<li class="AppAssetDetail-downloadsItem">
							{% include "@elements/icon/icon.twig" with {
								sprite_sheet: "file-icons",
								name: file.file_icon,
								classes: ["AppAssetDetail-downloadsIcon"]
							} only %}
							<span class="AppAssetDetail-downloadsItemLabel">
								<span class="u-typo-TextM">{{ file.label }}</span>
								<span class="AppAssetDetail-downloadsItemMeta">
									<span>{{ file.metadata.filetype }}</span>
									{{ file.metadata.filesize }}
								</span>
							</span>
							{% include "@elements/button/button.twig" with {
								classes: ["AppAssetDetail-downloadLink"],
								label: file.label,
								icon: {
									name: "download",
									position: "icon_only",
									},
								url: file.url,
							} only %}
						</li>
					{% endfor %}
				</ul>
			</section>
		</section>
	</div>
	{% include "@elements/link/link.twig" with {
		url: back_link,
		label: "asset_detail.back_link.label"|tc,
		icon: {
			name: "arrow_back",
			position: "start",
		},
		classes: ["AppAssetDetail-backLink"]
	} only %}
</div>
