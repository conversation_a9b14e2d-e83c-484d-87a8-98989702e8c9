$schema: http://json-schema.org/draft-07/schema
$id: https://finstral.com/apps/extranet/asset-detail/app-asset-detail

type: object

required:
  - asset
  - back_link
  - title
  - details
  - downloads

additionalProperties: false

properties:
  back_link:
    type: string
    format: uri-reference
  details:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - label
        - values
      properties:
        label:
          type: string
        values:
          type: array
          items:
            type: string
  downloads:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - label
        - metadata
        - url
      properties:
        label:
          type: string
        url:
          type: string
          format: uri-reference
        metadata:
          type: object
          additionalProperties: false
          required:
            - filesize
            - filetype
          properties:
            filesize:
              type: string
            filetype:
              type: string
        file_icon:
          type: string
  asset:
    type: object
    additionalProperties: false
    required:
      - height
      - url
      - width
    properties:
      alt:
        type: string
      height:
        type: integer
      type:
        type: string
      url:
        type: string
        format: uri-reference
      width:
        type: integer
  title:
    type: string
