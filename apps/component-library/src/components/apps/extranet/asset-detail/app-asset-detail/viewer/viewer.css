/** @define Viewer; weak; */

.Viewer {
	background-color: var(--shared-color-surface-subtle-lighter);
	display: grid;
	padding: var(--size-32);
}

.Viewer-assetContainer {
	align-items: center;
	aspect-ratio: 1;
	block-size: auto;
	display: flex;
	inline-size: 100%;
	justify-content: center;
}

.Viewer img {
	block-size: 100%;
	inline-size: 100%;
	object-fit: contain;
}

/* overwrites display styling in video component */
.Viewer-video {
	align-items: center;
	display: flex;
	justify-content: center;

	.Video-asset {
		block-size: 100%;
		object-fit: contain;
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.Viewer {
		align-self: start;
		inline-size: 50%;
	}
}
