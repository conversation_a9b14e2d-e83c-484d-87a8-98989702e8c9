@import "./finstral-asset-download/finstral-asset-download.css";

/** @define AssetCard; weak; */
.AssetCard {
	block-size: 100%;
	display: grid;
	grid-template-rows: auto 1fr;
	position: relative;
}

.AssetCard-imageWrapper {
	background-color: var(--shared-color-surface-subtle-lighter);
	padding: var(--AssetCard-padding);
}

.AssetCard-imageContainer {
	align-items: center;
	aspect-ratio: 1;
	display: flex;
	justify-content: center;
}

.AssetCard .AssetCard-image {
	block-size: 100%;
	inline-size: 100%;
	object-fit: contain;
}

.AssetCard-button {
	border-radius: 50%;
	inset-block-start: var(--size-8);
	inset-inline-end: var(--size-8);
	position: absolute;

	* {
		pointer-events: none;
	}
}

.AssetCard-button:not(.AssetCard-button--more) {
	inset-block-start: var(--size-16);
	inset-inline-end: var(--size-16);
}

.AssetCard-popover {
	display: none;
}

.AssetCard:has(.AssetCard-popover.u-modal-open) .AssetCard-button--more {
	background-color: var(--shared-color-surface-primary);
	box-shadow: var(--box-shadow-subtle);
}

.AssetCard-groupAmount {
	align-content: center;
	background-color: var(--shared-color-surface-invert);
	block-size: var(--size-24);
	border-radius: 50%;
	color: var(--shared-color-text-invert);
	inline-size: var(--size-24);
	inset-block-start: var(--size-8);
	inset-inline-start: var(--size-8);
	position: absolute;
	text-align: center;
}

@supports (text-box-trim: trim-both) {
	.AssetCard-groupAmount {
		text-box-edge: cap alphabetic;
		text-box-trim: trim-both;
	}
}

.AssetCard-title {
	display: block;
	margin-block-start: var(--size-16);
	word-break: break-all;
}

.AssetCard-meta {
	color: var(--shared-color-text-secondary);
	display: block;
	font: var(--typo-TextS);
	margin-block-start: var(--size-8);
}

.AssetCard-meta :first-child::after {
	content: "|";
	inset-block-start: -0.1rem;
	margin-inline: var(--size-8);
	position: relative;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.AssetCard-popover.u-modal-open {
	background-color: var(--shared-color-surface-primary);
	border: none;
	padding-block: var(--size-56) var(--size-16);
	padding-inline: var(--size-24);
}

.AssetCard-detailLink {
	padding-block: var(--size-16) var(--size-24);
}

.AssetCard-downloadItem {
	border-block-start: var(--border-subtle);
	padding-block: var(--size-24);
}

.AssetCard-downloadLink {
	max-inline-size: 100%;
}

.AssetCard-downloadLink .Button-label {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* mobile */
@media (width < 48rem) {
	.AssetCard {
		--AssetCard-padding: var(--size-24);
	}

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.AssetCard-popover.u-modal-open {
		inline-size: 100%;
		inset: unset;
		inset-block-end: 0;
		inset-inline-start: 0;
		min-block-size: 40%;
		position: fixed;
		z-index: var(--bring-to-front);
	}

	.AssetCard-backdrop {
		background-color: var(--shared-color-surface-overlay-dialog);
		inset: 0;
		position: fixed;
		z-index: var(--backdrop);
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.AssetCard {
		--AssetCard-padding: var(--size-32);
		--AssetCard-popover-inline-size: 24.375rem; /* magic number taken from figma */
		min-block-size: 21rem;
	}

	.AssetCard-popover {
		inline-size: var(--AssetCard-popover-inline-size);
		margin-block-start: var(--size-8);
		position: absolute;

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		&.u-modal-open {
			z-index: var(--middle-layer);
		}

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		&.base-inline {
			inset-inline-end: calc(
				(var(--AssetCard-popover-inline-size) - 0.25rem) * -1
			);
		}

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		&.flip-inline {
			inset-inline-end: 2rem;
		}
	}
}

/* NOTE: the AssetCard can appear inside `extranet-results` where the layout can be either a grid of cards (ExtranetResults--grid) or
a list with rows (ExtranetResults--list), and looks like an actual `card` or a `row` depending.
Below are nested styles for AssetCard inside a row layout. */

/* stylelint-disable plugin/selector-bem-pattern */

/* style card as row item when inside result list layout  */
.ExtranetResults--list {
	.AssetCard {
		--AssetCard-padding: var(--size-4);

		align-items: center;
		display: flex;
		gap: var(--size-16);
		min-block-size: initial;
	}

	.AssetCard-link {
		align-items: center;
		display: flex;
		flex-grow: 1;
		gap: var(--size-16);
	}

	.AssetCard-imageWrapper {
		align-self: flex-start;
		block-size: var(--size-64);
		flex-shrink: 0;
		inline-size: var(--size-64);
	}

	.AssetCard-button--more {
		inset: unset;
		position: initial;
	}

	.AssetCard-groupAmount {
		display: none;
	}

	.AssetCard-meta {
		flex-shrink: 0;
	}

	.AssetCard-title {
		margin-block-start: 0;
	}

	/* tablet and desktop */
	@media (width >= 48rem) {
		.AssetCard-meta {
			margin-block-start: 0;
		}
	}
}
