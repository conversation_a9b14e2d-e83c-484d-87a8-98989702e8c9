class AssetCard extends HTMLElement {
	static #selectors = {
		assetCardButton: ".AssetCard-button",
	};

	#elements;

	/**
	 * Retrieves the elements used in the AssetCard component.
	 * @returns {object} An object containing references to the component's elements.
	 * @private
	 */
	#getElements() {
		return {
			assetCardButton: this.querySelector(AssetCard.#selectors.assetCardButton),
		};
	}

	/**
	 * Initializes the AssetCard component by setting up elements and event listeners.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();
		this.#addEventListeners();
	}

	/**
	 * Adds event listeners to the component.
	 * Handles click events to toggle the flyout menu.
	 * @private
	 */
	#addEventListeners() {
		this.addEventListener("click", (event) => {
			const target = event.target;
			if (target.matches(AssetCard.#selectors.assetCardButton)) {
				this.#toggleFlyout();
			}
		});

		document.addEventListener("click", this.#clickOutside.bind(this));
		document.addEventListener("keyup", this.#handleKeyboardEvents.bind(this));
	}

	/**
	 * Checks if the given trigger element is expanded.
	 * @param {HTMLElement} trigger - The trigger element to check.
	 * @returns {boolean} True if the trigger is expanded, otherwise false.
	 */
	#getIsExpanded(trigger) {
		return trigger.getAttribute("aria-expanded") === "true";
	}

	/**
	 * Toggles the expanded state of the given trigger element.
	 * @param {HTMLElement} trigger - The trigger element whose state is toggled.
	 * @private
	 */
	#setIsExpandedState(trigger) {
		const isExanded = this.#getIsExpanded(trigger);
		trigger.setAttribute("aria-expanded", !isExanded);
	}

	/**
	 * Handles keyboard events, such as closing the flyout menu on "Escape" key press.
	 * @param {KeyboardEvent} event - The keyboard event object.
	 * @private
	 */
	#handleKeyboardEvents(event) {
		const { assetCardButton } = this.#elements;
		const isExpanded = this.#getIsExpanded(assetCardButton);

		if (event.key === "Escape" && isExpanded) {
			this.#toggleFlyout(assetCardButton);
			assetCardButton.focus();
		}
	}

	/**
	 * Handles clicks outside the flyout menu to close it.
	 * @param {MouseEvent} event - The click event object.
	 * @private
	 */
	#clickOutside(event) {
		const { assetCardButton } = this.#elements;

		if (event.target === assetCardButton) {
			return;
		}

		const flyout = assetCardButton.nextElementSibling;
		const isExpanded = this.#getIsExpanded(assetCardButton);
		if (!flyout.contains(event.target) && isExpanded) {
			this.#toggleFlyout(assetCardButton);
		}
	}

	/**
	 * Toggles the visibility of the backdrop element.
	 * If the backdrop exists, it removes it; otherwise, it creates and appends a new backdrop.
	 * @private
	 */
	#toggleBackdrop() {
		let backdrop = document.querySelector(".AssetCard-backdrop");

		if (backdrop) {
			backdrop.remove();
			return;
		}

		backdrop = document.createElement("div");
		backdrop.classList.add("AssetCard-backdrop");
		document.body.appendChild(backdrop);
	}

	/**
	 * Toggles the flyout menu's visibility and position.
	 * Ensures that the flyout does not overflow the viewport.
	 * @private
	 */
	#toggleFlyout() {
		const { assetCardButton } = this.#elements;
		const flyout = assetCardButton.nextElementSibling;
		const flyoutComputedInlineSize = parseInt(
			getComputedStyle(flyout).inlineSize,
			10,
		);
		const triggerLeft = assetCardButton.getBoundingClientRect().left;
		const windowWidth = window.innerWidth;

		flyout.classList.toggle("u-modal-open");
		this.#toggleBackdrop();
		if (windowWidth - flyoutComputedInlineSize < triggerLeft) {
			flyout.classList.toggle("flip-inline");
		} else {
			flyout.classList.toggle("base-inline");
		}

		this.#setIsExpandedState(assetCardButton);
		flyout.focus();
	}
}

customElements.define("finstral-asset-card", AssetCard);
