{{ attach_library('finstral_global/apps-extranet-asset-card') }}

<template id="extranet-asset-card-tmpl">
	<finstral-asset-card class="AssetCard">
		<a href="" class="AssetCard-link u-link u-link--neutral">
			<div class="AssetCard-imageWrapper">
				<div class="AssetCard-imageContainer">
					<img src="placeholder.png" class="AssetCard-image" alt="placeholder">
				</div>
			</div>
			<span class="AssetCard-title">placeholder</span>
		</a>
		<span class="AssetCard-meta">
			<span class="AssetCard-metaType">{{ "asset_card.meta.type"|tc }}</span>
			<span class="AssetCard-metaSize">{{ "asset_card.meta.size"|tc }}</span>
		</span>
		<span class="AssetCard-groupAmount u-typo-TextS" hidden></span>
		<button aria-expanded="false" aria-haspopup="menu" class="AssetCard-button AssetCard-button--more" type="button">
			{% include "@elements/icon/icon.twig" with {
				name: "more"
			} only %}
			<span class="u-hiddenVisually">{{ "asset_card.more.button.label"|tc }}</span>
		</button>
	</finstral-asset-card>
</template>

<template id="extranet-asset-card-popover-tmpl">
	<div class="AssetCard-popover">
		<button class="AssetCard-button" type="button">
			{% include "@elements/icon/icon.twig" with {
					name: "close"
				} only %}
			<span class="u-hiddenVisually">{{ "asset_card.close.button.label"|tc }}</span>
		</button>

		{% include "@elements/link/link.twig" with {
				label: "asset_card.detail_link.label"|tc,
				icon: {
					name: "arrow_forward",
					position: "start",
				},
				classes: ["AssetCard-detailLink"],
			} only %}

		<ul class="AssetCard-downloadList"></ul>
	</div>
</template>

<template id="extranet-asset-card-download-entry-tmpl">
	<li class="AssetCard-downloadItem">
		{% include "@apps/extranet/components/asset-card/finstral-asset-download/finstral-asset-download.twig" %}
	</li>
</template>
