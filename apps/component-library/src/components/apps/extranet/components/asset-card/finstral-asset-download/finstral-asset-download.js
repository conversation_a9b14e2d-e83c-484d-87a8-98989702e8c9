export class FinstralAssetDownloads extends HTMLElement {
	static #selectors = {
		downloadLink: ".FinstralAssetDownload-link",
		downloadLabel: ".FinstralAssetDownload-label",
	};

	/**
	 * @returns {Array} list of elements from the document
	 */
	#getElements() {
		return {
			downloadLinkElem: this.querySelector(
				FinstralAssetDownloads.#selectors.downloadLink,
			),
			downloadLabelElem: this.querySelector(
				FinstralAssetDownloads.#selectors.downloadLabel,
			),
		};
	}

	/**
	 * Initialise FinstralAssetDownloads component
	 */
	constructor() {
		super();

		const { downloadLinkElem } = this.#getElements();
		if (downloadLinkElem) {
			this.#setLinkData();
		}
	}

	/**
	 * Sets the download link `href` and text label values based on the values
	 * of attributes set on the parent custom element.
	 */
	#setLinkData() {
		const { downloadLinkElem, downloadLabelElem } = this.#getElements();

		const downloadLabel = this.getAttribute("download-label");
		const downloadHref = this.getAttribute("download-href");
		const downloadFilename = this.getAttribute("filename");

		downloadLabelElem.textContent = downloadLabel.replace(
			"{{ filename }}",
			this.#getFileType(downloadFilename),
		);
		downloadLinkElem.href = downloadHref;
	}

	/**
	 * Returns the file extension extracted from the filename, uppercased unless otherwise specified.
	 * @param {string} filename - The filename from which to extract the file extension
	 * @param {boolean} [uppercase] - Whether the extension should be uppercased (default=true)
	 * @returns {string} The file extension as either uppercased or not
	 */
	#getFileType(filename, uppercase = true) {
		const filetype = filename.slice(filename.lastIndexOf(".") + 1);
		return uppercase ? filetype.toUpperCase() : filetype.toLowerCase();
	}
}

customElements.define("finstral-asset-download", FinstralAssetDownloads);
