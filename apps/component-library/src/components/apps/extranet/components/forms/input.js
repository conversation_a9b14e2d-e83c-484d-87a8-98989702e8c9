/**
 * Creates and returns a label element with the specified text and associated input ID.
 * @param {string} label - The text content for the label.
 * @param {string} id - The ID of the input element the label is associated with.
 * @returns {HTMLLabelElement} The constructed label element.
 */
const getLabelElement = (label, id) => {
	const labelElement = document.createElement("label");
	const labelText = document.createElement("span");

	labelElement.classList = "FormElementLabel u-typo-TextM";
	labelElement.htmlFor = id;
	labelText.textContent = label;

	labelElement.append(labelText);

	return labelElement;
};

/**
 * Creates and returns an input HTML element with the specified options.
 * @param {object} options - Configuration options for the input element.
 * @param {string} options.id - The ID to assign to the input element.
 * @param {string} options.type - The type attribute of the input element (e.g., "text", "password").
 * @param {string} options.name - The name attribute of the input element.
 * @param {string} options.value - The value attribute of the input element.
 * @param {string} [options.placeholder] - The placeholder text for the input element (optional).
 * @returns {HTMLInputElement} The created input HTML element.
 */
const getInputElement = (options) => {
	const { id, type, name, value, placeholder } = options;

	const inputElement = document.createElement("input");

	inputElement.type = type;
	inputElement.name = name;
	inputElement.value = value;
	inputElement.id = id;

	if (placeholder) {
		inputElement.placeholder = placeholder;
	}

	return inputElement;
};

/**
 * Creates a radio button element wrapped in a styled container.
 * @param {object} options - The options for creating the radio button element.
 * @param {string} options.name - The name attribute for the radio input, used to group radio buttons.
 * @param {string} options.value - The value attribute for the radio input.
 * @param {string} options.label - The text label associated with the radio input.
 * @param {boolean} [options.isChecked] - Whether the radio input is initially checked.
 * @returns {HTMLDivElement} A div element containing the radio input and its associated label.
 */
export const radioElement = (options) => {
	const { name, value, label, isChecked } = options;

	const uuid = self.crypto.randomUUID();
	const inputOptions = {
		id: uuid,
		type: "radio",
		name: name,
		value: value,
	};

	const radioWrapper = document.createElement("div");
	const radioInput = getInputElement(inputOptions);
	const radioLabel = getLabelElement(label, uuid);

	radioInput.checked = isChecked;

	radioWrapper.classList = "FormElement js-FormElement Option Radio";
	radioWrapper.append(radioInput, radioLabel);

	return radioWrapper;
};

/**
 * Creates a checkbox element wrapped in a div with a label.
 * @param {object} options - The options for creating the checkbox element.
 * @param {string} options.name - The name attribute for the checkbox input.
 * @param {string} options.value - The value attribute for the checkbox input.
 * @param {string} options.label - The text content for the checkbox label.
 * @param {boolean} options.isChecked - Indicates whether the checkbox should be checked.
 * @returns {HTMLDivElement} A div element containing the checkbox input and its associated label.
 */
export const checkBoxElement = (options) => {
	const { name, value, label, isChecked } = options;

	const uuid = self.crypto.randomUUID();
	const inputOptions = {
		id: uuid,
		type: "checkbox",
		name: name,
		value: value,
	};

	const checkBoxWrapper = document.createElement("div");
	const checkBoxInput = getInputElement(inputOptions);
	const checkBoxLabel = getLabelElement(label, uuid);

	checkBoxInput.checked = isChecked;

	checkBoxWrapper.classList = "FormElement js-FormElement Option Checkbox";
	checkBoxWrapper.append(checkBoxInput, checkBoxLabel);

	return checkBoxWrapper;
};
