const state = {};

/**
 * Starts the session end alert by configuring and initializing the timer.
 * @param {HTMLElement} sessionEndAlert - The session end alert custom element.
 * @returns {object} An instance of the sessionEndAlert component
 */
export function startSessionEndAlert(sessionEndAlert) {
	return sessionEndAlert
		.configure(state.defaultSessionTimeoutInMilliseconds, () => {
			window.location.reload();
		})
		.startTimer();
}

/**
 * Initializes and retrieves the Algolia base data from the DOM.
 * @returns {object} The parsed Algolia base data.
 * @throws {Error} If the Algolia base data is not found or cannot be parsed.
 * @example
 * // Example of the data returned:
 * {
 *   "indexName": "finstral_extranet_stage",
 *   "langCode": "en",
 * }
 */
const initAlgoliaBaseData = () => {
	const algoliaBaseData = document.querySelector("#algolia-data");

	if (!algoliaBaseData) {
		throw new Error("Algolia base data not found.");
	}

	try {
		const algoliaBaseDataJson = algoliaBaseData.textContent;
		return JSON.parse(algoliaBaseDataJson);
	} catch (error) {
		throw new Error(`Failed to parse Algolia base data: ${error.message}`);
	}
};

/**
 * Retrieves the current Algolia language code.
 * @returns {string} The current Algolia language code.
 * @throws {Error} If the language code is not set.
 */
export const getCurrentAlgoliaLangCode = () => {
	if (!state.langCode) {
		throw new Error("Algolia language code not set.");
	}

	return state.langCode;
};

/**
 * Constructs the filter string for Algolia search queries.
 * @returns {string} The filter string for Algolia queries.
 */
export const getFilters = () => {
	return state.baseFilters;
};

/**
 * Fetches Algolia credentials from the server. Returns an object with the following properties:
 * @typedef {object} AlgoliaCredentials
 * @property {string} applicationId - The Algolia application ID.
 * @property {string} securedApiKey - The secured API key for Algolia.
 * @example
 * // Example of the data returned:
 * {
 *   applicationId: string;
 *   securedApiKey: string;
 * }
 * @param {string} indexName - The name of the Algolia index.
 * @returns {Promise<AlgoliaCredentials>} The Algolia credentials.
 * @throws {Error} If the request fails or the response is invalid.
 */
const getAlgoliaCredentials = async (indexName) => {
	const generateKeyEndpoint = `/api/search/generate-key/${indexName}`;

	try {
		const response = await fetch(generateKeyEndpoint);

		if (!response.ok) {
			throw new Error(`HTTP error: ${response.status}`);
		}

		const responseData = await response.json();

		if (responseData && responseData.status === "error") {
			throw new Error(responseData.message);
		}

		return responseData;
	} catch (error) {
		throw new Error(`Failed to get Algolia key: ${error.message}`);
	}
};

/**
 * Validates the provided Algolia API key by making a request to the server. If no API key
 * is provided, it returns 0.
 * https://factorial-io.atlassian.net/wiki/spaces/FINSTRAL/pages/10307829761/Search+API+Keys
 * @param {string} apiKey - The API key to validate.
 * @returns {Promise<object>} The response data indicating the time (in seconds) until the key expires.
 * @throws {Error} If the validation request fails or the server returns an error.
 */
export const isApiKeyValid = async (apiKey) => {
	// If no API key is provided, return 0 (indicating the key is invalid)
	// This is a fallback to avoid unnecessary API calls and to ensure
	// that the function can be called without an API key. This can
	// happen if the Algolia client is not initialized yet.
	if (!apiKey) {
		return Promise.resolve(0);
	}

	const validateApiKeyEndpoint = `/api/search/validate-key/${apiKey}`;

	try {
		const response = await fetch(validateApiKeyEndpoint);

		if (!response.ok) {
			throw new Error(`HTTP error: ${response.status}`);
		}

		const responseData = await response.json();

		return responseData;
	} catch (error) {
		throw new Error(`Failed to validate Algolia key: ${error.message}`);
	}
};

/**
 * Retrieves the client credentials from the application state.
 * @throws {Error} Throws an error if the state is not initialized.
 * @returns {object} An object containing the client credentials:
 * - `applicationId` {string}: The application ID.
 * - `indexName` {string}: The index name.
 * - `securedApiKey` {string}: The secured API key.
 */
export const getClientCredentials = () => {
	if (!state) {
		throw new Error("State not initialized.");
	}

	return {
		applicationId: state.applicationId,
		indexName: state.indexName,
		securedApiKey: state.securedApiKey,
	};
};

/**
 * Retrieves the current Algolia index name.
 * @returns {string} The current Algolia index name.
 * @throws {Error} If the index name is not set.
 */
export const getCurrentAlgoliaIndexName = () => {
	if (!state.indexName) {
		throw new Error("Algolia index name not set.");
	}

	return state.indexName;
};

/**
 * @typedef {'changed_asc' | 'title_asc' | 'title_desc'} IndexType
 * Represents the possible Algolia sorting replica index types.
 */

/**
 * Retrieves the Algolia index name based on the specified type.
 * @param {IndexType} [type] - The sorting type to append to the base index name. Defaults to the primary index if not provided.
 * @returns {string} The full Algolia index name.
 */
export const getAlgoliaIndexNameByType = (type) => {
	const base = getCurrentAlgoliaIndexName();
	return type ? `${base}_${type}` : base;
};

/**
 * Asynchronously sets up Algolia data by initializing base data,
 * retrieving credentials, and updating the application state.
 * @async
 * @throws {Error} Throws an error if the Algolia index name is not set
 *                 or if there is a failure in setting Algolia data.
 */
export const setAlgoliaData = async () => {
	try {
		const algoliaBaseData = initAlgoliaBaseData();

		if (!algoliaBaseData.indexName) {
			throw new Error("Algolia index name not set.");
		}

		state.indexName = algoliaBaseData.indexName;
		state.langCode = algoliaBaseData.langCode;

		state.defaultSessionTimeoutInMilliseconds = 6300000; // 1 hour 45 minutes
		state.baseFilters = `search_api_language:${getCurrentAlgoliaLangCode()} AND NOT is_archived:true AND NOT is_secondary_variant:true`;

		const credentials = await getAlgoliaCredentials(state.indexName);

		if (credentials) {
			state.applicationId = credentials.applicationId;
			state.securedApiKey = credentials.securedApiKey;
		}
	} catch (error) {
		throw new Error(`Failed to set Algolia data: ${error.message}`);
	}
};
