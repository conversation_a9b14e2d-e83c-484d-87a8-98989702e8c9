import { liteClient as algoliasearch } from "algoliasearch/lite";
import instantsearch from "instantsearch.js";
import { configure } from "instantsearch.js/es/widgets";

import {
	getCurrentAlgoliaIndexName,
	getClientCredentials,
	getFilters,
	startSessionEndAlert,
	setAlgoliaData,
} from "./client-utils.js";

// Private module-level variables
let client = null;
let instantSearchInstance = null;
let initPromise = null;
let sessionEndAlertStarted = null;

/**
 * Initializes the Algolia client and InstantSearch instance
 * @returns {Promise<void>}
 */
export async function initAlgolia() {
	// If already initialized, return early
	if (client) {
		return;
	}

	// If initialization is in progress, wait for it to complete
	if (initPromise) {
		return initPromise;
	}

	// Start new initialization
	initPromise = (async () => {
		try {
			await setAlgoliaData();

			// @see web/themes/custom/finstral_global/src/components/apps/extranet/components/session-end-alert
			const sessionEndAlertElem = document.querySelector("session-end-alert");

			if (sessionEndAlertElem && !sessionEndAlertStarted) {
				sessionEndAlertStarted = startSessionEndAlert(sessionEndAlertElem);
			}

			let algoliaData = getClientCredentials();

			client = algoliasearch(
				algoliaData.applicationId,
				algoliaData.securedApiKey,
			);

			instantSearchInstance = instantsearch({
				indexName: getCurrentAlgoliaIndexName(),
				future: {
					preserveSharedStateOnUnmount: true,
				},
				searchClient: client,
			});
		} catch (error) {
			// Clear the initialization state on error
			client = null;
			instantSearchInstance = null;
			throw new Error(`Error initializing Algolia: ${error.message}`);
		} finally {
			// Always reset the promise when done (success or failure)
			initPromise = null;
		}
	})();

	return initPromise;
}

/**
 * Gets the Algolia client, initializing it if necessary
 * @returns {Promise<object>} The Algolia client
 */
export async function getAlgoliaClient() {
	if (!client) {
		await initAlgolia();
	}
	return client;
}

/**
 * Gets the InstantSearch instance, initializing it if necessary.
 * @returns {Promise<object>} The InstantSearch instance
 */
export async function getInstantSearchClient() {
	if (!instantSearchInstance) {
		await initAlgolia();
	}
	return instantSearchInstance;
}

/**
 * Configures the search parameters for Algolia.
 * @param {string} [customFilters] - Additional custom filters to apply to the search query.
 * @returns {object} The configured search parameters.
 */
export function getAlgoliaSearchParametersConfiguration(customFilters) {
	let filters = getFilters();

	if (customFilters) {
		filters = `${filters} AND ${customFilters}`;
	}

	return configure({
		filters,
		maxValuesPerFacet: 1000,
	});
}

/**
 * Fetches the latest assets from the specified Algolia index
 * @param {number} [hitsPerPage] - The number of hits per page (default: 12)
 * @param {string} [query] - The search query (default: empty string)
 * @returns {Promise<Array>} An array of hits
 */
export async function getLatestAssets(hitsPerPage = 9, query = "") {
	const algoliaClient = await getAlgoliaClient();
	const indexName = getCurrentAlgoliaIndexName();

	const { results } = await algoliaClient.search({
		requests: [
			{
				filters: getFilters(),
				hitsPerPage,
				indexName,
				query,
			},
		],
	});

	return results.length ? results[0].hits : [];
}
