/* define @ExtranetResults; */

.ExtranetResults {
	margin-block-start: var(--shared-spacing-sections-sm);
}

.ExtranetResults li {
	min-inline-size: 0;
}

.ExtranetResults--grid {
	display: grid;
	gap: var(--size-16);
}

.ExtranetResults--list li {
	border-block-end: var(--border-subtle-transparent);
	padding-block-end: var(--size-16);
}

.ExtranetResults--list li:not(:first-child) {
	padding-block-start: var(--size-16);
}

/* mobile */
@media (width < 40rem) {
	.ExtranetResults--grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

/* tablet and desktop */
@media (width >= 40rem) {
	.ExtranetResults--grid {
		grid-template-columns: repeat(auto-fill, minmax(min(15rem, 100%), 1fr));
		row-gap: var(--size-24);
	}
}
