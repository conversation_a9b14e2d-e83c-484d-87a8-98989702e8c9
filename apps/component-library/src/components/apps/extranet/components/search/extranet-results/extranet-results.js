import { connectInfiniteHits } from "instantsearch.js/es/connectors";

import { getAssetCard } from "../search-ui-utils";

const renderResults = (renderOptions, isFirstRender) => {
	const { items, isLastPage, showMore, widgetParams } = renderOptions;
	const extranetShowMoreContainer = document.querySelector(".ShowMore");
	const extranetShowMoreButton =
		extranetShowMoreContainer.querySelector("button");
	const resultsContainer = widgetParams.container;

	resultsContainer.classList.add("u-hidden");

	if (isFirstRender && extranetShowMoreButton) {
		extranetShowMoreButton.addEventListener("click", (event) => {
			event.preventDefault();
			showMore();
		});
	}

	if (items) {
		const resultsList = items.map((item) => {
			return getAssetCard(item, /* asListItem */ true);
		});

		resultsContainer.innerHTML = ""; // Clear previous results
		resultsContainer.append(...resultsList);

		resultsContainer.classList.remove("u-hidden");

		extranetShowMoreContainer.hidden = isLastPage;

		if (!isFirstRender) {
			const renderHitsStatus = new CustomEvent("renderHitsStatus", {
				detail: {
					hasResults: items.length > 0,
					status: "ready",
				},
			});
			document.dispatchEvent(renderHitsStatus);
		}
	}
};

const customInfiniteHits = connectInfiniteHits(renderResults);

const container = document.querySelector(".ExtranetResults");
export const initExtranetResults = customInfiniteHits({
	container,
});
