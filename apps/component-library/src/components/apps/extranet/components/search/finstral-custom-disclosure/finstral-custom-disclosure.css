/** @define FinstralCustomDisclosure; weak; */

.FinstralCustomDisclosure {
	--FinstralCustomDisclosure-border: var(--shared-border-width-sm) solid
	var(--shared-color-border-distinct);
	--FinstralCustomDisclosure-border-active: var(--shared-border-width-md) solid
	var(--shared-color-border-full);

	display: block;
}

.FinstralCustomDisclosure-itemsContainer {
	/* initial display mode */
	display: none;
}

.FinstralCustomDisclosure-toggle {
	/* sets the display mode when the container is shown */
	--display-mode: flex;

	align-items: center;
	background-color: var(--shared-color-surface-primary);
	color: var(--shared-color-text-secondary);
	display: flex;
	gap: var(--size-8);
	inline-size: 100%;
	justify-content: space-between;
	padding-block: var(--FinstralCustomDisclosure-padding-block);
	padding-inline: var(--FinstralCustomDisclosure-padding-inline);

	/* this is to ensure none of the child element trigger events */
	* {
		pointer-events: none;
	}
}

.FinstralCustomDisclosure-toggle[aria-expanded="true"] {
	color: var(--shared-color-text-primary);

	.FinstralCustomDisclosure-toggleIcon {
		transform: rotate(180deg);
	}
}

.FinstralCustomDisclosure-toggleIcon {
	color: var(--shared-color-text-primary);
}

.FinstralCustomDisclosure-toggle[aria-expanded="true"]
+ .FinstralCustomDisclosure-itemsContainer {
	background-color: var(--shared-color-surface-primary);
	display: block;
	padding-block: var(--size-12) var(--size-16);
}

.FinstralCustomDisclosure-itemsContainer ul {
	display: grid;
	gap: var(--size-16);
}

.FinstralCustomDisclosure-toggleLabelWrapper {
	inline-size: 70%;
}

.FinstralCustomDisclosure-toggleLabel {
	display: block;
	overflow: hidden;
	text-align: start;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* mobile */
@media (width < 48rem) {
	.FinstralCustomDisclosure {
		--FinstralCustomDisclosure-padding-block: var(--size-16);
		--FinstralCustomDisclosure-padding-inline: 0;
	}

	.FinstralCustomDisclosure-toggle {
		border-block-end: var(--FinstralCustomDisclosure-border);
	}

	.FinstralCustomDisclosure-toggle[aria-expanded="true"] {
		border-block-end: none;
	}

	.FinstralCustomDisclosure-toggle[aria-expanded="true"]
	+ .FinstralCustomDisclosure-itemsContainer {
		border-block-end: var(--FinstralCustomDisclosure-border);
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.FinstralCustomDisclosure {
		--FinstralCustomDisclosure-padding-block: var(--size-12);
		--FinstralCustomDisclosure-padding-inline: var(--size-16);
	}

	.FinstralCustomDisclosure-toggle {
		border: var(--FinstralCustomDisclosure-border);
	}

	.FinstralCustomDisclosure-toggle[aria-expanded="true"] {
		border: var(--FinstralCustomDisclosure-border-active);

		border-block-end: none;
	}

	.FinstralCustomDisclosure-toggle[aria-expanded="true"]
		+ .FinstralCustomDisclosure-itemsContainer {
		border-block-end: var(--FinstralCustomDisclosure-border-active);
		border-inline: var(--FinstralCustomDisclosure-border-active);
		max-block-size: 20rem;
		overflow-y: auto;
		padding-inline: var(--size-16);
	}

	.FinstralCustomDisclosure[as-popover] {
		position: relative;

		.FinstralCustomDisclosure-itemsContainer {
			inline-size: 100%;
			position: absolute;
			z-index: var(--middle-layer);
		}
	}
}
