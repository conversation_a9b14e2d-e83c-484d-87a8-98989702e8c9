class FinstralCustomDisclosure extends HTMLElement {
	static #selectors = {
		disclosureToggle: ".FinstralCustomDisclosure-toggle",
		disclosureToggleLabel: ".FinstralCustomDisclosure-toggleLabel",
	};

	static observedAttributes = ["label"];

	#elements;

	/**
	 * Retrieves the elements based on the defined selectors.
	 * @returns {object} An object containing the selected elements.
	 */
	#getElements() {
		return {
			disclosureToggle: this.querySelector(
				FinstralCustomDisclosure.#selectors.disclosureToggle,
			),
		};
	}

	/**
	 * Initializes the FinstralCustomDisclosure component and sets up event listeners.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();
		this.#addEventListeners();
	}

	/**
	 * Called when one of the observed attributes is changed.
	 * @param {string} name - The name of the changed attribute.
	 * @param {string} _oldValue - The previous value of the attribute.
	 * @param {string} newValue - The new value of the attribute.
	 */
	attributeChangedCallback(name, _oldValue, newValue) {
		if (name === "label") {
			const label = this.querySelector(
				FinstralCustomDisclosure.#selectors.disclosureToggleLabel,
			);
			const { disclosureToggle } = this.#elements;
			disclosureToggle.title = newValue;
			label.textContent = newValue;
		}
	}

	/**
	 * Checks if the disclosure toggle is expanded.
	 * @returns {boolean} True if expanded, otherwise false.
	 */
	#getIsExpanded() {
		const { disclosureToggle } = this.#elements;
		return disclosureToggle.getAttribute("aria-expanded") === "true";
	}

	/**
	 * Toggles the state of the disclosure drawer and prevents scrolling.
	 * @param {boolean} isExpanded - Indicates whether the drawer is currently expanded.
	 */
	#toggleDrawer(isExpanded) {
		const { disclosureToggle } = this.#elements;
		disclosureToggle.setAttribute("aria-expanded", !isExpanded);
		disclosureToggle.classList.toggle("u-modal-open");
	}

	/**
	 * Handles keyboard events for the disclosure toggle.
	 * @param {KeyboardEvent} event - The keyboard event object.
	 */
	#handleKeyboardEvents(event) {
		const { disclosureToggle } = this.#elements;
		const isExpanded = this.#getIsExpanded();

		if (event.key === "Escape" && isExpanded) {
			this.#toggleDrawer(isExpanded);
			disclosureToggle.focus();
		}
	}

	/**
	 * Handles click events outside the component to close the disclosure drawer.
	 * @param {MouseEvent} event - The click event object.
	 */
	#clickOutside(event) {
		const isExpanded = this.#getIsExpanded();
		if (!this.contains(event.target) && isExpanded) {
			this.#toggleDrawer(isExpanded);
		}
	}

	/**
	 * Collapses all disclosure drawers in the same group except the current one.
	 * @param {string} groupName - The name of the group to collapse.
	 */
	#collapseGroup(groupName) {
		const group = Array.from(
			document.querySelectorAll(
				`finstral-custom-disclosure[name="${groupName}"]`,
			),
		).filter((disclosure) => disclosure !== this);

		group.forEach((disclosure) => {
			const disclosureToggle = disclosure.querySelector(
				FinstralCustomDisclosure.#selectors.disclosureToggle,
			);

			disclosureToggle.setAttribute("aria-expanded", false);
			disclosureToggle.classList.remove("u-modal-open");
		});
	}

	/**
	 * Adds event listeners for the disclosure toggle and document interactions.
	 */
	#addEventListeners() {
		const { disclosureToggle } = this.#elements;
		const hasGroupName = this.hasAttribute("name");

		disclosureToggle.addEventListener("click", (event) => {
			event.stopPropagation();

			if (hasGroupName) {
				const groupName = this.getAttribute("name");
				this.#collapseGroup(groupName);
			}

			this.#toggleDrawer(this.#getIsExpanded());
		});

		document.addEventListener("click", this.#clickOutside.bind(this));
		document.addEventListener("keyup", this.#handleKeyboardEvents.bind(this));
	}
}

customElements.define("finstral-custom-disclosure", FinstralCustomDisclosure);
