{{ attach_library('finstral_global/apps-extranet-finstral-custom-disclosure') }}

<finstral-custom-disclosure class="FinstralCustomDisclosure disclosure-container" as-popover {% if name %} name="{{ name }}" {% endif %}>
	<button class="FinstralCustomDisclosure-toggle" type="button" aria-expanded="false" aria-haspopup="listbox">
		<span class="FinstralCustomDisclosure-toggleLabelWrapper">
			<span class="FinstralCustomDisclosure-toggleLabel">{{ label|default("placeholder") }}</span>
		</span>
		{% include "@elements/icon/icon.twig" with {
			name: "chevron_down",
			classes: ["FinstralCustomDisclosure-toggleIcon"],
		} only %}
	</button>
	<div class="FinstralCustomDisclosure-itemsContainer"></div>
</finstral-custom-disclosure>
