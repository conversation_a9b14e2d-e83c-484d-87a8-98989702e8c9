/* @define LoaderWrapper; */
.LoaderWrapper {
	margin-block-start: var(--size-32);

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.SkeletonLoader {
		--animation-speed: 2.5s;

		--min-grid-item-size: 15rem;
		--box-default-block-size: 21rem;

		--initial-color-stop: var(--shared-color-surface-subtle-default) 25%;
		--mid-color-stop: var(--shared-color-surface-subtle-darker) 50%;
		--final-color-stop: var(--shared-color-surface-subtle-default) 75%;
	}
}
