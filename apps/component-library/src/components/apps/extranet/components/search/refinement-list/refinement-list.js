import { connectRefinementList } from "instantsearch.js/es/connectors";

import { checkBoxElement } from "../../forms/input.js";

const renderRefinementList = (renderOptions, isFirstRender) => {
	const { canRefine, items, refine, widgetParams } = renderOptions;

	const container = document.querySelector(widgetParams.container);
	const optionsContainer = document.createElement("ul");

	container.parentElement.classList.remove("u-hidden");
	optionsContainer.classList.add("RefinementList-itemsContainer");

	/**
	 * Handles the refinement submission when a change event occurs.
	 * @param {Event} event - The change event triggered by the input element.
	 */
	function submitRefinement(event) {
		refine(event.target.value);
	}

	if (isFirstRender) {
		container.addEventListener("change", submitRefinement);
		return;
	}

	if (!canRefine) {
		container.removeEventListener("change", submitRefinement);
		container.parentElement.classList.add("u-hidden");
	}

	if (!isFirstRender && canRefine) {
		const refinementItemsList = items.map((item) => {
			const { isRefined, label, value } = item;
			const listItem = document.createElement("li");
			const checkboxInputOptions = {
				name: label,
				value: value,
				label: label,
				isChecked: isRefined,
			};

			const currentCheckBoxElement = checkBoxElement(checkboxInputOptions);
			listItem.append(currentCheckBoxElement);

			return listItem;
		});

		container.innerHTML = "";
		container.append(optionsContainer);
		optionsContainer.append(...refinementItemsList);
	}
};

const customRefinementList = connectRefinementList(renderRefinementList);

export { customRefinementList };

/**
 * Generates a refinement list element based on the provided refinement list data.
 * @param {object} refinementListData - The refinement list data object.
 * @param {string} refinementListData.facet_key - The key of the facet in Algolia.
 * @param {string} refinementListData.title - The title of the refinement list.
 * @returns {DocumentFragment} The generated refinement list element.
 */
export const getRefinementList = (refinementListData) => {
	const { facet_key, title } = refinementListData;
	const refinementListTmpl = document.querySelector(
		"#extranet-refinement-list-tmpl",
	);
	const refinementList = refinementListTmpl.content.cloneNode(true);
	const facetId = facet_key.split("_").join("-").concat("-refinement-list");
	const mql = window.matchMedia("(width < 48rem)");

	if (!mql.matches) {
		const finstralCustomDisclosure = refinementList.querySelector(
			".FinstralCustomDisclosure",
		);
		finstralCustomDisclosure.setAttribute("name", "refinement-list");
	}

	const filtersContainer = refinementList.querySelector(
		".FinstralCustomDisclosure-itemsContainer",
	);
	const customDisclosure = refinementList.querySelector(
		"finstral-custom-disclosure",
	);
	const disclosureToggle = customDisclosure.querySelector(
		".FinstralCustomDisclosure-toggle",
	);
	customDisclosure.setAttribute("label", title);

	disclosureToggle.setAttribute("aria-controls", facetId);
	filtersContainer.id = facetId;

	return refinementList;
};
