/* @define RefinementTags; weak; */

.RefinementTags {
	display: flex;
	flex-wrap: wrap;
	gap: var(--size-8);
}

.RefinementTags-wrapper {
	margin-block-start: var(--shared-spacing-sections-sm);
}

.RefinementTags-item {
	display: flex;
	flex-wrap: wrap;
	gap: var(--size-8);
}

.RefinementTags-category {
	align-items: center;
	background-color: var(--shared-color-surface-subtle);
	block-size: var(--RefinementTags-tag-block-size);
	display: flex;
	gap: var(--size-8);
	padding-inline: var(--size-8);

	.ais-CurrentRefinements-categoryLabel {
		max-inline-size: var(--CategoryLabel-width);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

.RefinementTags-refinementLabel,
.RefinementTags:has(.RefinementTags-empty) .RefinementTags-clear {
	display: none;
}

.RefinementTags-clear button {
	background-color: var(--shared-color-surface-invert);
	block-size: var(--RefinementTags-tag-block-size);
	color: var(--shared-color-text-invert);
	padding-inline: var(--size-8);
}

@media (width < 64rem) {
	.RefinementTags-category,
	.RefinementTags-clear button {
		--RefinementTags-tag-block-size: var(--size-24);
	}

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.ais-CurrentRefinements-categoryLabel {
		--CategoryLabel-width: 7rem;
	}
}

@media (width >= 64rem) {
	.RefinementTags-category,
	.RefinementTags-clear button {
		--RefinementTags-tag-block-size: var(--size-32);
	}

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.ais-CurrentRefinements-categoryLabel {
		--CategoryLabel-width: 8.75rem;
	}
}
