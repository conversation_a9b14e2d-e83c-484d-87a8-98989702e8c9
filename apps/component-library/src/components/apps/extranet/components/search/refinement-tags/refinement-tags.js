import {
	clearRefinements,
	currentRefinements,
} from "instantsearch.js/es/widgets";

/**
 * Creates and returns a current refinements widget for the given container.
 * @param {HTMLElement|string} [container] - The container element or its CSS selector where
 * the current refinements widget will be rendered. [default: "#current-refinements"]
 * @returns {object} The current refinements widget instance.
 */
export const initCurrentRefinements = (container = "#current-refinements") => {
	return currentRefinements({
		cssClasses: {
			category: "RefinementTags-category",
			delete: "RefinementTags-refinementDelete",
			item: "RefinementTags-item",
			label: "RefinementTags-refinementLabel",
			list: "RefinementTags",
			noRefinementRoot: "RefinementTags-empty",
		},
		container,
	});
};

/**
 * Creates and returns a clear refinements widget for the given container.
 * @param {HTMLElement|string} [container] - The container element or its CSS selector where
 * the clear refinements widget will be rendered. [default: "#clear-refinements"]
 * @returns {object} The clear refinements widget instance.
 */
export const initClearRefinements = (container = "#clear-refinements") => {
	const clearRefinementsElement = document.querySelector(container);
	const clearRefinementsOptions = {
		cssClasses: {
			root: "RefinementTags-clear",
		},
		container,
	};

	if (clearRefinements) {
		const buttonLabel = clearRefinementsElement.dataset.clearRefinements;
		clearRefinementsOptions.templates = {
			resetLabel({ hasRefinements }, { html }) {
				return html`<span>${hasRefinements ? buttonLabel : ""}</span>`;
			},
		};
	}

	return clearRefinements({
		...clearRefinementsOptions,
	});
};
