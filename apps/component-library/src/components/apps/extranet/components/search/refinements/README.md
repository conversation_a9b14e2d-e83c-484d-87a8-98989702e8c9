The refinements component is a wrapper for search refinement elements. Namely `refinement-list` and `sort-by`, both in turn using the `finstral-custom-disclosre` component.

On desktop `refinements` is displayed between the searchbar (`searchbox`) and the `result-bar`. `sort-by` is visually hidden and instead displayed inside the `result-bar`.

On mobile `refinements` is initially hidden and becomes visible fullscreen (like a dialog) via the `Refinements-openButton` button inside the template.
