/** @define Refinements; weak; */

/* mobile */
@media (width < 48rem) {
	.Refinements-openButton {
		inline-size: 100%;
		margin-block-start: var(--size-16);
	}

	.Refinements {
		--display-mode: flex;

		background-color: var(--shared-color-surface-primary);
		block-size: 100dvb; /* dynamic viewport-percentage units - https://drafts.csswg.org/css-values/#viewport-variants */
		display: none;
		flex-direction: column;
		inset: 0;
		overflow-y: scroll;
		position: fixed;
		z-index: var(--bring-to-front);
	}

	.Refinements-header {
		align-items: center;
		border-block-end: var(--border-subtle-transparent);
		display: flex;
		gap: var(--size-16);
		justify-content: space-between;
		padding: var(--size-16);
	}

	.Refinements-submitButton {
		inline-size: 100%;
		margin-block: var(--size-16);
	}

	.Refinements-close {
		/* this is to ensure none of the child element trigger events */
		* {
			pointer-events: none;
		}
	}

	/* includes u-container styles only for the mobile layout */
	.Refinements-content {
		block-size: 100%;
		display: flex;
		flex: 1;
		flex-direction: column;
		inline-size: min(
			100%,
			calc(var(--container-max-inline-size) + var(--component-spacing-inline))
		);
		justify-content: space-between;
		margin-inline: auto;
		padding-inline: var(--component-spacing-inline);
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.Refinements-openButton,
	.Refinements-header,
	.Refinements-submitButton {
		display: none;
	}

	.Refinements {
		display: block;
		margin-block-start: var(--size-16);
	}

	.Refinements-filters {
		display: grid;
		gap: var(--size-16);
		grid-template-columns: repeat(3, 1fr);
	}
}

/* desktop */
@media (width >= 62rem) {
	.Refinements-filters {
		grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
	}
}
