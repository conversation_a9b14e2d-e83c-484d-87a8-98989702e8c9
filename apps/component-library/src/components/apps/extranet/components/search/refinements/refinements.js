export class FinstralRefinements extends HTMLElement {
	static #selectors = {
		openRefinementsButton: ".Refinements-openButton",
	};

	#elements;
	/**
	 * Retrieves the elements based on the defined selectors.
	 * @returns {object} An object containing the selected elements.
	 */
	#getElements() {
		return {
			openRefinementsButton: document.querySelector(
				FinstralRefinements.#selectors.openRefinementsButton,
			),
		};
	}

	/**
	 * Initializes a new instance of the FinstralRefinements component.
	 */
	constructor() {
		super();
		this.#elements = this.#getElements();
		this.#addEventListeners();
	}

	/**
	 * Adds event listeners for opening and closing the refinements modal.
	 */
	#addEventListeners() {
		const { openRefinementsButton } = this.#elements;
		openRefinementsButton.addEventListener("click", () =>
			this.classList.add("u-modal-open"),
		);
		this.addEventListener("click", (event) => {
			const target = event.target;
			if (target.classList.contains("Refinements-close")) {
				this.classList.remove("u-modal-open");
			}
		});
	}
}

customElements.define("finstral-refinements", FinstralRefinements);
