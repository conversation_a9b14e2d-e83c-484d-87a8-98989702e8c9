{{ attach_library('finstral_global/apps-extranet-refinements') }}

{% include "@elements/button/button.twig" with {
	label: label,
	variant: "secondary",
	icon: {name: "filter"},
	classes: ["Refinements-openButton u-hidden"],
} only %}
<finstral-refinements class="Refinements u-hidden">
	<header class="Refinements-header">
		{{ label }}
		{% include "@elements/button/button.twig" with {
			label: "global.close_button"|tc,
			variant: "tertiary",
			icon: {
				name: "close",
				position: "icon_only",
				},
			classes: ["Refinements-close"],
		} only %}
	</header>
	<div class="Refinements-content">
		<div>
			<div class="SortBy--mobile">
				{% include "@apps/extranet/components/search/sort-by/sort-by.twig" %}
			</div>
			<div class="Refinements-filters"></div>
		</div>
		{% include "@elements/button/button.twig" with {
			label: "search.refinements.submit"|tc,
			classes: ["Refinements-submitButton", "Refinements-close"]
		} only %}
	</div>
</finstral-refinements>
