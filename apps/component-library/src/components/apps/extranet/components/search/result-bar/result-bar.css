@import "./toggle/toggle.css";

/** @define ResultBar;  */

.ResultBar {
	align-items: center;
	border-block-start: var(--border-subtle-transparent);
	display: flex;
	justify-content: space-between;
	margin-block-start: var(--shared-spacing-sections-sm);
	padding-block-start: var(--ResultBar-padding-block-start);
}

.ResultBar-count {
	color: var(--shared-color-text-secondary);
}

.ResultBar-settings {
	align-items: center;
	display: flex;
	gap: var(--size-16);
}

/* mobile */
@media (width < 48rem) {
	.ResultBar {
		--ResultBar-padding-block-start: var(--size-16);
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.ResultBar {
		--ResultBar-padding-block-start: var(--size-32);
	}
}
