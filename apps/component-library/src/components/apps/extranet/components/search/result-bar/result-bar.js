import { connectStats } from "instantsearch.js/es/connectors";

// for details, please review: https://www.algolia.com/doc/api-reference/widgets/stats/js/#create-a-render-function
const renderStats = (stats) => {
	const { nbHits, widgetParams } = stats;
	widgetParams.container.textContent = `${nbHits}`;
};

const customStats = connectStats(renderStats);

/**
 * Initializes the custom stats widget for displaying the number of search results.
 * @param {string|HTMLElement} [statsContainer] - The container element or selector where the stats will be displayed. [Default: ".ResultBar-count span"]
 * @example
 * // Initialize the stats widget with a specific container
 * initStats('#my-stats-container');
 * @example
 * // Initialize the stats widget with the default container
 * initStats();
 * @returns {object} The custom stats widget instance.
 */
export const initStats = (statsContainer = ".ResultBar-count span") => {
	if (typeof statsContainer === "string") {
		statsContainer = document.querySelector(statsContainer);
	}

	// https://www.algolia.com/doc/api-reference/widgets/stats/js/
	return customStats({
		container: statsContainer,
	});
};

/**
 * Updates the search summary using the provided query string or a fallback.
 * @param {string} query - The search query.
 * @example
 * updateSearchSummary('windows');
 * @returns {void}
 */
export const updateSearchSummary = (query) => {
	const searchSummary = document.querySelector(".Resultbar-searchSummary");
	const searchSummaryTxt = searchSummary.querySelector("span");
	const summaryFallback = searchSummary.dataset.summaryFallback;

	searchSummaryTxt.textContent = query.trim() ? query : summaryFallback;
};
