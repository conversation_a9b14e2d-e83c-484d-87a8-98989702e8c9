export class ResultbarToggle extends HTMLElement {
	/**
	 * Creates an instance of ResultbarToggle.
	 */
	constructor() {
		super();
		this.#addEventListeners();
	}

	/**
	 * Adds event listeners to the component.
	 * @private
	 */
	#addEventListeners() {
		const extranetResultsContainer = document.querySelector(".ExtranetResults");
		if (!extranetResultsContainer) {
			return;
		}
		this.addEventListener("change", (event) => {
			const target = event.target;
			const classes =
				target.id === "list"
					? "ExtranetResults ExtranetResults--list"
					: "ExtranetResults ExtranetResults--grid";
			extranetResultsContainer.classList = classes;
		});
	}
}

customElements.define("resultbar-toggle", ResultbarToggle);
