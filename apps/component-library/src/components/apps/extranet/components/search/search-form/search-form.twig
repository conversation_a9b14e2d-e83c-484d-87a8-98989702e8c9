{{ attach_library('finstral_global/apps-extranet-components-search-searchform') }}

<search class="SearchForm">
	<form name="extranet-search" action="/{{ langcode }}/compendium/search" method="get">
		<label for="query" class="u-hiddenVisually">{{ 'search.placeholder'|tc }}</label>
		<input type="text" name="query" id="query" placeholder="{{ 'search.placeholder'|tc }}" class="Input">
		<button type="submit" class="SearchForm-button">
			<span class="u-hiddenVisually">{{ 'search.submit_label'|tc }}</span>
			{% include "@elements/icon/icon.twig" with {
				name: "search"
			} only %}
		</button>
	</form>
</search>
