/**
 * Generates an asset card element based on the provided asset data.
 * @param {object} asset - The asset data object.
 * @param {string} asset.title - The title of the asset.
 * @param {number} asset.asset_height - The height of the asset preview image.
 * @param {string} asset.asset_preview_url - The URL of the asset preview image.
 * @param {number} asset.asset_width - The width of the asset preview image.
 * @param {string} asset.asset_detail_url - The URL of the asset detail page.
 * @param {string} asset.file_type - The file type of the asset.
 * @param {number} asset.file_size - The file size of the asset in bytes.
 * @param {boolean} [asListItem] - Flag to determine if the asset card should be displayed as a list item. [default=false]
 * @returns {DocumentFragment} The generated asset card element.
 */
export const getAssetCard = (asset, asListItem = false) => {
	const assetCardTmpl = document.querySelector("#extranet-asset-card-tmpl");
	const assetCardId = self.crypto.randomUUID();

	const assetCard = assetCardTmpl.content.cloneNode(true);
	const assetCardWrapper = assetCard.querySelector(".AssetCard");
	const assetCardImage = assetCard.querySelector(".AssetCard-image");
	const fileType = assetCard.querySelector(".AssetCard-metaType");
	const fileSize = assetCard.querySelector(".AssetCard-metaSize");
	const assetCardLink = assetCard.querySelector(".AssetCard-link");
	const assetCardTitle = assetCard.querySelector(".AssetCard-title");
	const assetCardAmount = assetCard.querySelector(".AssetCard-groupAmount");

	assetCardImage.alt = asset.title;
	assetCardImage.height = asset.asset_height || "260";
	assetCardImage.src = asset.asset_preview_url;
	assetCardImage.width = asset.asset_width || "260";

	assetCardLink.href = asset.asset_detail_url;
	assetCardLink.setAttribute("aria-labelledby", assetCardId);

	assetCardTitle.id = assetCardId;
	assetCardTitle.textContent = asset.title;

	// set meta info only if single asset, otherwise use strings in template
	if (!asset.group_size && asset.group_size < 1) {
		fileType.textContent = asset.file_type;
		fileSize.textContent = asset.file_size;
	}

	if (asset.group_size && asset.group_size > 1) {
		assetCardAmount.removeAttribute("hidden");
		assetCardAmount.textContent = asset.group_size;
	}

	const popover = createAssetCardPopover(asset);

	assetCardWrapper.append(popover);

	// prevent page scroll when popover is open
	const assetCardPopover = assetCardWrapper.querySelector(".AssetCard-popover");
	assetCardPopover.addEventListener("beforetoggle", (event) => {
		if (event.newState === "open") {
			assetCardPopover.classList.add("u-modal-open");
		} else {
			assetCardPopover.classList.remove("u-modal-open");
		}
	});

	if (asListItem) {
		const listItem = document.createElement("li");
		listItem.append(assetCard);
		return listItem;
	}

	return assetCard;
};

/**
 * Generates download link elements for the provided asset downloads.
 * @param {Array} assetDownloads - An array of asset download objects.
 * @param {string} assetDownloads[].asset_download_path - The download path of the asset.
 * @param {string} assetDownloads[].asset_download_label - The label for the asset download.
 * @returns {Array<DocumentFragment>} An array of document fragments representing download links.
 */
function getDownloadLinks(assetDownloads) {
	if (!assetDownloads.length) {
		return [];
	}
	const downloadLinks = assetDownloads.map((download) => {
		const downloadItemTmpl = document.querySelector(
			"#extranet-asset-card-download-entry-tmpl",
		);
		const downloadItem = downloadItemTmpl.content.cloneNode(true);
		const assetDownloadLink = downloadItem.querySelector(
			".FinstralAssetDownload",
		);
		assetDownloadLink.setAttribute(
			"download-href",
			download.asset_download_path,
		);
		assetDownloadLink.setAttribute("filename", download.asset_download_label);
		return downloadItem;
	});
	return downloadLinks;
}

/**
 * Creates the popover for each card with link to details page and download link(s) for the asset.
 * @param {object} asset - The asset data object.
 * @returns {DocumentFragment} - The generated popover element.
 */
function createAssetCardPopover(asset) {
	const assetCardPopoverTmpl = document.querySelector(
		"#extranet-asset-card-popover-tmpl",
	);

	const assetPopover = assetCardPopoverTmpl.content.cloneNode(true);

	const assetCardPopover = assetPopover.querySelector(".AssetCard-popover");
	const viewDetailsLink = assetPopover.querySelector(".AssetCard-detailLink");
	const downloadLinksContainer = assetCardPopover.querySelector(
		".AssetCard-downloadList",
	);

	viewDetailsLink.href = asset.asset_detail_url;
	downloadLinksContainer.append(...getDownloadLinks(asset.asset_downloads));

	return assetPopover;
}
