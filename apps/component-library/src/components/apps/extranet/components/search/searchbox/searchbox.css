/* @define SearchBox; weak */

.SearchBox form {
	position: relative;
}

.SearchBox-button {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='currentColor'%3E%3Cpath d='m19.543 20.577-6.281-6.28q-.75.618-1.725.968t-2.017.35q-2.565 0-4.34-1.775-1.776-1.775-1.776-4.338t1.776-4.34 4.338-1.777q2.563 0 4.34 1.776 1.777 1.775 1.777 4.34 0 1.07-.36 2.045a5.7 5.7 0 0 1-.96 1.696l6.281 6.281zM9.52 14.115q1.932 0 3.274-1.34 1.341-1.342 1.341-3.275t-1.341-3.274Q11.452 4.885 9.52 4.885q-1.933 0-3.274 1.341Q4.903 7.568 4.904 9.5q0 1.933 1.342 3.274 1.34 1.342 3.274 1.342Z'/%3E%3C/svg%3E");
	block-size: var(--size-24);
	inline-size: var(--size-24);
	inset-block-start: 50%;
	inset-inline-start: var(--size-8);
	position: absolute;
	transform: translateY(-50%);
}

.SearchBox-button svg {
	display: none;
}

/* make room for absolute positioned button */
.SearchBox .Input {
	padding-inline-start: var(--size-40);

	&::-webkit-search-cancel-button {
		display: none;
	}
}
