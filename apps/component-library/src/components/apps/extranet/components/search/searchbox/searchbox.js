import { searchBox } from "instantsearch.js/es/widgets";

const defaultSearchboxOptions = {
	cssClasses: {
		input: "Input",
		root: "SearchBox",
		submit: "SearchBox-button",
	},
	container: "",
	placeholder: "",
	searchAsYouType: false,
	showLoadingIndicator: false,
	showReset: false,
	showSubmit: true,
};

/**
 * Initializes the search box widget.
 * @param {object} searchBoxOptions - Options to customize the search box.
 * @returns {object} The initialized search box widget.
 */
export const initSearchBox = (searchBoxOptions) => {
	const searchContainer = document.querySelector(".ExtranetSearch-search");
	const placeholder = searchContainer.dataset.placeholder;

	defaultSearchboxOptions.placeholder = placeholder;

	return searchBox({ ...defaultSearchboxOptions, ...searchBoxOptions });
};
