import { connectSortBy } from "instantsearch.js/es/connectors";

import {
	getCurrentAlgoliaIndexName,
	getAlgoliaIndexNameByType,
} from "../../../components/search/client-utils.js";
import { radioElement } from "../../forms/input.js";

const renderSortBy = (renderOptions, isFirstRender) => {
	const { options, widgetParams, refine, currentRefinement } = renderOptions;
	const sortByContainer = document.querySelector(widgetParams.container);
	const disclosureDrawer = sortByContainer.querySelector(
		".FinstralCustomDisclosure-itemsContainer",
	);

	// called during the init lifecycle step - before the first search
	if (isFirstRender) {
		const optionsContainer = document.createElement("ul");
		optionsContainer.classList.add("SortBy-itemsContainer");
		disclosureDrawer.append(optionsContainer);

		disclosureDrawer.addEventListener("change", (event) => {
			refine(event.target.value);
		});
	}

	// the below is run each time results come back from Algolia (render lifecycle step).
	const mountedOptionsContainer = disclosureDrawer.querySelector(
		".SortBy-itemsContainer",
	);

	if (!mountedOptionsContainer || !disclosureDrawer) {
		return;
	}

	const optionsList = options.map((currentOption) => {
		const optionItem = document.createElement("li");
		optionItem.classList.add("SortBy-item");
		// ensures that the name attribute for the mobile and desktop
		// radio inputs are not the same.
		const nameAttr = widgetParams.container.replace(".", "").toLowerCase();
		const radioInputOptions = {
			name: `${nameAttr}-filter`,
			value: currentOption.value,
			label: currentOption.label,
			isChecked: currentOption.value === currentRefinement,
		};

		const currentRadioElement = radioElement(radioInputOptions);
		const iconTpl = document.querySelector("#SortByArrows");
		let icon;
		if (iconTpl) {
			icon = iconTpl.content.cloneNode(true);
			const currentRadioElementLabel =
				currentRadioElement.querySelector("label");
			currentRadioElementLabel.append(icon);
			if (currentOption.value.includes("_asc")) {
				currentRadioElementLabel.classList.add("SortBy-label--ascending");
			}
		}

		optionItem.append(currentRadioElement);

		return optionItem;
	});

	mountedOptionsContainer.innerHTML = "";
	mountedOptionsContainer.append(...optionsList);
};

export const initSortBy = (container) => {
	let sortByLabelsParsed;
	try {
		const sortByLabels = document.querySelector("#sort-by-labels");

		if (!sortByLabels) {
			throw new Error("Sorting labels not configured");
		}

		sortByLabelsParsed = JSON.parse(sortByLabels.innerHTML);
	} catch (error) {
		console.error(`Error parsing sort by labels: ${error}`);
		return;
	}

	const options = [
		{
			label: sortByLabelsParsed["dateAsc"],
			value: getAlgoliaIndexNameByType("changed_asc"),
		},
		{
			label: sortByLabelsParsed["dateDesc"],
			value: getCurrentAlgoliaIndexName(),
		},
		{
			label: sortByLabelsParsed["nameAsc"],
			value: getAlgoliaIndexNameByType("title_asc"),
		},
		{
			label: sortByLabelsParsed["nameDesc"],
			value: getAlgoliaIndexNameByType("title_desc"),
		},
	];

	const customSortBy = connectSortBy(renderSortBy);

	return customSortBy({
		items: options,
		container,
	});
};
