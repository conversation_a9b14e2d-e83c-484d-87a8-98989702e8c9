/* @define SortBy; */

.SortBy-icon {
	inline-size: var(--size-16);
	margin-inline-start: var(--size-4);
}

.SortBy-label--ascending .SortBy-icon {
	transform: rotate(180deg);
}

@media screen and (width < 48rem) {
	.SortBy {
		display: none;
	}

	.SortBy--mobile {
		display: block;
	}
}

@media screen and (width >= 48rem) {
	.SortBy {
		display: block;
		min-inline-size: 16rem; /* magic number taken from design */
	}

	.SortBy--mobile {
		display: none;
	}
}
