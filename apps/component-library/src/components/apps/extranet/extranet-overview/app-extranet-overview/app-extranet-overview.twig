{{ attach_library('finstral_global/apps-app-extranet-overview') }}

{% set data = {
	indexName: index_name,
	langCode: langcode,
} %}

<section class="u-container">
	<h1 class="u-typo-HeadlineM">
		{{ "overview.title"|tc }}
	</h1>

	{% include "@apps/extranet/components/search/search-form/search-form.twig" with {
		langcode: langcode,
	} only %}

	{% include "@apps/extranet/extranet-overview/app-extranet-overview/latest-assets/latest-assets.twig" %}

	<div class="ExtranetOverview-categories">
		{% include "@patterns/teaser-card-list/teaser-card-list.twig" with {
				heading: categories.heading,
				items: categories.items,
			} only %}
	</div>
</section>

<script id="algolia-data" type="application/json">{{ data|json_encode()|raw }}</script>
