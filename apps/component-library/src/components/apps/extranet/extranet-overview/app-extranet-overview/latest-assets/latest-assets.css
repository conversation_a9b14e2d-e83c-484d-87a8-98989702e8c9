/* define LatestAssets; weak; */

latest-assets {
	display: block;
	margin-block: var(--shared-spacing-sections-md);
}

.LatestAssets-container-list {
	display: grid;
	gap: var(--size-16);
	margin-block-start: var(--size-32);
}

.LatestAssets-container-list li {
	min-inline-size: 0;
}

/* mobile */
@media (width < 48rem) {
	.LatestAssets-container-list {
		grid-template-columns: repeat(2, 1fr);
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.LatestAssets-container-list {
		grid-template-columns: repeat(4, 1fr);
	}
}
