import { getLatestAssets } from "../../../components/search/client.js";
import { getAssetCard } from "../../../components/search/search-ui-utils.js";

export class LastestAssets extends HTMLElement {
	static #selectors = {
		container: ".LatestAssets-container",
		loader: ".LoaderWrapper",
		list: ".LatestAssets-container-list",
	};

	#assets;
	#elements;

	/**
	 * Retrieves the DOM elements used by the component.
	 * @private
	 * @returns {object} An object containing references to the container and loader elements.
	 */
	#getElements() {
		return {
			container: this.querySelector(LastestAssets.#selectors.container),
			loader: this.querySelector(LastestAssets.#selectors.loader),
			list: this.querySelector(LastestAssets.#selectors.list),
		};
	}

	/**
	 * Constructs an instance of LastestAssets.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		this.#init();
	}

	/**
	 * Initializes the LastestAssets by fetching the latest assets and rendering them.
	 * @private
	 */
	async #init() {
		const { container, loader } = this.#elements;
		loader.classList.remove("u-hidden");

		this.#assets = await getLatestAssets(/** hitsPerPage */ 4);

		if (this.#assets.length) {
			loader.classList.add("u-hidden");
			container.classList.remove("u-hidden");
			this.#render();
		}
	}

	/**
	 * Renders the asset list with the latest assets.
	 * @private
	 */
	async #render() {
		const { container, list } = this.#elements;

		const cards = this.#assets.map((asset) => {
			return getAssetCard(asset, /* asListItem */ true);
		});

		list.append(...cards);
		container.append(list);
	}
}

customElements.define("latest-assets", LastestAssets);
