import {
	getAlgoliaSearchParametersConfiguration,
	getInstantSearchClient,
} from "../../components/search/client";
import { getCurrentAlgoliaIndexName } from "../../components/search/client-utils";
import { initSearchBox } from "../../components/search/searchbox/searchbox";
import { initExtranetResults } from "../../components/search/extranet-results/extranet-results";
import {
	initStats,
	updateSearchSummary,
} from "../../components/search/result-bar/result-bar";
import { initSortBy } from "../../components/search/sort-by/connect-sort-by.js";
import {
	hideLoader,
	showLoader,
} from "../../components/search/loader/loader.js";

export class ExtranetSearch extends HTMLElement {
	static #selectors = {
		resultBarSettings: ".ResultBar-settings",
		resultsContainer: ".ExtranetSearch-resultsContainer",
		refinementsOpenButton: ".Refinements-openButton",
	};

	#elements;
	#instantSearch;

	/**
	 * Retrieves the elements used in the search component.
	 * @returns {object} An object containing references to the DOM elements.
	 * @private
	 */
	#getElements() {
		return {
			resultBarSettings: this.querySelector(
				ExtranetSearch.#selectors.resultBarSettings,
			),
			resultsContainer: this.querySelector(
				ExtranetSearch.#selectors.resultsContainer,
			),
			refinementsOpenButton: this.querySelector(
				ExtranetSearch.#selectors.refinementsOpenButton,
			),
		};
	}

	/**
	 * Creates an instance of ExtranetSearch.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		this.#init();
	}

	/**
	 * Updates the state of the search component.
	 * @private
	 * @param {string} query - The search query.
	 */
	#updateState(query) {
		const currentURL = new URL(window.location);

		if (query.trim() === "") {
			currentURL.searchParams.delete("query");
		} else {
			currentURL.searchParams.set("query", query);
		}

		window.history.replaceState({}, "", currentURL);
		updateSearchSummary(query);
	}

	/**
	 * Initializes the search component.
	 * @private
	 */
	async #init() {
		const searchBoxOptions = {
			container: this.querySelector("search"),
			showLoadingIndicator: false,
			// This function is called when the user submits the search form.
			// Critically _not_ during the initial search on page load.
			queryHook: (query, search) => {
				const { resultBarSettings, resultsContainer } = this.#elements;

				// Show the loader when the search is triggered
				// and hide the results container.
				showLoader();
				resultsContainer.hidden = true;
				resultBarSettings.classList.add("u-hidden");

				this.#updateState(query);
				search(query);
			},
		};

		this.#instantSearch = await getInstantSearchClient();

		this.#instantSearch.addWidgets([
			initSearchBox(searchBoxOptions),
			initExtranetResults,
			initStats(),
			initSortBy(".SortBy--mobile"),
			initSortBy(".SortBy"),
			getAlgoliaSearchParametersConfiguration(),
		]);

		this.#instantSearch.start();
		this.#addEventListeners();

		showLoader();

		const searchParams = new URLSearchParams(window.location.search);
		const query = searchParams.get("query") || "";

		updateSearchSummary(query);

		// This ensures that the query passed via the URL is reflected in the search box
		// and passed to the InstantSearch client.
		const indexName = getCurrentAlgoliaIndexName();
		this.#instantSearch.setUiState({
			[indexName]: {
				query,
			},
		});
	}

	/**
	 * Adds event listeners for handling search-related events.
	 * @private
	 */
	#addEventListeners() {
		document.addEventListener("renderHitsStatus", (event) => {
			const { hasResults, status } = event.detail;
			const { resultBarSettings, resultsContainer, refinementsOpenButton } =
				this.#elements;
			// can be one of "idle" | "loading" | "stalled" | "error"
			// we are interested in "idle" as this means the search
			// has settled and we can update the UI.
			const instantSearchStatus = this.#instantSearch.status;
			const mql = window.matchMedia("(width < 48rem)");

			if (
				status.toLowerCase() === "ready" &&
				instantSearchStatus.toLowerCase() === "idle"
			) {
				hideLoader();

				if (hasResults) {
					resultBarSettings.classList.remove("u-hidden");
				}

				if (hasResults && mql.matches) {
					refinementsOpenButton.classList.remove("u-hidden");
				}

				resultsContainer.hidden = false;
			}
		});
	}
}

customElements.define("app-extranet-search", ExtranetSearch);
