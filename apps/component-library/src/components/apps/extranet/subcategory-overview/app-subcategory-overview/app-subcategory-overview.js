import {
	getAlgoliaSearchParametersConfiguration,
	getInstantSearchClient,
} from "../../components/search/client";
import { getCurrentAlgoliaIndexName } from "../../components/search/client-utils";
import { initSearchBox } from "../../components/search/searchbox/searchbox";
import { initExtranetResults } from "../../components/search/extranet-results/extranet-results";

import {
	initClearRefinements,
	initCurrentRefinements,
} from "../../components/search/refinement-tags/refinement-tags";
import {
	customRefinementList,
	getRefinementList,
} from "../../components/search/refinement-list/refinement-list";
import {
	initStats,
	updateSearchSummary,
} from "../../components/search/result-bar/result-bar";
import { initSortBy } from "../../components/search/sort-by/connect-sort-by.js";
import {
	hideLoader,
	showLoader,
} from "../../components/search/loader/loader.js";

export class SubcategoryOverview extends HTMLElement {
	static #selectors = {
		refinements: "finstral-refinements",
		refinementListsContainer: ".Refinements-filters",
		refinementsOpenButton: ".Refinements-openButton",
		resultBarSettings: ".ResultBar-settings",
		resultsContainer: ".ExtranetSearch-resultsContainer",
		subcategoryOverviewData: "#subcategory-overview-data",
	};

	#elements;
	#instantSearch;

	#state = {
		refinementLists: [],
		subcategory: {},
	};

	/**
	 * Gets the elements required for the component.
	 * @private
	 * @returns {object} The elements required for the component.
	 */
	#getElements() {
		return {
			refinements: this.querySelector(
				SubcategoryOverview.#selectors.refinements,
			),
			refinementListsContainer: this.querySelector(
				SubcategoryOverview.#selectors.refinementListsContainer,
			),
			refinementsOpenButton: this.querySelector(
				SubcategoryOverview.#selectors.refinementsOpenButton,
			),
			resultBarSettings: this.querySelector(
				SubcategoryOverview.#selectors.resultBarSettings,
			),
			resultsContainer: this.querySelector(
				SubcategoryOverview.#selectors.resultsContainer,
			),
			subcategoryOverviewData: document.querySelector(
				SubcategoryOverview.#selectors.subcategoryOverviewData,
			),
		};
	}

	/**
	 * Creates an instance of SubcategoryOverview.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		if (!this.#elements.subcategoryOverviewData) {
			throw new Error("No subcategory overview data found.");
		}

		this.#setState();

		if (this.#state.refinementLists.length) {
			this.#appendRefinementListContainers();
		}

		this.#render();
	}

	/**
	 * Updates the state of the search component.
	 * @private
	 * @param {string} query - The search query.
	 */
	#updateState(query) {
		const currentURL = new URL(window.location);

		if (query.trim() === "") {
			currentURL.searchParams.delete("query");
		} else {
			currentURL.searchParams.set("query", query);
		}

		window.history.replaceState({}, "", currentURL);
		updateSearchSummary(query);
	}

	/**
	 * Renders the subcategory overview component.
	 * @private
	 */
	async #render() {
		const { subcategory } = this.#state;

		const searchBoxOptions = {
			container: this.querySelector("search"),
			showLoadingIndicator: false,
			// This function is called when the user submits the search form.
			// Critically _not_ during the initial search on page load.
			queryHook: (query, search) => {
				const { resultBarSettings, resultsContainer } = this.#elements;

				// Show the loader when the search is triggered
				// and hide the results container.
				showLoader();
				resultsContainer.hidden = true;
				resultBarSettings.classList.add("u-hidden");

				updateSearchSummary(query);
				this.#updateState(query);
				search(query);
			},
		};

		this.#instantSearch = await getInstantSearchClient();

		this.#instantSearch.addWidgets([
			initSearchBox(searchBoxOptions),
			initExtranetResults,
			initCurrentRefinements(),
			initClearRefinements(),
			initStats(),
			initSortBy(".SortBy--mobile"),
			initSortBy(".SortBy"),
			getAlgoliaSearchParametersConfiguration(
				`subcategory:${subcategory.value}`,
			),
			...this.#getRefinementListWidgets(),
		]);

		this.#instantSearch.start();
		this.#addEventListeners();

		showLoader();

		const searchParams = new URLSearchParams(window.location.search);
		const query = searchParams.get("query") || "";

		updateSearchSummary(subcategory.label);

		// This ensures that the query passed via the URL is reflected in the search box
		// and passed to the InstantSearch client.
		const indexName = getCurrentAlgoliaIndexName();
		this.#instantSearch.setUiState({
			[indexName]: {
				query,
			},
		});
	}

	/**
	 * Sets the state of the component by parsing the subcategory overview data
	 * and storing it in localStorage.
	 * @private
	 */
	#setState() {
		const { subcategoryOverviewData } = this.#elements;
		const data = JSON.parse(subcategoryOverviewData.textContent);

		try {
			localStorage.setItem("subcategoryOverviewData", JSON.stringify(data));
			this.#state = { ...data };
		} catch (error) {
			throw new Error(`Error setting state in subcategory overview: ${error}`);
		}
	}

	/**
	 * Appends refinement list containers to the refinement lists container.
	 * @private
	 */
	#appendRefinementListContainers() {
		const { refinementLists } = this.#state;
		const { refinementListsContainer } = this.#elements;

		refinementLists.forEach((refinementList) => {
			const refinementListElement = getRefinementList(refinementList);
			refinementListsContainer.append(refinementListElement);
		});
	}

	/**
	 * Gets the refinement list widgets.
	 * @private
	 * @returns {Array} The refinement list widgets.
	 */
	#getRefinementListWidgets() {
		const { refinementLists } = this.#state;

		return refinementLists.map((facet) => {
			const { facet_key } = facet;
			const containerId = facet_key
				.split("_")
				.join("-")
				.concat("-refinement-list");
			return customRefinementList({
				attribute: facet_key,
				cssClasses: {
					count: "RefinementList-count",
				},
				container: `#${containerId}`,
				sortBy: ["name"], // @see https://www.algolia.com/doc/api-reference/widgets/refinement-list/js/#widget-param-sortby
				limit: 30, // @see https://www.algolia.com/doc/api-reference/widgets/refinement-list/js/#widget-param-limit
			});
		});
	}

	/**
	 * Adds event listeners for handling search-related events.
	 * @private
	 */
	#addEventListeners() {
		document.addEventListener("renderHitsStatus", (event) => {
			const { hasResults, status } = event.detail;
			const {
				refinements,
				resultBarSettings,
				resultsContainer,
				refinementsOpenButton,
			} = this.#elements;
			// can be one of "idle" | "loading" | "stalled" | "error"
			// we are interested in "idle" as this means the search
			// has settled and we can update the UI.
			const instantSearchStatus = this.#instantSearch.status;
			const mql = window.matchMedia("(width < 48rem)");

			if (
				status.toLowerCase() === "ready" &&
				instantSearchStatus.toLowerCase() === "idle"
			) {
				hideLoader();

				if (hasResults) {
					resultBarSettings.classList.remove("u-hidden");
					refinements.classList.remove("u-hidden");
				}

				if (hasResults && mql.matches) {
					refinementsOpenButton.classList.remove("u-hidden");
				}

				resultsContainer.hidden = false;
			}
		});
	}
}

customElements.define("app-subcategory-overview", SubcategoryOverview);
