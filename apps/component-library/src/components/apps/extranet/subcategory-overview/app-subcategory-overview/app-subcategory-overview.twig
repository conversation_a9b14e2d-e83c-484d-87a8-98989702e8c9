{{ attach_library('finstral_global/apps-app-subcategory-overview') }}

{% set data = {
	refinementLists: refinement_lists,
	subcategory: {
		label: subcategory.label[0]['#context'].value,
		value: subcategory.value,
	},
} %}

{% set algolia_data = {
	indexName: index_name,
	langCode: langcode,
} %}

{% set sorting_labels = {
	dateAsc: "search_result.sort_by.date_asc"|tc,
	dateDesc: "search_result.sort_by.date_desc"|tc,
	nameAsc: "search_result.sort_by.name_asc"|tc,
	nameDesc: "search_result.sort_by.name_desc"|tc,
} %}

<section class="u-container">
	<h1 class="u-typo-HeadlineM">
		{{ subcategory.label }}
	</h1>

	<app-subcategory-overview>
		<search class="ExtranetSearch-search" data-placeholder="{{ "search.placeholder"|tc }}"></search>

		{% include "@apps/extranet/components/search/refinements/refinements.twig" with {
			label: "search.refinements.title"|tc,
			name: "search-refinements",
		} only %}

		{% include "@apps/extranet/components/search/refinement-tags/refinement-tags.twig" %}

		{% include "@apps/extranet/components/search/loader/loader.twig" %}

		<section class="ExtranetSearch-resultsContainer" hidden>
			{% include "@apps/extranet/components/search/refinement-list/refinement-list.twig" %}
			{% include "@apps/extranet/components/search/result-bar/result-bar.twig" %}
			{% include "@apps/extranet/components/search/extranet-results/extranet-results.twig" %}
		</section>
	</app-subcategory-overview>
</section>

{% include "@apps/extranet/components/session-end-alert/session-end-alert.twig" %}

<script id="subcategory-overview-data" type="application/json">{{ data|json_encode()|raw }}</script>
<script id="algolia-data" type="application/json">{{ algolia_data|json_encode()|raw }}</script>
<script id="sort-by-labels" type="application/json">{{ sorting_labels|json_encode()|raw }}</script>
