$schema: http://json-schema.org/draft-07/schema
$id: https://finstral.com/apps/extranet/subcategory-overview

type: object

required:
  - breadcrumb
  - index_name
  - langcode
  - refinement_lists
  - subcategory

additionalProperties: false

properties:
  breadcrumb:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - text
      properties:
        text:
          type: string
        url:
          type: string
          format: uri-reference

  index_name:
    type: string

  langcode:
    type: string

  refinement_lists:
    type: array
    items:
      type: object
      additionalProperties: false
      required:
        - title
        - facet_key
      properties:
        title:
          type: string
        facet_key:
          type: string

  subcategory:
    type: object
    additionalProperties: false
    required:
      - label
      - value
    properties:
      label:
        type: string
      value:
        type: number
