# Accordion

This _accordion_ component works without JavaScript by using multiple `detail` and `summary` elements. It therefore does not behave like a normal accordion, but instead multiple entries could be open at the same time.
If JavaScript is enabled, the behavior will be enhanced and the component behaves like a normal accordion.

An accordion item can be pre-opened by adding its ID as the URL hash.
