/** @define AccordionItem; */

.AccordionItem-title {
	align-items: center;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	padding-block: var(--AccordionItem-title-padding-block, var(--size-32));
	text-wrap: balance;
}

/* only relevant to hide details triangle in safari */
.AccordionItem-title::-webkit-details-marker {
	display: none;
}

.AccordionItem-titleIcon {
	flex-shrink: 0;
}

.AccordionItem[open] .AccordionItem-titleIcon {
	rotate: 180deg;
}

.AccordionItem-content {
	margin-block: var(--AccordionItem-content-padding-block);
}

@media (48em > width) {
	.AccordionItem {
		--AccordionItem-title-padding-block: var(--size-24);
		--AccordionItem-content-padding-block: var(--size-12) var(--size-48);
	}
}

@media (width >= 48em) {
	.AccordionItem {
		--AccordionItem-content-padding-block: 0 var(--size-64);
	}
}
