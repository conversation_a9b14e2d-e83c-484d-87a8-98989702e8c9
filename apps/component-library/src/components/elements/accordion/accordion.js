export default class Accordion {
	/**
	 * @param {HTMLElement} element
	 */
	constructor(element) {
		this.element = element;
		this.elements = {
			details:
				/** @type {HTMLDetailsElement[]} */
				([...this.element.querySelectorAll(".Accordion-item details")]),
		};

		this.elements.details.forEach((detailsElement) => {
			detailsElement.addEventListener("toggle", () => {
				this.#onToggle(detailsElement);
			});
		});

		this.#preOpen();
	}

	/**
	 * Pre-open accordion item based on URL hash
	 */
	#preOpen() {
		const { hash } = window.location;

		if (!hash) {
			return;
		}

		/** @type {HTMLDetailsElement|null} */
		const target = this.element.querySelector(hash);

		if (!target) {
			return;
		}

		target.open = true;
	}

	/**
	 * @param {HTMLDetailsElement} element
	 */
	#onToggle(element) {
		if (element.open) {
			this.elements.details
				.filter((detail) => detail !== element)
				.forEach((detail) => {
					detail.open = false;
				});

			window.history.replaceState(null, "", `#${element.id}`);
		}

		if (!this.#hasOpenedDetails) {
			window.history.replaceState(null, "", " ");
		}
	}

	/**
	 * @returns {boolean}
	 */
	get #hasOpenedDetails() {
		return this.elements.details.some((detail) => detail.open);
	}
}
