import { beforeEach, describe, it, expect } from "vitest";
import { renderElement } from "../../../../tests/utils.js";
import Accordion from "./accordion.js";

describe("Accordion", () => {
	let accordion;
	let container;

	beforeEach(async () => {
		container = await renderElement("elements/accordion");

		document.body.appendChild(container);

		accordion = new Accordion(container);
	});

	it("simulates the opening of an item", () => {
		const firstElement = accordion.elements.details[0];
		const secondElement = accordion.elements.details[1];

		expect(firstElement.open).toBe(false);
		expect(secondElement.open).toBe(false);

		firstElement.open = true;

		expect(firstElement.open).toBe(true);
		expect(secondElement.open).toBe(false);
	});

	// It's currently not possible to test this. Probably because of a bug in
	// jsdom where the toggle event for <details> doesn't work reliable.
	it.todo("closes open items when opening another one");
});
