/* @define AlertBox */

.AlertBox,
.AlertBox-section {
	border: var(--border-subtle-transparent);
}

.AlertBox,
.AlertBox-sectionHeader,
.AlertBox-message,
.AlertBox-title {
	align-items: center;
	display: flex;
	gap: var(--size-8);
}

.AlertBox,
.AlertBox-sectionHeader {
	justify-content: space-between;
	padding: var(--size-12);
}

.AlertBox-title {
	font-size: var(--typo-TextL-font-size);
}

.AlertBox-message {
	font-size: var(--typo-TextM-font-size);
}

.AlertBox-icon {
	flex: 0 0 auto;
}

.AlertBox-section {
	.AlertBox-message {
		padding: var(--size-12);
	}
}

/* variants */
.AlertBox-tone--info {
	background-color: var(--shared-color-surface-activity-light-default);
}

.AlertBox-tone--success {
	background-color: var(--shared-color-surface-success-light-default);
}

.AlertBox-tone--warning {
	background-color: var(--shared-color-surface-warning-light-default);
}

.AlertBox-tone--critical {
	background-color: var(--shared-color-surface-critical-light-default);
}
