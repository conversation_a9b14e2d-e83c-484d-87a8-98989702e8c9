{{ attach_library('finstral_global/element-alert-box') }}

{% set id = "alertbox-" ~ random() %}
{% set has_title = title is defined and title is not empty %}
{% set current_tone = tone ?? 'info' %}

{% set icons = {
	"critical": "error",
	"info": "info",
	"success": "check_circle",
	"warning": "warning",
} %}

{% set dismissible_button %}
<button class="AlertBox-close" type="button">
	<span class="visually-hidden">{{ "global.close_button"|tc }}</span>
	{% include "@elements/icon/icon.twig" with {
			name: "close",
		} only %}
</button>
{% endset %}

{% set icon %}
{% include "@elements/icon/icon.twig" with {
		classes: ["AlertBox-icon"],
		name: icons[current_tone],
	} only %}
{% endset %}

{% if has_title %}
	<section aria-labelledby="{{ id }}" class="AlertBox-section AlertBox-style--{{ style ?? 'primary' }}">
		<header class="AlertBox-sectionHeader Al<PERSON>Box-tone--{{ current_tone }}">
			<h2 id="{{ id }}" class="AlertBox-title">
				{{ icon }}
				{{ title }}
			</h2>

			{% if dismissible %}
				{{ dismissible_button }}
			{% endif %}
		</header>
		<span class="AlertBox-message">{{ message }}</span>
	</section>
{% else %}
	<div class="AlertBox AlertBox-style--{{ style ?? 'primary' }} AlertBox-tone--{{ current_tone }}">
		<span class="AlertBox-message">
			{{ icon }}
			{{ message }}
		</span>

		{% if dismissible %}
			{{ dismissible_button }}
		{% endif %}
	</div>
{% endif %}
