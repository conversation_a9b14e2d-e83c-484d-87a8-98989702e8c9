$schema: http://json-schema.org/draft-07/schema
$id: elements/alert-box

type: object

required:
  - message

additionalProperties: false

properties:
  action_icon:
    type: object
    additionalProperties: false
    required:
      - name
    properties:
      name:
        type: string
      position:
        type: string
        enum:
          - start
          - end
        default: end
  has_action:
    type: boolean
    default: false

  dismissible:
    type: boolean
    default: false

  title:
    type: string

  message:
    type: string

  style:
    type: string
    enum:
      - primary
      - secondary
    default: primary

  tone:
    type: string
    enum:
      - critical
      - info
      - success
      - warning
    default: info
