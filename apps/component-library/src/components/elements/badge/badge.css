/** @define Badge; */

/* neutral */
.Badge--color1A,
.Badge--color1B,
.Badge--color1C,
.Badge--color7A,
.Badge--color7B,
.Badge--color7C {
	--Badge-background-color: var(--shared-color-surface-subtle);
	--Badge-color: var(--shared-color-text-primary);
}

/* success */
.Badge--color6A,
.Badge--color6B,
.Badge--color6C {
	--Badge-background-color: var(--shared-color-surface-success-light-default);
	--Badge-color: var(--shared-color-text-success-dark-default);
}

/* warning */
.Badge--color5A,
.Badge--color5B,
.Badge--color5C {
	--Badge-background-color: var(--shared-color-surface-warning-light-default);
	--Badge-color: var(--shared-color-text-warning-dark);
}

/* critical */
.Badge--color4A,
.Badge--color4B,
.Badge--color4C {
	--Badge-background-color: var(--shared-color-surface-critical-light-default);
	--Badge-color: var(--shared-color-text-critical-dark-default);
}

/* activity */
.Badge--color2A,
.Badge--color2B,
.Badge--color2C,
.Badge--color3A,
.Badge--color3B,
.Badge--color3C {
	--Badge-background-color: var(--shared-color-surface-activity-light-default);
	--Badge-color: var(--shared-color-text-activity-dark);
}

.Badge--xs {
	--Badge-min-block-size: var(--size-20);
}

.Badge--md {
	--Badge-padding-inline: var(--size-8);
	--Badge-min-block-size: var(--size-32);
}

.Badge {
	align-items: center;
	background-color: var(
		--Badge-background-color,
		var(--shared-color-surface-subtle)
	);
	color: var(--Badge-color, var(--shared-color-text-primary));
	display: flex;
	min-block-size: var(--Badge-min-block-size, var(--size-24));
	padding-block: var(--size-2);
	padding-inline: var(--Badge-padding-inline, var(--size-4));
}
