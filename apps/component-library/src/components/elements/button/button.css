/** @define Button; weak; */

.Button {
	align-items: center;
	appearance: none;
	background-color: var(--Button-background-color);
	border: solid var(--shared-border-width-sm);
	border-color: var(--Button-border-color);
	color: var(--Button-color);
	display: inline-flex;
	gap: var(--size-4);
	justify-content: center;
	padding: var(--Button-padding-block) var(--Button-padding-inline);
	text-decoration: none;
	transition:
		background-color var(--default-transition-duration),
		box-shadow var(--default-transition-duration),
		color var(--default-transition-duration),
		border-color var(--default-transition-duration);
}

.Button:active {
	outline: var(--focus-outline);
	outline-offset: var(--focus-outline-offset);
}

/* variant: primary, secondary, tertiary */

.Button--primary {
	--Button-background-color: var(--shared-color-surface-brand);
	--Button-border-color: transparent;
	--Button-color: var(--shared-color-text-invert);
}

.Button--primary:hover,
.Button--primary:focus-visible {
	--Button-background-color: var(--shared-color-surface-brand-hover);
}

.Button--primary:active {
	--Button-background-color: var(--shared-color-surface-brand-pressed);
}

.Button--primary:disabled {
	--Button-background-color: var(--shared-color-surface-disabled);
}

.Button--primary:disabled,
.Button--secondary:disabled,
.Button--tertiary:disabled {
	--Button-color: var(--shared-color-text-disabled);
}

.Button--secondary {
	--Button-background-color: transparent;
	--Button-border-color: var(--shared-color-border-full);
	--Button-color: var(--shared-color-text-primary);
}

.Button--secondary:not(:disabled):hover {
	--Button-background-color: var(--shared-color-surface-invert);
	--Button-color: var(--shared-color-text-invert);
}

.Button--secondary:disabled {
	--Button-border-color: var(--shared-color-border-disabled);
}

.Button--secondary:active {
	--Button-background-color: var(--shared-color-surface-invert-pressed);
	--Button-color: var(--shared-color-text-invert);
}

.Button--tertiary {
	--Button-background-color: transparent;
	--Button-border-color: transparent;
	--Button-color: var(--shared-color-text-primary);
}

.Button--tertiary:not(:disabled):hover {
	--Button-background-color: var(--shared-color-surface-transparent-hover);
}

.Button--tertiary:active {
	--Button-background-color: var(--shared-color-surface-transparent-pressed);
}

/* tone: neutral */

.Button--primary.Button--neutral {
	--Button-background-color: var(--shared-color-surface-invert);
}

.Button--primary.Button--neutral:hover,
.Button--primary.Button--neutral:focus-visible {
	--Button-background-color: var(--shared-color-surface-invert-hover);
}

.Button--primary.Button--neutral:active {
	--Button-background-color: var(--shared-color-surface-invert-pressed);
}

/* sizes: sm, md, lg */

.Button--sm {
	--Button-padding-block: var(--size-4);
	--Button-padding-inline: var(--size-12);
	font: var(--typo-static-TextM-bold);
}

.Button--md {
	--Button-padding-block: var(--size-8);
	--Button-padding-inline: var(--size-24);
	font: var(--typo-static-TextM-bold);
}

.Button--lg {
	--Button-padding-block: var(--size-12);
	--Button-padding-inline: var(--size-24);
	font: var(--typo-static-TextL-bold);
}

.Button--sm.Button--iconOnly {
	--Button-padding-block: var(--size-2);
	--Button-padding-inline: var(--size-2);
}

.Button--md.Button--iconOnly {
	--Button-padding-block: var(--size-4);
	--Button-padding-inline: var(--size-4);
}

.Button--lg.Button--iconOnly {
	--Button-padding-block: var(--size-8);
	--Button-padding-inline: var(--size-8);
}

/* icon and badge styles */

.Button--iconEnd {
	flex-direction: row-reverse;
}

.Button-icon {
	flex-shrink: 0;
}

.Button--primary .Badge {
	background-color: var(--shared-color-surface-invert-subtle);
	color: var(--shared-color-text-invert);
	margin-inline-start: var(--size-4);
}

/* loading state */

.Button-loader {
	display: none;
}

.Button--loading .Button-loader {
	animation: rotation 1s linear infinite;
	block-size: var(--size-16);
	border: 2px solid var(--shared-color-text-disabled);
	border-bottom-color: transparent;
	border-radius: 50%;
	display: inline-block;
	inline-size: var(--size-16);
	margin-inline-end: var(--size-4);
}

@keyframes rotation {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* responsive button: medium on most viewports, large on large viewports */

/* mobile and tablet */
@media (width < 80rem) {
	.Button--responsive {
		--Button-padding-block: var(--size-8);
		--Button-padding-inline: var(--size-24);
		font: var(--typo-static-TextM-bold);
	}

	.Button--responsive.Button--iconOnly {
		--Button-padding-block: var(--size-4);
		--Button-padding-inline: var(--size-4);
	}
}

/* desktop */
@media (width >= 80rem) {
	.Button--responsive {
		--Button-padding-block: var(--size-12);
		--Button-padding-inline: var(--size-24);
		font: var(--typo-static-TextL-bold);
	}

	.Button--responsive.Button--iconOnly {
		--Button-padding-block: var(--size-8);
		--Button-padding-inline: var(--size-8);
	}
}
