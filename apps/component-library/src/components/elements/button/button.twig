{% set tag = url ? "a" : "button" %}

<{{ tag }}
	{{ attributes }}
	class="Button  {{ classes|join(" ") }}
	{{ button_classes }}
	Button--{{ size|default("md") }}
	Button--{{ variant|default("primary") }}
	{% if tone %}Button--{{ tone }}{% endif %}
	{% if is_responsive %}Button--responsive{% endif %}
	{% if icon.position == "icon_only" %}Button--iconOnly{% endif %}
	{% if icon.position == "end" %}Button--iconEnd{% endif %}

	"
	{% if tag == "button" %}
		type="{{ type|default("button") }}"
		{% if disabled %} disabled{% endif %}
		{% if form %} form="{{ form }}"{% endif %}
	{% else %}
		href="{{ url }}"
		{% if target %}target="{{ target }}"{% endif %}
	{% endif %}
	{% if id %}id="{{ id }}"{% endif %}
	{% if popover_target %}popover_target="{{ popover_target }}"{% endif %}
	{% if hidden %}hidden{% endif %}
	{% if download %}download{% endif %}
	{% if iframe_id %}data-dialog="{{ iframe_id|clean_id }}"{% endif %}
>
	<span class="Button-loader"></span>
	{% if icon %}
		{% include "@elements/icon/icon.twig" with icon|merge({
			name: icon.name,
			classes: ["Button-icon"],
			size: "medium",
		}) only %}
	{% endif %}
	<span class="Button-label {% if icon.position == "icon_only" %}u-hiddenVisually{% endif %}">{{ label }}</span>
	{% if badge %}
		{% include "@elements/badge/badge.twig" with {
			label: badge.label,
			size: "xs",
		} only %}
	{% endif %}
</{{ tag }}>
