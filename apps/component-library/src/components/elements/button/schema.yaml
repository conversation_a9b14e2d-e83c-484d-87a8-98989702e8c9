$schema: http://json-schema.org/draft-07/schema#
$id: /elements/button
additionalProperties: false
required:
  - label

properties:
  label:
    type: string
  size:
    type: string
    enum:
      - sm
      - md
      - lg
    default: md
  is_responsive:
    type: boolean
    default: false
  variant:
    type: string
    enum:
      - primary
      - secondary
      - tertiary
    default: primary
  icon:
    type: object
    additionalProperties: false
    required:
      - name
    properties:
      name:
        type: string
      position:
        type: string
        enum:
          - start
          - end
          - icon_only
        default: start
  tone:
    type: string
    enum:
      - neutral
  badge:
    type: object
    additionalProperties: false
    required:
      - label
    properties:
      label:
        type: string
  popover_target:
    type: string
  attributes:
    type: object
  classes:
    type: array
  target:
    type: string
    enum:
      - _self
      - _blank
      - _parent
      - _top
  type:
    type: string
    enum:
      - button
      - reset
      - submit
  url:
    type: string
    format: uri-reference
  disabled:
    type: boolean
  form:
    type: string
  iframe_id:
    type: string
  id:
    type: string
  download:
    type: boolean
  hidden:
    type: boolean
