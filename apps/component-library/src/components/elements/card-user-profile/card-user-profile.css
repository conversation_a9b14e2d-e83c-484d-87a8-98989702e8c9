/* stylelint-disable plugin/selector-bem-pattern */

/** @define CardUserProfile; */

.CardUserProfile {
	background-color: inherit;
}

.CardUserProfile-header {
	align-items: center;
	display: flex;
	gap: var(--size-12);
}

.CardUserProfile-header--stacked {
	align-items: initial;
	flex-flow: column;
}

.CardUserProfile-avatar,
.CardUserProfile-avatar img {
	block-size: var(--CardUserProfile-avatar-size);
	border-radius: 50%;
	display: inline-block;
	inline-size: var(--CardUserProfile-avatar-size);
	object-fit: cover;
}

.CardUserProfile-organisation {
	color: var(--shared-color-text-secondary);
}

.CardUserProfile-contactDetails {
	display: flex;
	flex-flow: column;
	gap: var(--size-12);
	margin-block-start: var(--size-24);
}

.CardUserProfile-contactDetailsItem {
	align-items: center;
	display: flex;
	gap: var(--size-12);
}

.CardUserProfile-name--bold {
	font-weight: 700;
}

@media (width < 48rem) {
	.CardUserProfile-avatar,
	.CardUserProfile-avatar img {
		--CardUserProfile-avatar-size: var(--size-64);
	}
}

@media (width >= 48rem) {
	.CardUserProfile-avatar,
	.CardUserProfile-avatar img {
		--CardUserProfile-avatar-size: var(--size-80);
	}
}
