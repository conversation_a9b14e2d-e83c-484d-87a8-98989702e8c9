<div class="CardUserProfile {{ classes|join(' ') }}">
	<div class="CardUserProfile-header {%- if card_style == 'full' %} CardUserProfile-header--stacked {%- endif -%}">
			{%- if avatar -%}
				<div class="CardUserProfile-avatar">
					{{ avatar }}
				</div>
			{%- endif -%}
			<div>
				<div
					class="js-CardUserProfile-name {% if card_style == 'full' %}u-typo-HeadlineS{% else %}CardUserProfile-name--bold{% endif %}"
				>
					{{ first_name }} {{ last_name }}
				</div>
				{# This div should not get rendered conditionally, even though `organisation` is optional,
					as the content can get set via JS in the user management dialog. #}
				<div
					class="CardUserProfile-organisation {% if card_style == 'full' %}u-typo-TextL{% else %}u-typo-TextM{% endif %}"
				>
					{{ organisation }}
				</div>
			</div>
	</div>


	{% if email or phone %}
		<ul class="CardUserProfile-contactDetails u-typo-TextM">
			{% if email %}
				<li class="CardUserProfile-contactDetailsItem">
					{% include "@elements/icon/icon.twig" with {
						name: "email",
					} only %}
					{{ email }}
				</li>
			{% endif %}

			{% if phone %}
				<li class="CardUserProfile-contactDetailsItem">
					{% include "@elements/icon/icon.twig" with {
						name: "phone",
					} only %}
					{{ phone }}
				</li>
			{% endif %}
		</ul>
	{% endif %}
</div>
