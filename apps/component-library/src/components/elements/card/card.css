/** @define Card; */

.Card {
	background: var(--shared-color-surface-primary);
	block-size: 100%;
	color: var(--shared-color-text-secondary);
	display: flex;
	flex-direction: column;
	min-block-size: 18.5rem;
	position: relative;
	transform: var(--Card-transform, none);
	transition: transform var(--default-transition-duration);
}

.Card:has(.Card-headingLink:hover, .Card-headingLink:focus-visible) {
	--Card-headingLink-color: var(--shared-color-text-primary);
	--Card-locationArrow-color: var(--shared-color-text-brand);
}

@media (prefers-reduced-motion: no-preference) {
	.Card:has(.Card-headingLink:hover, .Card-headingLink:focus-visible) {
		--Card-transform: scale(
			calc(368 / 360)
		); /* magic numbers taken from the design */
	}
}

.Card-image {
	aspect-ratio: 16 / 9;
	margin-block-end: calc(-1 * var(--size-8));
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.Card-image img {
	inline-size: 100%;
}

.Card-content {
	display: grid;
	grid-template-rows: auto 2fr auto;
	min-block-size: inherit;
	padding: var(--size-32) var(--size-24);
}

.Card-content:has(.Card-partnerImage) {
	grid-template-rows: auto 2fr 2fr auto;
	min-block-size: 100%;
}

.Card-additionalInfo {
	margin-block-end: var(--size-8);
}

.Card-additionalInfoItem {
	display: inline;
}

.Card-additionalInfoItem:not(:last-child)::after {
	content: "|";
	margin-inline-end: var(--size-4);
}

.Card-heading {
	hyphens: auto;
	margin-block-end: var(--Card-heading-margin-block-end, var(--size-40));
	text-wrap: balance;
}

.Card-headingLink {
	-webkit-box-orient: vertical;
	color: var(--Card-headingLink-color, var(--shared-color-text-primary));
	display: -webkit-box; /* stylelint-disable-line value-no-vendor-prefix */
	-webkit-line-clamp: 3; /* number of lines to show */
	line-clamp: 3;
	overflow: hidden;
	text-decoration: none;
	transition: color var(--default-transition-duration);
}

.Card-headingLink:focus-visible {
	outline: none;
}

.Card-headingLink::after {
	content: "";
	cursor: pointer;
	inset: 0;
	position: absolute;
	transition:
		outline-color var(--default-transition-duration),
		outline-offset var(--default-transition-duration),
		outline-width var(--default-transition-duration);
}

.Card-headingLink:focus-visible::after {
	outline: var(--focus-outline);
	outline-offset: var(--focus-outline-offset);
}

.Card-location {
	align-items: flex-end;
	display: flex;
	gap: var(--size-4);
	margin-block-start: auto;
}

.Card-locationPin {
	flex-shrink: 0;
}

.Card-locationName {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.Card-locationArrow {
	color: var(--Card-locationArrow-color, var(--shared-color-text-primary));
	flex-shrink: 0;
	margin-inline-start: auto;
	transition: color var(--default-transition-duration);
}

.Card-partnerImage {
	align-self: end;
	block-size: var(--Card-partnerImage-block-size, var(--size-48));
	margin-block-end: var(--size-8);
}

@media (48em > width) {
	.Card {
		--Card-heading-margin-block-end: var(--size-24);
		--Card-partnerImage-block-size: var(--size-32);
	}
}
