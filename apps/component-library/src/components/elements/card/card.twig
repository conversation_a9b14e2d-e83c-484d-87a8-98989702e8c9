<article class="Card u-typo-TextM">
	{% if image %}
		<div class="Card-image">{{ image }}</div>
	{% endif %}
	<div class="Card-content">
		{% if additional_info %}
			<ul class="Card-additionalInfo">
				{% for item in additional_info %}
          {% if item %}
					  <li class="Card-additionalInfoItem">{{ item }}</li>
          {% endif %}
				{% endfor %}
			</ul>
		{% endif %}
		<h3 class="u-typo-HeadlineM Card-heading">
			<a href="{{ url }}" class="Card-headingLink">{{ heading }}</a>
		</h3>
		{% if logo %}
			<div class="Card-partnerImage">{{ logo }}</div>
		{% endif %}
		<span class="Card-location">
			{% include "@elements/icon/icon.twig" with {
				name: "pin",
				classes: ["Card-locationPin"],
			} only %}
			<span class="Card-locationName">{{ location }}</span>
			{% include "@elements/icon/icon.twig" with {
				name: "arrow_forward",
				classes: ["Card-locationArrow"],
			} only %}
		</span>
	</div>
</article>
