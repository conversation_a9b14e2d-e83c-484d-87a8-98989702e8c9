/** @define ContactBlock; */

.Contact<PERSON>lock {
	border-block: var(--border-distinct);
	display: flex;
	gap: var(--ContactBlock-gap, var(--size-24));
	min-inline-size: 21.875rem; /* magic number taken from design */
	padding-block: var(--ContactBlock-padding-block, var(--size-40));
}

.Contact<PERSON>lock-kicker {
	color: var(--shared-color-text-secondary);
	margin-block-end: var(--size-8);
}

.ContactBlock-position {
	margin-block-start: var(--size-16);
}

.ContactBlock-image {
	flex-shrink: 0;
	inline-size: var(--ContactBlock-image-size, var(--size-128));
}

.ContactBlock-data {
	display: flex;
	flex-direction: column;
}

.ContactBlock-partnerLogo {
	block-size: var(--ContactBlock-partnerLogo-block-size, var(--size-48));
	inline-size: auto;
	margin-block-start: var(--size-16);
}

.ContactBlock-address {
	font-style: normal;
	margin-block-start: var(--size-24);
}

.ContactBlock-partnerDetails {
	margin-block-start: var(--size-24);
}

@media (48em > width) {
	.ContactBlock {
		--ContactBlock-gap: var(--size-16);
		--ContactBlock-image-size: var(--size-96);
		--ContactBlock-padding-block: var(--size-24);
		--ContactBlock-partnerLogo-block-size: var(--size-32);
	}
}
