/** @define Dialog; */

.Dialog {
	border: none;
	inline-size: 100%;
	margin: auto;
	max-block-size: var(--Dialog-max-block-size);
	max-inline-size: var(--Dialog-max-inline-size);
	overflow: hidden;
	padding: 0;
}

.Dialog[open] {
	display: flex;
	flex-direction: column;
}

.Dialog--withIframe {
	--Dialog-padding: 0;

	container-type: inline-size;
}

.Dialog::backdrop {
	background: rgb(0 0 0 / 0.5);
}

.Dialog-header {
	align-items: center;
	background: var(--shared-color-surface-sandstein-light);
	display: flex;
	flex-direction: row-reverse;
	justify-content: space-between;
	padding: var(--size-24);
	padding-block-end: var(--size-12);
}

.Dialog-close {
	background: var(--shared-color-surface-sandstein-light);
}

.Dialog-heading {
	inline-size: calc(100% - 1rem - var(--size-8));
}

.Dialog-content {
	block-size: 100%;
	overflow: auto;
	padding: var(--Dialog-padding, var(--size-32));
}

.Dialog-iframe {
	block-size: 100%;
	border: none;
	display: block;
	inline-size: 100%;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
body:has(.Dialog[open]) {
	overflow: hidden;
}

@media (48em > width) {
	.Dialog {
		--Dialog-max-block-size: 100dvh;
		--Dialog-max-inline-size: 100vw;
	}
}

@media (width >= 48em) {
	.Dialog {
		--Dialog-max-block-size: calc(100dvh - var(--size-128) * 2);
		--Dialog-max-inline-size: min(70rem, calc(100vw - var(--size-32)));
	}
}
