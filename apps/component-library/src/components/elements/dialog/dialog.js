export default class Dialog {
	/**
	 * @param {HTMLDialogElement} element
	 */
	constructor(element) {
		this.element = element;
		this.elements = {
			trigger: document.querySelectorAll(`[data-dialog="${this.element.id}"]`),
			iframe: /** @type {HTMLIFrameElement|null} */ (
				document.querySelector(".js-Dialog-iframe")
			),
		};
		this.trigger = null;

		this.elements.trigger.forEach((button) => {
			button.addEventListener("click", (event) => {
				event.preventDefault();

				this.element.showModal();
				this.trigger = button;
				this.setIframeHeight();
			});
		});

		if (this.element.open) {
			this.element.open = false;
			this.element.showModal();

			this.elements.iframe?.addEventListener("load", () => {
				this.setIframeHeight();
			});
		}

		this.element.addEventListener("close", () => {
			if (this.trigger) {
				this.trigger.focus();
			}
		});
	}

	/**
	 * Set the height of the iframe to the height of the content
	 */
	setIframeHeight() {
		if (!this.elements.iframe?.contentWindow) {
			return;
		}

		const iframeHeight =
			this.elements.iframe.contentWindow.document.body.scrollHeight;
		if (iframeHeight) {
			this.element.style.blockSize = `${iframeHeight}px`;
		}
	}
}
