import {
	afterEach,
	beforeAll,
	beforeEach,
	describe,
	it,
	expect,
	vi,
} from "vitest";
import { renderElement } from "../../../../tests/utils.js";
import Dialog from "./dialog.js";

describe("Dialog", () => {
	/** @type {HTMLButtonElement} */
	let trigger;
	/** @type {HTMLDialogElement} */
	let container;

	// https://github.com/jsdom/jsdom/issues/3294
	beforeAll(() => {
		HTMLDialogElement.prototype.showModal = vi.fn();
	});

	beforeEach(async () => {
		trigger = document.createElement("button");
		trigger.setAttribute("data-dialog", "dialog");
		document.body.appendChild(trigger);

		container = await renderElement("elements/dialog");
		document.body.appendChild(container);

		new Dialog(container);
	});

	afterEach(async () => {
		vi.clearAllMocks();
	});

	it("measures setting the initial open state of the modal", () => {
		expect(HTMLDialogElement.prototype.showModal).toHaveBeenCalled();
	});

	it("triggers a click to measure if the modal has opened", () => {
		trigger.click();
		expect(HTMLDialogElement.prototype.showModal).toHaveBeenCalledTimes(2);
	});

	it("moves focus to trigger on close", () => {
		trigger.click();
		container.dispatchEvent(new Event("close"));
		expect(document.activeElement).toBe(trigger);
	});
});
