<dialog class="Dialog js-Dialog{% if iframe_src %} Dialog--withIframe{% endif %}" id="{{ id }}"{% if open %} open{% endif %} aria-labelledby="{{ id }}-title">
	<header class="Dialog-header">
		<form method="dialog">
			<button class="Dialog-close" type="submit" aria-label="{{ "global.close_button"|tc }}">
				{% include "@elements/icon/icon.twig" with {
					name: "close",
				} only %}
			</button>
		</form>
		<h2 class="Dialog-heading{% if hidden_title %} u-hiddenVisually{% endif %}" id="{{ id }}-title">{{ title }}</h2>
	</header>
	<div class="Dialog-content js-Dialog-content">
		{% if content %}
			{{ content|raw }}
		{% endif %}
		{% if iframe_src %}
			<iframe src="{{ iframe_src }}" title="{{ title }}" class="Dialog-iframe js-Dialog-iframe" id="{{ id }}-iframe"></iframe>
		{% endif %}
	</div>
</dialog>
