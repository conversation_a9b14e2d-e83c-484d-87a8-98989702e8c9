/** @define FileUploader; */

.FileUploader-fallback {
	align-items: center;
	background-color: var(--shared-color-surface-subtle-lighter);
	border: var(--border-distinct);
	cursor: pointer;
	display: flex;
	flex-direction: column;
	justify-content: center;
	margin-block-start: var(--FileUploader-margin-block-start, var(--size-24));
	padding: var(--size-32) var(--size-24);
	position: relative;
	transition: all var(--default-transition-duration);
}

.FileUploader-fallback:hover,
.FileUploader-fallback.is-dragover {
	border-color: var(--shared-color-border-full);
}

.FileUploader-fallback.is-dragover {
	border-style: dashed;
}

.FileUploader-description {
	margin-block-start: var(
		--FileUploader-description-margin-block-start,
		var(--size-24)
	);
}

.FileUploader-sizeWarning {
	margin-block-start: var(--size-8);
}

.FileUploader-fallbackInput {
	cursor: pointer;
	inset: 0;
	opacity: 0;
	position: absolute;
}

.FileUploader-files:not([hidden]),
.FileUploader-errors:not([hidden]) {
	display: flex;
	flex-direction: column;
	gap: var(--size-16);
	margin-block-start: var(--size-16);
}

.FileUploader-file:not([hidden]) {
	align-items: center;
	display: flex;
	gap: var(--size-4);
}

.FileUploader-fileDescription {
	flex: 1;
}

.FileUploader-success,
.FileUploader-failed {
	transform: translateY(-10%);
}

.FileUploader-success {
	color: var(--shared-color-surface-success);
}

.FileUploader-error,
.FileUploader-failed {
	color: var(--shared-color-text-critical);
}

.FileUploader-loadingIndicator,
.FileUploader-icon {
	flex-shrink: 0;
}

.FileUploader-loadingIndicator[hidden],
.FileUploader-icon[hidden],
.FileUploader-loadingBar[hidden] {
	display: none;
}

.FileUploader-loadingIndicator {
	background-color: var(--shared-color-surface-subtle);
	block-size: var(--size-16);
	border-radius: 50%;
	display: block;
	inline-size: var(--size-16);
}

.FileUploader-contents {
	align-items: center;
	display: flex;
	inline-size: calc(100% - var(--size-40));
}

.FileUploader-loadingBar,
.FileUploader-name {
	flex: 1;
}

.FileUploader-name {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.FileUploader-loadingBar {
	background-image: linear-gradient(
		to right,
		var(--shared-color-surface-invert) 0% var(--FileUploader-progress, 0),
		var(--shared-color-surface-subtle) var(--FileUploader-progress, 0) 100%
	);
	block-size: var(--size-2);
	inline-size: 100%;
	padding: 0 var(--size-4);
}

.FileUploader-removeButton[disabled] .FileUploader-fileRemoveIcon {
	color: var(--shared-color-surface-subtle);
	cursor: not-allowed;
}

@media (48em > width) {
	.FileUploader {
		--FileUploader-description-margin-block-start: var(--size-32);
		--FileUploader-margin-block-start: var(--size-32);
	}
}
