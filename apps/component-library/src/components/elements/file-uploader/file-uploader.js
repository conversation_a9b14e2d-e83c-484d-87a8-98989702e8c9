import Dropzone from "dropzone";

const selectors = {
	files: ".js-FileUploader-files",
	file: ".js-FileUploader-file",
	fileDescription: ".js-FileUploader-name",
	icon: ".js-FileUploader-icon",
	loadingIndicator: ".js-FileUploader-loadingIndicator",
	loadingBar: ".js-FileUploader-loadingBar",
	errors: ".js-FileUploader-errors",
	error: ".js-FileUploader-error",
	fallback: ".js-FileUploader-fallback",
	button: ".js-FileUploader-description",
	removeButton: ".js-FileUploader-removeButton",
	uploadedFiles: ".js-FileUploader-uploaded-files",
};

const iconClasses = {
	success: "FileUploader-success",
	failed: "FileUploader-failed",
};

const icons = {
	success: "check_circle_filled",
	failed: "error_circle_filled",
};

/**
 *
 */
function hideWrapperComponentIfEmpty() {
	const files = document.querySelector(selectors.files);
	// if the only child of this component is the hidden template one, hide the whole component
	if (files.children.length === 1) {
		files.setAttribute("hidden", true);
	}
}

/**
 *
 * @param {HTMLElement} element
 */
function replaceButtonIcon(element) {
	const svg = element.querySelector(`${selectors.removeButton} use`);
	const currentUrl = svg.getAttribute("xlink:href");
	svg.setAttribute("xlink:href", `${currentUrl}_filled`);
}

/**
 *
 * @param {HTMLElement} element
 * @param {string} state
 */
function setIcon(element, state) {
	const icon = element.querySelector(selectors.icon);
	const svg = icon.querySelector("use");
	const currentUrl = svg.getAttribute("xlink:href");

	if (state === "success") {
		icon.classList.add(iconClasses.success);
		icon.classList.remove(iconClasses.failed);
		// replace everything after # with icons.success
		svg.setAttribute(
			"xlink:href",
			`${currentUrl.split("#")[0]}#${icons.success}`,
		);
	} else if (state === "failed") {
		icon.classList.add(iconClasses.failed);
		icon.classList.remove(iconClasses.success);
		// replace everything after # with icons.failed
		svg.setAttribute(
			"xlink:href",
			`${currentUrl.split("#")[0]}#${icons.failed}`,
		);
	}
}
export default class FileUploader {
	/**
	 * @param {HTMLElement} element
	 */
	constructor(element) {
		this.element = element;
		this.dzElement = this.init();
	}

	/**
	 * initializes an instance of Dropzone with the provided attributes and events
	 * @returns {HTMLElement} returns a Dropzone element that's been initialized with event handlers and functionality
	 */
	init() {
		const dzElement = this.element.querySelector(selectors.fallback);
		const dz = new Dropzone(dzElement, {
			url: this.element.dataset.uploadUrl,
			maxFilesize: this.element.dataset.maxFileSize || 5,
			maxFiles: this.element.dataset.maxFiles || 3,
			previewTemplate: "",
			addedfile: this.onFileAdded.bind(this),
			error: this.onFileError.bind(this),
			uploadprogress: this.onProgress.bind(this),
			clickable: [selectors.fallback, selectors.button],
			dictFileTooBig: this.element.dataset.errorBigFile,
			dictMaxFilesExceeded: this.element.dataset.errorMaxFilesExceeded,
			acceptedFiles: "image/*,application/pdf",
		});

		dz.on("success", this.onSuccess.bind(this));
		dz.on("removedfile", this.onRemovedFile.bind(this));

		dzElement.addEventListener("dragover", this.onDragOver.bind(this));
		dzElement.addEventListener("dragleave", this.onDragLeave.bind(this));
		dzElement.addEventListener("drop", this.onDragLeave.bind(this));

		return dzElement;
	}

	/**
	 * Adds .is-dragover class to the dropzone element
	 */
	onDragOver() {
		this.dzElement.classList.add("is-dragover");
	}

	/**
	 * Removes .is-dragover class from the dropzone element
	 */
	onDragLeave() {
		this.dzElement.classList.remove("is-dragover");
	}

	/**
	 *
	 * @param {File} file
	 */
	onFileAdded(file) {
		const element = this.element.querySelector(selectors.file).cloneNode(true);
		element.querySelector(selectors.fileDescription).textContent = file.name;
		element.setAttribute("data-id", file.upload.uuid);
		element.querySelector("button").addEventListener("click", (e) => {
			e.preventDefault();
			this.element.querySelector(selectors.files).removeChild(element);
			this.dzElement.dropzone.removeFile(file);
			hideWrapperComponentIfEmpty();
		});
		element.querySelector(selectors.icon).setAttribute("hidden", true);
		element.removeAttribute("hidden");
		document.querySelector(selectors.files).appendChild(element);
		document.querySelector(selectors.files).removeAttribute("hidden");
		element
			.querySelector(selectors.loadingBar)
			.style.setProperty("--FileUploader-progress", "0%");
	}

	/**
	 *
	 * @param {File} file
	 * @param {object} response
	 */
	onSuccess(file, response) {
		const fileElement = this.element.querySelector(
			`[data-id="${file.upload.uuid}"]`,
		);
		setIcon(fileElement, "success");
		fileElement.querySelector(selectors.icon).removeAttribute("hidden");
		fileElement
			.querySelector(selectors.loadingIndicator)
			.setAttribute("hidden", true);
		fileElement
			.querySelector(selectors.loadingBar)
			.setAttribute("hidden", true);
		fileElement
			.querySelector(selectors.removeButton)
			.removeAttribute("disabled");
		replaceButtonIcon(fileElement);
		this.element.querySelector(selectors.errors).setAttribute("hidden", true);

		// The file is transliterated on upload and a .txt suffix s added. The
		// element has to reflect the real filename.
		file.processedName = response.result;

		// Get the existing files from the hidden element and add the new file.
		const hiddenInput = this.element.querySelector(selectors.uploadedFiles);
		const currentValue = hiddenInput.getAttribute("value") || "";
		hiddenInput.setAttribute("value", `${currentValue + response.result};`);
	}

	/**
	 *
	 * @param {File} file
	 */
	onRemovedFile(file) {
		const hiddenInput = this.element.querySelector(selectors.uploadedFiles);
		const currentValue = hiddenInput.getAttribute("value");

		// Find the file in the array of file from the element and remove it.
		if (currentValue.length) {
			const fileNames = currentValue.split(";");
			for (const i in fileNames) {
				if (fileNames[i] === file.processedName) {
					fileNames.splice(i, 1);
					break;
				}
			}

			// Assign back the rest of the files.
			hiddenInput.setAttribute("value", fileNames.join(";"));
		}
	}

	/**
	 *
	 * @param {File} file
	 * @param {string} message
	 */
	onFileError(file, message) {
		if (
			typeof message === "object" &&
			message !== null &&
			"message" in message
		) {
			message = message.message;
		}

		const files = this.element.querySelector(selectors.files);
		const fileElement = files.querySelector(`[data-id="${file.upload.uuid}"]`);
		setIcon(fileElement, "failed");
		fileElement.querySelector(selectors.icon).removeAttribute("hidden");
		fileElement
			.querySelector(selectors.loadingIndicator)
			.setAttribute("hidden", true);
		fileElement
			.querySelector(selectors.loadingBar)
			.setAttribute("hidden", true);
		fileElement
			.querySelector(selectors.removeButton)
			.removeAttribute("disabled");
		replaceButtonIcon(fileElement);

		if (message) {
			const element = document.querySelector(selectors.error);
			element.textContent = message;
			element.removeAttribute("hidden");
			const errorsElement = document.querySelector(selectors.errors);
			errorsElement.appendChild(element);
			errorsElement.removeAttribute("hidden");
		}
	}

	/**
	 *
	 * @param {File} file
	 * @param {number} progress
	 */
	onProgress(file, progress) {
		const element = this.element.querySelector(
			`[data-id="${file.upload.uuid}"]`,
		);
		element
			.querySelector(selectors.loadingBar)
			.style.setProperty("--FileUploader-progress", `${progress}%`);
	}
}

document
	.querySelectorAll(".js-FileUploader")
	.forEach((element) => new FileUploader(element));
