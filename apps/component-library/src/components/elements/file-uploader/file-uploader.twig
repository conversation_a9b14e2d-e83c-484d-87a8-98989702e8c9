{{ attach_library('finstral_global/element-file-uploader') }}

<div class="FileUploader js-FileUploader"
		 data-upload-url="{{ upload_url }}"
		 data-max-file-size="{{ max_file_size_in_mb }}"
		 data-max-files="{{ max_files }}"
		 data-error-required="{{ "fileUploader.isRequiredError"|tc }}"
		 data-error-big-file="{{ "fileUploader.errorBigFile"|tc }}"
		 data-error-max-files-exceeded="{{ "fileUploader.errorMaxFilesExceeded"|tc }}"
		 {% if required %}data-required{% endif %}
>
	<p class="FileUploader-description u-typo-TextL">{{ field_description|default("fileUploader.description"|tc) }}</p>
	<div class="FileUploader-fallback js-FileUploader-fallback">
		{% include "@elements/icon/icon.twig" with {
			classes: ["FileUploader-icon", "js-FileUploader-icon"],
			name: "upload",
			size: "large"
		} only %}
		<button type="button" class="FileUploader-description js-FileUploader-description u-typo-TextL">{{ "fileUploader.button.text"|tc }}</button>
		<p class="FileUploader-sizeWarning u-typo-TextS">{{ "fileUploader.sizeWarning"|tc({
				'@file_size': max_file_size_in_mb,
				'@files_amount': max_files,
			}) }}</p>
		{# .fallback is used by dropzone to hide this element when it cannot initialize #}
		<div class="fallback">
			<input name="file" type="file" multiple class="FileUploader-fallbackInput" id="{{ id }}">
		</div>
	</div>
	<div class="FileUploader-files js-FileUploader-files" hidden>
		<p class="FileUploader-file js-FileUploader-file" hidden>
			<span class="FileUploader-loadingIndicator js-FileUploader-loadingIndicator" aria-hidden="true"></span>
			{% include "@elements/icon/icon.twig" with {
				classes: ["FileUploader-icon", "js-FileUploader-icon"],
				name: "check_circle_filled",
				size: "small"
			} only %}
			<span class="FileUploader-contents">
				<span class="FileUploader-name js-FileUploader-name u-typo-TextS"></span>
				<span class="FileUploader-loadingBar js-FileUploader-loadingBar" aria-hidden="true"></span>
			</span>
			<button type="button" class="FileUploader-removeButton js-FileUploader-removeButton" disabled>
				{% include "@elements/icon/icon.twig" with {
					classes: ["FileUploader-fileRemoveIcon"],
					name: "delete",
					size: "small"
				} only %}
			</button>
		</p>
	</div>
	<div class="FileUploader-errors js-FileUploader-errors" hidden tabindex="-1">
		<p class="FileUploader-error js-FileUploader-error u-typo-TextS"></p>
	</div>
	<input class="FileUploader-uploaded-files js-FileUploader-uploaded-files Input" name="{{ upload_input_name }}" value="" type="hidden">
</div>
