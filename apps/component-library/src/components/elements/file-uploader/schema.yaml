$schema: http://json-schema.org/draft-07/schema
$id: /elements/file-uploader
additionalProperties: false
type: object
required:
  - id
  - max_file_size_in_mb
  - max_files
  - upload_input_name
  - upload_url
properties:
  field_description:
    type: string
    description: Short user visible description or guidelines
  upload_url:
    type: string
    format: uri-reference
  upload_input_name:
    type: string
    description: The name attribute for the uploaded files field
  max_file_size_in_mb:
    type: integer
    minimum: 0
    maximum: 32
    default: 10
    description: Maximum file size in megabytes
  max_files:
    type: integer
    description: Maximum number of files that can be uploaded
  required:
    type: boolean
    description: Whether the file upload field is required
  id:
    type: string
    description: The id of the file uploader
