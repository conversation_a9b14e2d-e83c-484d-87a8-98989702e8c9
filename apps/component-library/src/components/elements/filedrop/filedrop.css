/* stylelint-disable plugin/selector-bem-pattern */

/** @define FileDrop; */

.FileDrop {
	display: block;
}

.FileDrop-description {
	font-size: var(--typo-TextM-font-size);
	margin-block-start: var(--size-8);
}

.FileDrop-dropTarget {
	align-items: center;
	background-color: var(--shared-color-surface-subtle-lighter);
	border: var(--border-subtle);
	display: flex;
	flex-direction: column;
	margin-block-start: var(--size-32);
	padding: var(--size-24);
}

.FileDrop-dropTarget--active {
	background-color: var(--shared-color-surface-sandstein);
}

.FileDrop-actionsContainer {
	font-size: var(--typo-TextL-font-size);
	margin-block: var(--size-16);
}

.FileDrop-action--browseLabel {
	pointer-events: none;
	text-decoration: underline;
	text-underline-offset: var(--underline-offset);
}

.FileDrop-maxSize {
	font-size: var(--typo-TextS-font-size);
}

/** Filedrop constraints */
.FileDrop-constraints {
	font-size: var(--typo-TextM-font-size);
	margin-block-start: var(--size-16);
	max-inline-size: 90ch;
}

/** File list */
.FileDrop-fileList:not([hidden]) {
	display: grid;
	gap: var(--size-8);
	margin-block-start: var(--size-16);
}

.FileDrop-fileListItem {
	align-items: center;
	display: flex;
	font-size: var(--typo-TextM-font-size);
	gap: var(--size-8);
	justify-content: space-between;

	span {
		align-items: center;
		display: flex;
		gap: var(--size-8);
	}
}

.FileDrop-fileListContainer--block {
	margin-block-start: var(--size-24);
}

.FileDrop-fileList--block-heading {
	font-size: var(--typo-TextL-font-size);
	font-weight: 500;
}

.FileDrop-fileList--allow svg {
	color: var(--shared-color-surface-success);
}

.FileDrop-fileList--block svg {
	color: var(--shared-color-surface-critical);
}

/** Buttons */
.FileDrop-action--remove {
	padding-block: var(--size-8);
	padding-inline: var(--size-24);

	svg {
		color: inherit;
		pointer-events: none;
	}
}
