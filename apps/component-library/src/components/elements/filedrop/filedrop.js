import { containsFile } from "../../../js/utils/helper/helper";

export default class FileDrop extends HTMLElement {
	static #classNames = {
		fileDropAllowList: "FileDrop-fileList--allow",
		fileDropActionBrowse: "FileDrop-action--browse",
		fileDropActionRemove: "FileDrop-action--remove",
		fileDropFileListItem: "FileDrop-fileListItem",
		fileDropTargetActive: "FileDrop-dropTarget--active",
	};

	static #selectors = {
		fileDropAllowList: ".FileDrop-fileList--allow",
		fileDropBlockList: ".FileDrop-fileList--block",
		fileDropBlockListContainer: ".FileDrop-fileListContainer--block",
		fileDropConfigJSON: "[type='application/json']",
		fileDropTarget: ".FileDrop-dropTarget",
		fileDropIconsTmpl: "[data-tmpl='filedrop-icons-template']",
		fileDropInput: ".FileDrop-input",
		iconAllowed: ".FileDrop-icon--allowed",
		iconBlocked: ".FileDrop-icon--blocked",
	};

	#ALLOWED_IMAGE_FILE_TYPES = [
		"image/bmp",
		"image/gif",
		"image/heic",
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/tiff",
		"image/webp",
	];

	#config;

	#blockListFileArray = [];
	#fileUploadQueue = [];

	#containsFiles = false;

	#elements;

	#toast;

	/**
	 * Checks if the file drop area contains files.
	 * @public
	 * @returns {boolean} True if the file drop area contains files, otherwise false.
	 */
	containsFiles = () => this.#containsFiles;

	/**
	 * Retrieves the list of allowed files. The function returns an array of
	 * files that are permitted based on the current allow list criteria.
	 * @public
	 * @returns {Array} An array of allowed files as File objects.
	 */
	getFiles = () => this.#fileUploadQueue;

	/**
	 * Resets the FileDrop component to its initial state. This function clears
	 * the list of files and hides the FileDrop output containers.
	 * @public
	 * @returns {void}
	 */
	reset = () => this.#reset();

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			fileDropAllowList: /** @type {HTMLUListElement} */ (
				this.querySelector(FileDrop.#selectors.fileDropAllowList)
			),
			fileDropBlockList: /** @type {HTMLUListElement} */ (
				this.querySelector(FileDrop.#selectors.fileDropBlockList)
			),
			fileDropBlockListContainer: /** @type {HTMLDivElement} */ (
				this.querySelector(FileDrop.#selectors.fileDropBlockListContainer)
			),
			fileDropConfigJSON: /** @type {HTMLScriptElement} */ (
				this.querySelector(FileDrop.#selectors.fileDropConfigJSON)
			),
			fileDropTarget: /** @type {HTMLDivElement} */ (
				this.querySelector(FileDrop.#selectors.fileDropTarget)
			),
			fileDropIconsTmpl: /** @type {HTMLTemplateElement} */ (
				this.querySelector(FileDrop.#selectors.fileDropIconsTmpl)
			),
			fileDropInput: /** @type {HTMLInputElement} */ (
				this.querySelector(FileDrop.#selectors.fileDropInput)
			),
		};
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	constructor() {
		super();

		this.#elements = this.#getElements();

		if (this.#elements.fileDropInput && this.#elements.fileDropTarget) {
			this.#init();
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#init() {
		const { fileDropConfigJSON, fileDropInput } = this.#elements;
		fileDropInput.hidden = true;

		if (fileDropConfigJSON) {
			this.#config = JSON.parse(fileDropConfigJSON.textContent);
		}

		let { allowList } = this.#config;
		if (allowList.includes("image/*")) {
			this.#config.allowList = allowList.filter((entry) => entry !== "image/*");
			this.#config.allowList.push(...this.#ALLOWED_IMAGE_FILE_TYPES);
		}

		// @TODO: FileDrop should have its own toast template and not rely on
		// external components providing a toast element.
		if (this.#config.toastId) {
			this.#toast = document.getElementById(this.#config.toastId);

			if (this.#toast.tagName.toLowerCase() === "template") {
				const clone = this.#toast.content.cloneNode(true);
				this.appendChild(clone);
				this.#toast = this.querySelector("finstral-toast");
			}
		}

		this.#addEventListeners();
	}

	/**
	 * Resets the FileDrop area to its initial state. Clears the
	 * file lists and hides the FileDrop output containers.
	 * @private
	 * @returns {void}
	 */
	#reset() {
		const { fileDropBlockListContainer, fileDropInput } = this.#elements;
		const listContainers = ["fileDropAllowList", "fileDropBlockList"];

		listContainers.forEach((container) => {
			const list = this.#elements[container];
			list.innerHTML = "";
			list.hidden = true;
		});

		fileDropBlockListContainer.hidden = true;
		fileDropInput.value = "";

		this.#blockListFileArray = [];
		this.#containsFiles = false;
	}

	/**
	 * Converts bytes to megabytes
	 * @param {number} bytes - The number of bytes to convert
	 * @param {number} decimals - Number of decimal places (default: 2)
	 * @returns {number} The converted value in megabytes
	 */
	#bytesToMB = (bytes, decimals = 2) => {
		const BYTES_PER_MB = 1024 * 1024;
		return Number((bytes / BYTES_PER_MB).toFixed(decimals));
	};

	/**
	 * Converts a size with unit to bytes
	 * @param {number} size - The size to convert
	 * @param {string} unit - The unit to convert from ('B', 'KB', 'MB', 'GB', 'TB')
	 * @returns {number} The converted value in bytes
	 * @throws {Error} If invalid unit is provided
	 */
	#toBytes = (size, unit) => {
		const units = {
			B: 1,
			BYTES: 1,
			KB: 1024,
			MB: 1024 ** 2,
			GB: 1024 ** 3,
			TB: 1024 ** 4,
		};

		// Normalize unit to uppercase and remove any dots or spaces
		const normalizedUnit = unit.toUpperCase().replace(/[. ]/g, "");

		if (!(normalizedUnit in units)) {
			throw new Error(
				`Invalid unit: ${unit}. Valid units are: ${Object.keys(units).join(", ")}`,
			);
		}

		return Math.floor(size * units[normalizedUnit]);
	};

	/**
	 * Validates the total size of all files in the upload queue against the maximum allowed file size.
	 * If the total size exceeds the maximum, it filters out files that exceed the limit and adds them
	 * to the block list. Displays a toast message if the maximum accumulative file size is exceeded
	 * and halts further validation yielding to the renderFileList method.
	 * @private
	 * @returns {void}
	 */
	#validateAccumulativeFileSize = () => {
		const { maxFileSize, messages } = this.#config;
		const maxSizeInBytes = this.#toBytes(maxFileSize.size, maxFileSize.unit);
		const totalSize = this.#fileUploadQueue.reduce(
			(accumulator, file) => accumulator + file.size,
			0,
		);

		if (totalSize > maxSizeInBytes) {
			// The total file size of all files exceeds the maximum size
			// so we need to filter the array of files down to only
			// those that are within the size limit.
			let total = 0;
			this.#fileUploadQueue.forEach((file, index) => {
				total = total + file.size;
				if (total > maxSizeInBytes) {
					const hasMatch = containsFile(file, this.#blockListFileArray);
					if (!hasMatch) {
						this.#blockListFileArray.push(file);
					}

					// remove the file from the upload queue
					this.#fileUploadQueue.splice(index, 1);
				}
			});

			if (this.#toast) {
				this.#toast.setAttribute("toast-role", "alert");
				this.#toast.showToast(messages.maxAccumulativeFileSizeExceeded);
			}
		}
	};

	/**
	 * Validates the file types in the upload queue against the allowed list.
	 * If any file type is not allowed, it removes the file from the queue
	 * and adds it to the block list. Displays a toast message if there are
	 * invalid file formats and yields to the renderFileList method.
	 * @private
	 * @returns {void}
	 */
	#validateFileTypes = () => {
		let { allowList, messages } = this.#config;
		let containsInvalidFormats = false;

		this.#fileUploadQueue.forEach((file, index) => {
			if (!allowList.includes(file.type)) {
				const hasMatch = containsFile(file, this.#blockListFileArray);
				if (!hasMatch) {
					this.#blockListFileArray.push(file);
				}

				// remove the file from the upload queue
				this.#fileUploadQueue.splice(index, 1);
				containsInvalidFormats = true;
			}
		});

		if (containsInvalidFormats && this.#toast) {
			this.#toast.setAttribute("toast-role", "alert");
			this.#toast.showToast(messages.invalidFileFormats);
		}
	};

	/**
	 * Validates the maximum file count in the upload queue. If the number
	 * of files in the queue exceeds the maximum allowed count, excess
	 * files are removed and added to the block list. Displays a
	 * toast message if the maximum file count is exceeded.
	 * @private
	 * @returns {void}
	 */
	#validateMaxFileCount = () => {
		const { maxFileCount, messages } = this.#config;

		if (this.#fileUploadQueue.length <= maxFileCount) {
			return;
		}

		const deleted = this.#fileUploadQueue.splice(maxFileCount);

		if (deleted.length) {
			deleted.forEach((file) => {
				const hasMatch = containsFile(file, this.#blockListFileArray);
				if (!hasMatch) {
					this.#blockListFileArray.push(file);
				}
			});
		}

		if (this.#toast) {
			this.#toast.setAttribute("toast-role", "alert");
			this.#toast.showToast(messages.maxFileCountExceeded);
		}
	};

	/**
	 * Validates and sets the `#blockListFileArray` and `#allowListFileArray` state
	 * entries based on the configuration.
	 * @param {File[]} files - The array of files to validate and set.
	 * @private
	 * @returns {void}
	 */
	#validateAndSetFileUploadQueue(files) {
		const { allowMultiple, maxFileCount, maxFileSize, messages } = this.#config;

		// A user can incrementally add files either using the file dialog
		// or by drag-and-drop. As such, there might have been a
		// previous toast message that needs to be closed.
		if (this.#toast) {
			this.#toast.closeToast();
		}

		if (!allowMultiple && files.length > 1) {
			const deleted = files.splice(1);
			this.#blockListFileArray.push(...deleted);

			if (this.#toast) {
				this.#toast.setAttribute("toast-role", "alert");
				this.#toast.showToast(messages.mutlipleFilesDisallowed);
			}

			return;
		}

		// Up to this point we do not need the queue as we will only
		// allow single file uploads. From here on we must use the
		// queue to handle multiple files, avoid duplicates, and
		// avoid losing files that was previously selected
		// or dropped.
		files.forEach((file) => {
			const hasMatch = containsFile(file, this.#fileUploadQueue);
			if (!hasMatch) {
				this.#fileUploadQueue.push(file);
			}
		});

		this.#validateFileTypes();

		if (maxFileSize.accumulative) {
			this.#validateAccumulativeFileSize();
		}

		if (maxFileCount) {
			this.#validateMaxFileCount();
		}
	}

	/**
	 * Generates the HTML for the remove file button.
	 * @param {string} fileName - The name of the file.
	 * @param {string} listType - The type of the list ('allow' or 'block').
	 * @returns {string} The HTML string for the remove file button.
	 */
	#getRemoveFileButton(fileName, listType) {
		const { fileDropIconsTmpl } = this.#elements;
		const { messages } = this.#config;

		const iconsTmpl = fileDropIconsTmpl.content.cloneNode(true);
		const iconCrossSVG = iconsTmpl.querySelector(
			FileDrop.#selectors.iconBlocked,
		);
		return `<button class="${FileDrop.#classNames.fileDropActionRemove}" data-filename="${fileName}" data-list="${listType}" type="button">
				${iconCrossSVG.outerHTML}
				<span class="visually-hidden">
					${messages.removeFile.replace("@filename", fileName)}
				</span>
			</button>`;
	}

	/**
	 * Renders the file list in the FileDrop component. This function updates the
	 * DOM to display the allowed and blocked files based on the current state.
	 * @private
	 * @returns {void}
	 */
	#renderFileList() {
		const {
			fileDropAllowList,
			fileDropBlockList,
			fileDropBlockListContainer,
			fileDropIconsTmpl,
			fileDropTarget,
		} = this.#elements;

		const iconsTmpl = fileDropIconsTmpl.content.cloneNode(true);
		const iconCheckSVG = iconsTmpl.querySelector(
			FileDrop.#selectors.iconAllowed,
		);
		const iconCrossSVG = iconsTmpl.querySelector(
			FileDrop.#selectors.iconBlocked,
		);

		if (this.#fileUploadQueue.length) {
			const entries = this.#fileUploadQueue.map(
				(file) =>
					`<li class="${FileDrop.#classNames.fileDropFileListItem}">
							<span>${iconCheckSVG.outerHTML} ${file.name} (${this.#bytesToMB(file.size)} MB)</span>
							${this.#getRemoveFileButton(file.name, "allow")}
						</li>`,
			);

			fileDropAllowList.innerHTML = entries.join("");

			this.#containsFiles = true;
			fileDropAllowList.hidden = false;
		}

		if (this.#blockListFileArray.length && fileDropBlockListContainer) {
			const entries = this.#blockListFileArray.map(
				(file) =>
					`<li class="${FileDrop.#classNames.fileDropFileListItem}">
							<span>${iconCrossSVG.outerHTML} ${file.name} (${this.#bytesToMB(file.size)} MB)</span>
							${this.#getRemoveFileButton(file.name, "block")}
						</li>`,
			);

			fileDropBlockList.innerHTML = entries.join("");
			fileDropBlockList.hidden = false;
			fileDropBlockListContainer.hidden = false;
		}

		fileDropTarget.classList.remove(FileDrop.#classNames.fileDropTargetActive);
	}

	/**
	 * Removes an entry from the specified file list based on the filename.
	 * @param {string} filename - The name of the file to be removed from the list.
	 * @param {string} list - The list from which the file should be removed. If "allow" is not provided, "block" is assumed.
	 * @private
	 * @returns {void}
	 */
	#removeEntryFromList = (filename, list) => {
		if (list === "allow") {
			this.#fileUploadQueue = this.#fileUploadQueue.filter(
				(file) => file.name !== filename,
			);
		} else {
			this.#blockListFileArray = this.#blockListFileArray.filter(
				(file) => file.name !== filename,
			);
		}
	};

	// eslint-disable-next-line jsdoc/require-jsdoc
	#addEventListeners() {
		const { fileDropInput, fileDropTarget } = this.#elements;

		this.addEventListener("click", (event) => {
			const target = event.target;

			if (
				target.classList.contains(FileDrop.#classNames.fileDropActionBrowse)
			) {
				fileDropInput.click();
			}

			if (
				target.classList.contains(FileDrop.#classNames.fileDropActionRemove)
			) {
				const { filename, list } = target.dataset;
				this.#removeEntryFromList(filename, list);
				target.parentElement.remove();
			}
		});

		fileDropInput.addEventListener("change", (event) => {
			event.preventDefault();

			const files = Array.from(event.target.files);
			// After we have our list of files we reset the input field.
			// This is because it is not possible to splice individual
			// files from an input field so we primarily use the
			// input field to trigger the file dialog. After
			// this, we handle files the same way as if the
			// user dragged and dropped the files.
			fileDropInput.value = "";
			this.#validateAndSetFileUploadQueue(files);
			this.#renderFileList();
		});

		fileDropTarget.addEventListener("dragover", (event) => {
			event.preventDefault();
			fileDropTarget.classList.add(FileDrop.#classNames.fileDropTargetActive);
		});

		fileDropTarget.addEventListener("drop", (event) => {
			event.preventDefault();

			this.#containsFiles = false;

			let files = Array.from(event.dataTransfer.files);

			this.#validateAndSetFileUploadQueue(files);
			this.#renderFileList();
		});
	}
}

customElements.define("finstral-filedrop", FileDrop);
