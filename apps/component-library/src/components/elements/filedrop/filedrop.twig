{{ attach_library('finstral_global/element-filedrop') }}

{% set allow_multiple = attachments_config.multiple|default(true) %}

{% set allowed_format_defaults = ['image/*', 'application/pdf', 'text/plain', 'text/xml', 'text/csv', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'] %}
{% set allow_list = attachments_config.accepted_formats ?? allowed_format_defaults %}

{% set max_filesize_accumulative = attachments_config.max_filesize.accumulative|default(true) %}
{% set max_file_size_message = max_filesize_accumulative ? "global.max_accumulative_file_size_message" : "global.max_file_size_message" %}
{% set max_file_size = attachments_config.max_filesize.size|default(15) %}
{% set unit = attachments_config.max_filesize.unit|default("MB") %}
{% set max_size = max_file_size ~ unit %}

{% set max_file_count_default = 50 %}

{% set config_data = {
	allowMultiple: allow_multiple,
	allowList: allow_list,
	maxFileCount: attachments_config.max_file_count|default(max_file_count_default),
	maxFileSize: {
		accumulative: max_filesize_accumulative,
		size: attachments_config.max_filesize.size|default(15),
		unit: attachments_config.max_filesize.unit|default('MB'),
	},
	messages: {
		invalidFileFormats: "global.invalid_file_formats"|tc,
		maxAccumulativeFileSizeExceeded: "global.max_accumulative_file_size_exceeded"|tc({
			"@size": max_size,
		}),
		maxFileCountExceeded: "global.max_file_count_exceeded"|tc({
			"@count": attachments_config.max_file_count|default(max_file_count_default),
		}),
		mutlipleFilesDisallowed: "global.multiple_files_disallowed"|tc,
		removeFile: "global.remove_file"|tc,
	},
	toastId: toast_id,
} %}

<finstral-filedrop class="FileDrop {{ classes|join(" ") }}">
	<div class="FileDrop-wrapper">
		{% include "@elements/form-element/label/label.twig" with {
			for: id,
			classes: label_classes,
			title: label,
			visually_hidden: label_visually_hidden|default(false),
			large: true,
		} only %}

		{% if field_description %}
			<p class="FileDrop-description">{{ field_description }}</p>
		{% endif %}

		{% if file_constraints %}
			<p class="FileDrop-constraints">{{ file_constraints }}</p>
		{% endif %}

		<input accept="{{ allow_list|join(", ") }}" class="FileDrop-input" id="{{ id }}" name="{{ name }}" type="file" {% if allow_multiple %} multiple {% endif %}>

		<div class="FileDrop-dropTarget">
			{% include "@elements/icon/icon.twig" with {
				name: "upload",
				classes: ["FileDrop-dropTargetIcon"],
				size: "large",
			} only %}
			<div class="FileDrop-actionsContainer">
				<button class="FileDrop-action--browse" type="button">
					<span class="FileDrop-action--browseLabel">{{ "filedrop.browse"|tc }}</span>
				</button> {{ "filedrop.dragdrop"|tc }} <!-- this ensures there is a space between the two strings -->
			</div>

			<div class="FileDrop-maxSize">
				{{ max_file_size_message|tc({"@size": attachments_config.max_filesize.size|default("15MB"),}) }} ({{ "global.multi_file_message"|tc({"@count": config_data.maxFileCount}) }})
			</div>
		</div>

		<ul class="FileDrop-fileList FileDrop-fileList--allow" hidden></ul>

		<div class="FileDrop-fileListContainer--block" hidden>
			<h3 class="FileDrop-fileList--block-heading visually-hidden">{{ "form_contact_support.file_upload.block_list_heading"|tc }}</h3>
			<ul class="FileDrop-fileList FileDrop-fileList--block"></ul>
		</div>
	</div>

	<template data-tmpl="filedrop-icons-template">
		{% include "@elements/icon/icon.twig" with {
			name: "check_circle_filled",
			classes: ["FileDrop-icon--allowed"],
		} only %}
		{% include "@elements/icon/icon.twig" with {
			name: "delete_filled",
			classes: ["FileDrop-icon--blocked"],
		} only %}
	</template>

	<script type="application/json">{{ config_data|json_encode()|raw }}</script>
</finstral-filedrop>
