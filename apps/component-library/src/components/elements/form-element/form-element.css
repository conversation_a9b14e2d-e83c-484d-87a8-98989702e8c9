@import url("./option/option.css");

/** @define FormElement; weak */

.FormElement {
	grid-column: var(--FormElement-grid-column);
	position: relative;
}

.FormElement--smallGap {
	padding-top: var(--FormElement-smallGap-padding-top);
}

.FormElement--mediumGap {
	padding-top: var(--FormElement-mediumGap-padding-top);
}

.FormElement--largeGap {
	padding-top: var(--FormElement-largeGap-padding-top);
}

.FormElement--centered {
	justify-self: center;
}

.FormElement-description,
.FormElement-error {
	margin-block-start: var(--size-8);
}

.FormElement-error {
	color: var(--shared-color-text-critical);
}

.FormElement.is-invalid .FormElementLabel {
	color: var(--shared-color-text-critical);
}

.FormElement.is-invalid .Input--select {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24'%3E%3Cpath d='M12 15.038 6.345 9.384 7.4 8.331l4.6 4.6 4.6-4.6 1.053 1.053z' fill='%23a6504a'/%3E%3C/svg%3E");
}

.FormElement-error.is-hidden {
	display: none;
}

@media (48em > width) {
	.FormElement {
		--FormElement-grid-column: 1 / -1;
		--FormElement-largeGap-padding-top: var(--size-64);
		--FormElement-mediumGap-padding-top: var(--size-48);
		--FormElement-smallGap-padding-top: var(--size-24);
	}
}

@media (width >= 48em) {
	.FormElement {
		--FormElement-grid-column: var(--start, auto) / span var(--span, 12);
		--FormElement-largeGap-padding-top: var(--size-128);
		--FormElement-mediumGap-padding-top: var(--size-56);
		--FormElement-smallGap-padding-top: var(--size-32);
	}
}
