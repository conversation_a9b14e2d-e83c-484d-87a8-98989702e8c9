{% set label_display = label_display|default("before") %}
<div{{ attributes }}
	class="FormElement
		js-FormElement
		{% if extra_gap %} FormElement--{{ extra_gap }}Gap{% endif %}
		{% if centered %} FormElement--centered{% endif %}
		{%- if errors and errors|length > 0 %} FormElement--error{% endif -%}
		{%- if type == "checkbox" or type == "radio" %}
			Option
			{{ type|capitalize }}
		{% endif -%}
		{{ classes|join(" ") }}"
	style="{%- if span %}--span: {{ span }}; {% endif -%}{%- if start %}--start: {{ start }};{% endif -%}"
>
	{% if label_display == "before" %}
		{{ label }}
	{% endif %}
	{% if prefix is not empty %}
		<span class="FormElement-prefix">{{ prefix }}</span>
	{% endif %}
	{{ children }}
	{% if suffix is not empty %}
		<span class="FormElement-suffix">{{ suffix }}</span>
	{% endif %}
	{% if label_display == "after" %}
		{{ label }}
	{% endif %}
	<div class="FormElement-error js-FormElement-error u-typo-TextS{% if not errors %} is-hidden{% endif %}">
		{% if errors %}
			{% include "@elements/error/error.twig" with {
				content: errors,
			} only %}
		{% endif %}
	</div>
	{% if description %}
		<p{{ description_attributes }} class="FormElement-description u-typo-TextS">
			{{ description }}
		</p>
	{% endif %}
</div>
