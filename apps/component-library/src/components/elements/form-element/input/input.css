/** @define Input; */

.Input {
	--Input-border-color: var(--shared-color-border-distinct);

	appearance: none;
	background-color: var(--shared-color-surface-primary);
	border: var(--shared-border-width-sm) solid var(--Input-border-color);
	color: var(--Input-color, inherit);
	display: block;
	font: inherit;
	inline-size: 100%;
	line-height: var(--input-button-lh);
	min-block-size: var(--input-min-block-size);
	padding: var(--size-8);
	padding-inline-start: var(--size-16);
}

.Input:disabled {
	background-color: var(--shared-color-surface-disabled-secondary);
	color: var(--shared-color-text-disabled);
	pointer-events: none;
}

.Input::placeholder {
	color: var(--shared-color-text-secondary);
}

.Input:not(:disabled, :focus-visible)[aria-invalid="true"] {
	--Input-border-color: var(--shared-color-border-critical);
	--Input-color: var(--shared-color-text-critical);
}

.Input:focus-visible {
	--Input-border-color: transparent;
	outline-offset: 0;
}
