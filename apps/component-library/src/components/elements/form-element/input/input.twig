{% set additional_attributes_string %}
	{% for attribute in additional_attributes %}
		{{ attribute[0] }}="{{ attribute[1] }}"
	{% endfor %}
{% endset %}

<input{{ attributes }}
	{% if id %}id="{{ id }}"{% endif %}
	{% if name %}name="{{ name }}"{% endif %}
	{% if value %}value="{{ value }}"{% endif %}
	class="Input {{ classes|join(" ") }}"
	type="{{ type|default("text") }}"
	{% if disabled %} disabled{% endif %}
	{% if required %} required{% endif %}
	{% if placeholder %} placeholder="{{ placeholder }}"{% endif %}
	{% if invalid %} aria-invalid="true"{% endif %}
	{{ additional_attributes_string|raw }}
>
