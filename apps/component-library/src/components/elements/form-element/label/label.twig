<label{{ attributes }}
	for="{{ for }}"
	class="FormElementLabel
		{{ classes|join(" ") }}
		{%- if large %} u-typo-HeadlineS{% else %} u-typo-TextM{% endif -%}
		{%- if visually_hidden %} visually-hidden{% endif -%}
		{%- if icon %} FormElementLabel--icon{% endif -%}
	"
>
	{% if icon %}
		{% include "@elements/icon/icon.twig" with {
			name: icon_name,
			classes: ["FormElementLabel-icon"],
		} only %}
	{% endif %}
	<span>{{ title }}</span>{%- if required %}<span aria-hidden="true">*</span>{% endif %}
</label>
