$hidden: true
label:
  $tpl: elements/form-element/label
  $ref: elements/form-element/label
$variants:
  - $name: Input
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input1
    children:
      $tpl: elements/form-element/input
      $ref: elements/form-element/input
      id: form-element-input1
  - $name: Input with description
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input2
    children:
      $tpl: elements/form-element/input
      $ref: elements/form-element/input
      id: form-element-input2
    description: This is a description.
  - $name: Input invalid
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input3
    children:
      $tpl: elements/form-element/input
      $ref: elements/form-element/input#invalid
      id: form-element-input3
    errors: "<p>This is an error message.</p>"
  - $name: Input invalid with description
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input4
    children:
      $tpl: elements/form-element/input
      $ref: elements/form-element/input#invalid
      id: form-element-input4
    errors: "<p>This is an error message.</p>"
    description: This is a description.
  - $name: File Input with description
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input-file
    children:
      $tpl: elements/file-uploader
      $ref: elements/file-uploader
      id: form-element-input-file
    description: This is a description.
  - $name: Select
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input5
    children:
      $tpl: elements/select
      $ref: elements/select
      id: form-element-input5
  - $name: Date
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-date
    children:
      $tpl: elements/form-element/input
      $ref: elements/form-element/input
      type: date
      id: form-element-date
  - $name: Textarea
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input6
    children:
      $tpl: elements/form-element/textarea
      $ref: elements/form-element/textarea
      id: form-element-input6
  - $name: Checkbox
    type: checkbox
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label#long-label
      for: form-element-input7
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#checkbox
      id: form-element-input7
  - $name: Checkbox invalid
    type: checkbox
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input8
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#checkbox-invalid
      id: form-element-input8
    errors: "<p>This is an error message.</p>"
  - $name: Checkbox checked
    type: checkbox
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input9
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#checkbox-checked
      id: form-element-input9
  - $name: Checkbox disabled
    type: checkbox
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input10
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#checkbox-disabled
      id: form-element-input10
  - $name: Checkbox invalid and checked
    type: checkbox
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input11
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#checkbox-invalid-and-checked
      id: form-element-input11
    errors: "<p>This is an error message.</p>"
  - $name: Checkbox checked and disabled
    type: checkbox
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input13
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#checkbox-checked-and-disabled
      id: form-element-input13
  - $name: Radio
    type: radio
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label#long-label
      for: form-element-input15
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#radio
      id: form-element-input15
  - $name: Radio invalid
    type: radio
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input16
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#radio-invalid
      id: form-element-input16
    errors: "<p>This is an error message.</p>"
  - $name: Radio checked
    type: radio
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input17
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#radio-checked
      id: form-element-input17
  - $name: Radio disabled
    type: radio
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input18
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#radio-disabled
      id: form-element-input18
  - $name: Radio invalid and checked
    type: radio
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input19
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#radio-invalid-and-checked
      id: form-element-input19
    errors: "<p>This is an error message.</p>"
  - $name: Radio checked and disabled
    type: radio
    label:
      $tpl: elements/form-element/label
      $ref: elements/form-element/label
      for: form-element-input21
    label_display: after
    children:
      $tpl: elements/form-element/option
      $ref: elements/form-element/option#radio-checked-and-disabled
      id: form-element-input21
  - $name: Submit
    label: ""
    children:
      $tpl: elements/button
      $ref: elements/button#primary-button-medium
      type: submit
