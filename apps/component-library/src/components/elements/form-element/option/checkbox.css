/** @define Checkbox; weak */

.Checkbox {
	--bg-image--checked: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.7949219,7.095703 6.1816406,9.601563 12.205078,3.2753906 13.689453,4.8359375 6.1816406,12.724609 2.3105469,8.65625 Z' fill='white'/%3E%3C/svg%3E");
	--bg-image--checkedDisabled: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.7949219,7.095703 6.1816406,9.601563 12.205078,3.2753906 13.689453,4.8359375 6.1816406,12.724609 2.3105469,8.65625 Z' fill='white'/%3E%3C/svg%3E");

	--bg-color--checked: var(--shared-color-surface-brand);
	--bg-color--checkedDisabled: var(--shared-color-surface-disabled);

	--Option-border-color--checked: var(--shared-color-border-brand);
}

.Checkbox :disabled:checked + .FormElementLabel::before {
	--Option-border-color: transparent;
}
