@import url("./checkbox.css");
@import url("./radio.css");

.Option input,
.Option-next input {
	inset-block-start: 0;
	inset-inline-start: 0;
	opacity: 0.01;
	position: absolute;
}

.Option .FormElementLabel {
	align-items: center;
	display: flex;
}

.Option .FormElementLabel::before,
.Option-next::before {
	background: var(--background-image, none) 100% / 100% no-repeat;
	background-color: var(--Option-background-color);
	block-size: var(--size-16);
	border: 0.0625rem solid
		var(--Option-border-color, var(--shared-color-border-distinct));
	border-radius: var(--Option-border-radius, 0);
	content: "";
	flex-shrink: 0;
	inline-size: var(--size-16);
	margin-inline-end: var(--FormElementLabel-gap, var(--size-8));
}

/* checked */
.Option :checked + .FormElementLabel::before,
.Option-next:has(:checked)::before {
	--Option-background-color: var(--bg-color--checked);
	--background-image: var(--bg-image--checked);
	--Option-border-color: var(--Option-border-color--checked);
}

/* focus */
.Option :focus-visible + .FormElementLabel::before,
.Option-next:has(:focus-visible)::before {
	--Option-border-color: transparent;
	outline: var(--focus-outline);
}

/* invalid */
.Option :not(:disabled)[aria-invalid="true"] + .FormElementLabel::before,
.Option-next:has(:not(:disabled)[aria-invalid="true"])::before {
	--Option-border-color: var(--shared-color-border-critical);
}

/* disabled */
.Option :not(:checked):disabled + .FormElementLabel::before,
.Option-next:has(:not(:checked):disabled)::before {
	--Option-background-color: var(--shared-color-surface-disabled-secondary);
}

.Option :disabled:checked + .FormElementLabel::before,
.Option-next:has(:disabled:checked)::before {
	--Option-background-color: var(--bg-color--checkedDisabled);
	--background-image: var(--bg-image--checkedDisabled);
}

.Option :disabled + .FormElementLabel,
.Option-next:has(:disabled)::before {
	color: var(--shared-color-text-secondary);
}

@media (width >= 64em) {
	.Option,
	.Option-next {
		--FormElementLabel-gap: var(--size-12);
	}
}
