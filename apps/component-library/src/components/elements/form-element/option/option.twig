{% set additional_attributes_string %}
  {% for attribute in additional_attributes %}
    {{ attribute[0] }}="{{ attribute[1] }}"
  {% endfor %}
{% endset %}

<input
	id="{{ id }}"
	value="{{ value }}"
	name="{{ name }}"
	class="{{ classes|join(" ") }}"
	type="{{ type }}"
	{% if disabled %}disabled{% endif %}
	{% if checked %}checked{% endif %}
	{% if required %}required{% endif %}
	{% if invalid %}aria-invalid="true"{% endif %}
	{{ additional_attributes_string|raw }}
>
