$schema: http://json-schema.org/draft-07/schema#
$id: /elements/form-element
additionalProperties: false
required:
  - children
properties:
  attributes:
    type: string
  children:
    type: string
    format: html
  description:
    type: string
  description_attributes:
    type: string
  label:
    type: string
    format: html
  label_display:
    type: string
    enum:
      - after
  prefix:
    type: string
  suffix:
    type: string
  disabled:
    type: boolean
  errors:
    type: string
    format: html
  title_display:
    type: string
    enum:
      - after
      - before
  type:
    type: string
  span:
    type: number
    minimum: 1
    maximum: 12
  start:
    type: number
    minimum: 1
    maximum: 12
  extra_gap:
    type: string
    enum:
      - small
      - medium
      - large
  centered:
    type: boolean
