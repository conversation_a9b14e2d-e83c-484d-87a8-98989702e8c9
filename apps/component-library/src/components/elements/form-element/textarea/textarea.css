/** @define Textarea; */

/**
	* Buttons and Inputs usually have the same (to make sure they look aligned when placed next to each other)
	* and a lower line-height than normal text.
	* Since text in textareas has a higher line height, using the same padding as inputs
	* would make it look like more padding.
	* That's why we need to reduce the padding of textareas, so it appears to be the same as for inputs.
	*/

.Textarea {
	block-size: 10em;
	line-height: inherit;
	padding-block: calc(
		var(--input-button-padding-block) - (1lh - var(--input-button-lh) * 1em) / 2
	);
	resize: none;
}
