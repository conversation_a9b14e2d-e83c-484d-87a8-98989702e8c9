/* stylelint-disable plugin/selector-bem-pattern */

/** @define FormValidationErrors; */

.FormValidationErrors:not([hidden]) {
	color: var(--shared-color-text-critical);
	display: block;
	margin-block-end: var(--form-validation-errors-block-end);
	scroll-margin-block-start: calc(
		var(--header-min-block-size) + 1rem
	); /* 1rem is the gap between the header and the form validation errors */
}

@media (width < 48rem) {
	.FormValidationErrors {
		--form-validation-errors-block-end: var(--size-48);
	}
}

@media (width >= 48rem) {
	.FormValidationErrors {
		--form-validation-errors-block-end: var(--size-64);
	}
}

.FormValidationErrors-list,
.FormValidationErrors-title {
	font: var(--typo-TextM);
}

.FormValidationErrors-title {
	margin-block-end: var(--size-32);
}

.FormValidationErrors-list {
	display: grid;
	gap: var(--size-8);
}

.FormValidationErrors-list a {
	color: var(--shared-color-text-critical);
	text-decoration: underline;
}

.FormValidationErrors-list a:focus,
.FormValidationErrors-list a:hover {
	text-decoration: none;
}

.FormValidationErrors-item {
	display: flex;
	gap: var(--size-4);
}

@media (width >= 48rem) {
	.FormValidationErrors-item {
		align-items: center;

		svg {
			margin-block-start: -0.25rem;
		}
	}
}
