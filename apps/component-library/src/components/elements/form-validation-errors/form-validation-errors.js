export default class FinstralFormValidationErrors extends HTMLElement {
	static #selectors = {
		formValidationErrorsList: ".FormValidationErrors-list",
		formValidationErrorsTitle: ".FormValidationErrors-title",
		formValidationErrorsMounted: ".FormValidationErrors-mounted",
		formValidationErrorsTmpl: "#validation-errors-base",
		formValidationErrorsIconTmpl: "#validation-errors-icons",
		iconError: ".FormValidationErrors-icon--error",
	};

	#elements;

	#shouldReduceMotionQL;

	#tmplElements;

	#validatonErrors;

	/**
	 * Renders the validation errors list. Sending an empty
	 * errors array will clear the list.
	 * @typedef {ValidationError[]} ValidationError
	 * ## Errors Object
	 * {
	 * 	lead?: string;
	 * 	errors: [{
	 * 		fieldName: string;
	 * 		id: string;
	 * 		message: string;
	 * 	}]
	 * }
	 * @property {ValidationError} errors - The validation errors object.
	 * @param {Array} errors - The ValidationError array.
	 * @public
	 * @returns {void}
	 */
	setValidationErrors(errors) {
		this.#validatonErrors = errors;
		this.#render();
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			formValidationErrorsList: /** @type {HTMLOListElement} */ (
				this.querySelector(
					FinstralFormValidationErrors.#selectors.formValidationErrorsList,
				)
			),
			formValidationErrorsTitle: /** @type {HTMLHeadingElement} */ (
				this.querySelector(
					FinstralFormValidationErrors.#selectors.formValidationErrorsTitle,
				)
			),
			formValidationErrorsTmpl: /** @type {HTMLTemplateElement} */ (
				this.querySelector(
					FinstralFormValidationErrors.#selectors.formValidationErrorsTmpl,
				)
			),
			formValidationErrorsIconTmpl: this.querySelector(
				FinstralFormValidationErrors.#selectors.formValidationErrorsIconTmpl,
			),
		};
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getTmplElements() {
		const { formValidationErrorsTmpl } = this.#elements;
		return {
			formValidationErrorsList: /** @type {HTMLUListElement} */ (
				formValidationErrorsTmpl.content.querySelector(
					FinstralFormValidationErrors.#selectors.formValidationErrorsList,
				)
			),
			formValidationErrorsTitle: /** @type {HTMLHeadingElement} */ (
				formValidationErrorsTmpl.content.querySelector(
					FinstralFormValidationErrors.#selectors.formValidationErrorsTitle,
				)
			),
		};
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	constructor() {
		super();

		this.#elements = this.#getElements();
		this.#tmplElements = this.#getTmplElements();
		this.#shouldReduceMotionQL = window.matchMedia(
			"(prefers-reduced-motion: reduce)",
		);
	}

	/**
	 * Renders the validation errors list based on the current validation errors.
	 * If there are no errors, it removes the mounted validation errors element.
	 * @private
	 * @returns {void}
	 */
	#render() {
		const formValidationErrorsMounted = this.querySelector(
			FinstralFormValidationErrors.#selectors.formValidationErrorsMounted,
		);
		const { lead, errors } = this.#validatonErrors;

		if (!errors.length && !formValidationErrorsMounted) {
			return;
		}

		if (!errors.length && formValidationErrorsMounted) {
			this.hidden = true;
			formValidationErrorsMounted.remove();
			return;
		}

		if (formValidationErrorsMounted) {
			this.hidden = true;
			formValidationErrorsMounted.remove();
		}

		const { formValidationErrorsTmpl, formValidationErrorsIconTmpl } =
			this.#elements;

		const { formValidationErrorsList, formValidationErrorsTitle } =
			this.#tmplElements;

		if (lead) {
			formValidationErrorsTitle.textContent = lead;
		}

		const iconsTmpl = formValidationErrorsIconTmpl.content.cloneNode(true);
		const iconErrorSVG = iconsTmpl.querySelector(
			FinstralFormValidationErrors.#selectors.iconError,
		);

		// Data will only be provided by other developers using
		// the public API. The data is not user generated.
		formValidationErrorsList.innerHTML = errors
			.map(
				({ fieldName, id, message }) => `<li class="FormValidationErrors-item">
						${iconErrorSVG.outerHTML}
						<a href="#${id}">${message.replace("@fieldname", fieldName)}</a>
					</li>`,
			)
			.join("");

		this.hidden = false;
		this.appendChild(formValidationErrorsTmpl.content.cloneNode(true));

		// The current this.#elements.formValidationErrorsTitle points to the element
		// inside the template. Here we get the element added to the DOM above.
		this.querySelector(
			FinstralFormValidationErrors.#selectors.formValidationErrorsTitle,
		).focus();

		this.scrollIntoView({
			behavior: this.#shouldReduceMotionQL.matches ? "auto" : "smooth",
		});
	}
}

customElements.define(
	"finstral-form-validation-errors",
	FinstralFormValidationErrors,
);
