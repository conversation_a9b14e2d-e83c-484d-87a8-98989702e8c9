{{ attach_library('finstral_global/element-form-validation-errors') }}

<finstral-form-validation-errors class="FormValidationErrors" hidden>
	<template id="validation-errors-base">
		<section aria-labelledby="form-validation-errors-title" class="FormValidationErrors-mounted">
			<h3 class="FormValidationErrors-title" id="form-validation-errors-title" tabindex="-1">{{ "global.form_errors.lead"|tc }}</h3>
			<ol class="FormValidationErrors-list"></ol>
		</section>
	</template>

	<template id="validation-errors-icons">
		{% include "@elements/icon/icon.twig" with {
			name: "error_circle",
			classes: ["FormValidationErrors-icon--error"],
		} only %}
	</template>

</finstral-form-validation-errors>
