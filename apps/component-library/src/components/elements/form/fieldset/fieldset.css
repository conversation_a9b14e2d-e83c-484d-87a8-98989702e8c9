/** @define Fieldset; weak */

.Fieldset {
	border: none;
	grid-column: var(--Fieldset-grid-column);
	min-inline-size: 0;
	padding: 0;
}

/**
 * Fieldset legend.
 */

.Fieldset-legend {
	display: contents; /* For Firefox. */
	float: inline-start; /* iOS Safari, Android Chrome, Edge. */
	inline-size: 100%; /* iOS Safari, Android Chrome, Edge. */
}

.Fieldset-label {
	display: block;
	margin-block-end: var(--size-8);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.Fieldset-label.has-error {
	color: var(--shared-color-text-critical);
}

.Fieldset-description {
	margin-block-start: var(--size-8);
}

.Fieldset-wrapper {
	display: flex;
	flex-direction: var(--Fieldset-wrapper-flex-direction);
	flex-wrap: var(--Fieldset-wrapper-flex-wrap);
	gap: var(--Fieldset-wrapper-gap, var(--size-24));
}

.Fieldset-wrapper--col {
	--Fieldset-wrapper-flex-direction: column;
	--Fieldset-wrapper-flex-wrap: none;
}

.Fieldset-wrapper--row {
	--Fieldset-wrapper-flex-direction: row;
	--Fieldset-wrapper-flex-wrap: wrap;
	--Fieldset-wrapper-gap: var(--size-48);
}

@media (48em > width) {
	.Fieldset {
		--Fieldset-grid-column: 1 / -1;
	}
}

@media (width >= 48em) {
	.Fieldset {
		--Fieldset-grid-column: var(--start, auto) / span var(--span, 12);
	}

	.Fieldset-wrapper--col {
		--Fieldset-wrapper-gap: var(--size-32);
	}
}

.Fieldset.is-invalid .FormElementLabel {
	color: var(--shared-color-text-critical);
}
