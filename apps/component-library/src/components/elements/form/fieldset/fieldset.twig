<fieldset{{ attributes }}
	class="Fieldset js-Fieldset {{ classes|join(" ") }}"
	style="{%- if span %}--span: {{ span }}; {% endif -%}{%- if start %}--start: {{ start }};{% endif -%}"
>
	<legend{{ legend_attributes }} class="Fieldset-legend">
		<span{{ title_attributes }} class="Fieldset-label u-typo-TextM  {% if is_title_hidden %} u-hiddenVisually{% endif %}">{{ title }}</span>
	</legend>
	<div class="Fieldset-wrapper Fieldset-wrapper--{{ layout|default("col") }}">
		{% if prefix %}
			<span class="Fieldset-prefix u-typo-TextS">{{ prefix }}</span>
		{% endif %}
		{{ children }}
		{% if suffix %}
			<span class="Fieldset-suffix u-typo-TextS">{{ suffix }}</span>
		{% endif %}
		<div class="Fieldset-error js-Fieldset-error u-typo-TextS">
			{% if errors %}
				{% include "@elements/error/error.twig" with {
					content: errors,
				} only %}
			{% endif %}
		</div>
		{% if description %}
			<p{{ description_attributes }} class="Fieldset-description u-typo-TextS">{{ description }}</p>
		{% endif %}
	</div>
</fieldset>
