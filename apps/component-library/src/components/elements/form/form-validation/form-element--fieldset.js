/**
 * Represents a custom fieldset validation:
 * check if at least one select element inside isn't zero
 */
class FormElementFieldset {
	/**
	 * @param {HTMLElement} element - layout fieldset
	 */
	constructor(element) {
		this.element = element;

		this.config = {
			selectors: {
				parent: ".js-Fieldset",
				validationItems: "select, input[type=checkbox], input[type=radio]",
				errorContainer: ".js-Fieldset-error",
			},
			classes: {
				hidden: "is-hidden",
			},
		};

		this.errorContainer = this.element.querySelector(
			this.config.selectors.errorContainer,
		);

		this.errorMessage = this.errorContainer.innerHTML;

		this.validate = this.validate.bind(this);

		this.#bind();
	}

	/**
	 * binds events
	 * @private
	 */
	#bind() {
		this.element.addEventListener("input", this.validate);
	}

	/**
	 * returns if the element is valid (if at least one one the inputs is not zero).
	 * @returns {boolean}
	 */
	isValid() {
		const elements = [
			...this.element.querySelectorAll(this.config.selectors.validationItems),
		];
		return elements.some((formElement) => {
			if (formElement.tagName === "SELECT") {
				return parseInt(formElement.value, 10) !== 0;
			}
			if (formElement.tagName === "INPUT") {
				return formElement.validity.valid;
			}
			return false;
		});
	}

	/**
	 * validates user input and sets error message
	 */
	validate() {
		this.toggleParentClass();
		if (!this.errorContainer) {
			return;
		}
		if (!this.isValid()) {
			this.errorContainer.classList.remove(this.config.classes.hidden);
		} else {
			this.errorContainer.classList.add(this.config.classes.hidden);
		}
	}

	/**
	 *
	 */
	toggleParentClass() {
		this.element
			.closest(this.config.selectors.parent)
			?.classList.toggle("is-invalid", !this.isValid());
	}
}

export default FormElementFieldset;
