export default class FormElementDropzone {
	static #selectors = {
		errorsContainer: ".js-FileUploader-errors",
		errorMessageContainer: ".js-FileUploader-error",
		files: ".js-FileUploader-uploaded-files",
		requiredAttribute: "data-required",
	};

	#elements;

	/**
	 * Constructs a new instance of the FormElementDropzone class.
	 * @param {HTMLElement} element - The HTML element associated with the form element dropzone.
	 */
	constructor(element) {
		this.element = element;
		this.#setElements();
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setElements() {
		this.#elements = {
			errorsContainer: this.element.querySelector(
				FormElementDropzone.#selectors.errorsContainer,
			),
			errorMessageContainer: this.element.querySelector(
				FormElementDropzone.#selectors.errorMessageContainer,
			),
			files: this.element.querySelector(FormElementDropzone.#selectors.files),
		};
	}

	/**
	 * Checks if the Dropzone is required and is valid.
	 * @returns {boolean} True if the Dropzone is valid or not required, false otherwise.
	 */
	isValid() {
		const fileUploadRequired = this.element.hasAttribute(
			FormElementDropzone.#selectors.requiredAttribute,
		);

		if (!fileUploadRequired) {
			return true;
		}

		const hasUploadedFiles = this.#elements.files.value !== "";
		return hasUploadedFiles;
	}

	/**
	 * Validates the Dropzone and sets an error message if the field is in error.
	 */
	validate() {
		const { errorsContainer, errorMessageContainer } = this.#elements;
		const errorMessage = this.element.dataset.errorRequired;

		errorsContainer.hidden = true;

		if (!this.isValid()) {
			errorsContainer.hidden = false;
			errorMessageContainer.textContent = errorMessage;
		}
	}
}
