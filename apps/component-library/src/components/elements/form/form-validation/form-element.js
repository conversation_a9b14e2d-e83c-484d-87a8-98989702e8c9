/**
 * Represents a Form Element e.g. input, textarea, select
 */
class FormElement {
	/**
	 * @param {HTMLElement} element - form element
	 */
	constructor(element) {
		this.element = element;

		this.config = {
			classes: {
				touched: "is-touched",
			},
			attributes: {
				patternError: "data-pattern-error",
				requiredError: "data-required-error",
			},
		};

		this.patternError = this.element.getAttribute(
			this.config.attributes.patternError,
		);
		this.requiredError = this.element.getAttribute(
			this.config.attributes.requiredError,
		);

		this.validate = this.validate.bind(this);

		this.#init();
	}

	/**
	 * init
	 * @private
	 */
	#init() {
		this.#setErrorContainer();
		this.#bind();
	}

	/**
	 * sets correct error container of the form element
	 * @private
	 */
	#setErrorContainer() {
		const parentMap = {
			Default: ".js-FormElement",
		};

		this.parentElement = this.element.closest(parentMap.Default);

		// set error container based on parent
		this.errorContainer = this.parentElement
			? this.parentElement.querySelector(`${parentMap.Default}-error`)
			: null;
	}

	/**
	 * binds events
	 * @private
	 */
	#bind() {
		this.element.addEventListener("input", this.validate);
	}

	/**
	 * validates user input and sets error message
	 */
	validate() {
		if (!this.element.classList.contains(this.config.classes.touched)) {
			this.element.classList.add(this.config.classes.touched);
		}

		if (this.errorContainer) {
			this.errorContainer.innerHTML = !this.isValid()
				? this.getErrorMessage()
				: "";
		}

		this.parentElement?.classList.toggle("is-invalid", !this.isValid());
		this.errorContainer?.classList.toggle("is-hidden", this.isValid());
	}

	/**
	 * Set up appropriate error message(s).
	 * Concatenate all of them if there is more than one.
	 * @returns {string} errorMessage - error message
	 */
	getErrorMessage() {
		let errorMessage;
		if (this.patternError && this.requiredError) {
			errorMessage = `${this.patternError}<br/>${this.requiredError}`;
		} else if (this.patternError) {
			errorMessage = this.patternError;
		} else if (this.requiredError) {
			errorMessage = this.requiredError;
		} else {
			errorMessage = "Fehlerhafte Eingabe";
		}
		return errorMessage;
	}

	/**
	 * returns if the element is valid based on the ValidityState
	 * @returns {boolean} valid - validity state
	 */
	isValid() {
		return this.element.validity.valid;
	}

	/**
	 * sets the focus to the element
	 */
	focus() {
		this.element.focus();
	}
}

export default FormElement;
