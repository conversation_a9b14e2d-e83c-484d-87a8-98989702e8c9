import { scrollToElement } from "../../../../js/utils/helper/helper.js";
import FormElement from "./form-element.js";
import FormElementFieldset from "./form-element--fieldset.js";
import FormElementDropzone from "./form-element-dropzone.js";

const EVENTS = {
	FORM_SUBMITTED: "FORM_SUBMITTED",
};

/**
 * Represents a Form including e.g. fieldsets, inputs, textarea, selects, buttons
 */
class FormValidation {
	/**
	 * @param {HTMLElement} element - form
	 */
	constructor(element) {
		this.element = element;
		this.config = {
			formElements: [
				{
					selector: ".js-Fieldset",
					Class: FormElementFieldset,
				},
				{
					selector: "input, textarea, select",
					Class: FormElement,
				},
				{
					selector: ".js-FileUploader",
					Class: FormElementDropzone,
				},
			],
		};

		// set correct scope for callbacks
		this.onSubmit = this.onSubmit.bind(this);

		this.#init();
	}

	/**
	 * init
	 * @private
	 */
	#init() {
		this.#disableDefaultBrowserValidation();
		this.#bind();
	}

	/**
	 * disables custom browser validation and activates custom clientside form validation
	 * @private
	 */
	#disableDefaultBrowserValidation() {
		this.element.setAttribute("novalidate", "novalidate");
	}

	/**
	 * inits children form elements like input, textarea, select or custom form elements
	 * [Fieldset, FormElement, FormElement, ...]
	 * @private
	 */
	#initFormElements() {
		this.formElements = [];

		this.config.formElements.forEach((formElement) => {
			const domElements = [
				...this.element.querySelectorAll(formElement.selector),
			];

			domElements.forEach((domElement) => {
				this.formElements.push(new formElement.Class(domElement));
			});
		});
	}

	/**
	 * binds events
	 * @private
	 */
	#bind() {
		this.element.addEventListener("submit", this.onSubmit);
	}

	/**
	 * handles form submit
	 * @private
	 * @param {object} event - submit event
	 */
	onSubmit(event) {
		const submitButton = event.submitter;
		if (submitButton && submitButton.hasAttribute("formnovalidate")) {
			return;
		}

		this.#initFormElements();

		const allElementsValid = this.formElements.every((formElement) =>
			formElement.isValid(),
		);

		if (!allElementsValid) {
			event.preventDefault();
			// validate inputs and set error message
			this.formElements.forEach((formElement) => {
				formElement.validate();
			});

			this.#focusFirstError();
		} else {
			this.element.dispatchEvent(new CustomEvent(EVENTS.FORM_SUBMITTED));
		}
	}

	/**
	 * focuses first form element with error state
	 * it scrolls smoothly to that element based on the correct position
	 * native focus scroll wouldn't consider the sticky header
	 * @private
	 */
	#focusFirstError() {
		const { element } = this.formElements.find(
			(formElement) => !formElement.isValid(),
		);

		const fileUploadErrorContainer = element.querySelector(
			".js-FileUploader-errors",
		);

		// if the field that is in error is a file upload (Dropzone), focus the error container
		// as there is no native HTML element to focus on.
		if (fileUploadErrorContainer) {
			fileUploadErrorContainer.focus({ preventScroll: true });
		} else {
			element.focus({ preventScroll: true });
		}

		scrollToElement(element);
	}
}

export default FormValidation;
