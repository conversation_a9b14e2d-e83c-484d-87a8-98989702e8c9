method: GET
action: action
children:
  $render:
    - $tpl: elements/form-element
      $ref: elements/form-element#input
    - $tpl: elements/form-element
      $ref: elements/form-element#select
    - $tpl: elements/form-element
      $ref: elements/form-element#textarea
    - $tpl: elements/form-element
      $ref: elements/form-element#checkbox
    - $tpl: elements/form-element
      $ref: elements/form-element#submit
$variants:
  - $name: Application form
    copy: "*Bitte füllen Sie alle Pflichtfelder aus."
    $opts:
      children: overwrite
    children:
      $render:
        - $tpl: elements/form/fieldset
          $ref: elements/form/fieldset#radios
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 6
          start: 1
          label:
            title: Vorname *
            for: firstname
          children:
            $tpl: elements/form-element/input
            $ref: elements/form-element/input
            id: firstname
            title: Vorname
            required: true
            attributes: ' pattern="[A-Za-z]{1,}" data-required-error="This field is required"'
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 6
          label:
            title: Nachname *
            for: lastname
          children:
            $tpl: elements/form-element/input
            $ref: elements/form-element/input
            id: lastname
            title: Nachname
            required: true
            attributes: ' pattern="[A-Za-z]{1,}" data-required-error="This field is required"'
        - $tpl: elements/form-element
          $ref: elements/form-element#date
          span: 6
          label:
            title: Geburtsdatum *
            for: birthdate
          children:
            id: birthdate
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 6
          start: 1
          label:
            title: E-Mail Adresse *
            for: email
          children:
            $tpl: elements/form-element/input
            $ref: elements/form-element/input
            id: email
            title: E-Mail Adresse
            type: email
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 6
          label:
            title: Telefon *
            for: phone
          children:
            $tpl: elements/form-element/input
            $ref: elements/form-element/input
            id: phone
            title: Telefon
            type: tel
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 6
          label:
            title: Straße, Nr. *
            for: street
          children:
            id: street
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 2
          label:
            title: Postleitzahl *
            for: zip
          children:
            id: zip
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 4
          label:
            title: Ort *
            for: city
          children:
            id: city
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 6
          label:
            title: Bundesland / Provinz
            for: state
          children:
            id: state
        - $tpl: elements/form-element
          $ref: elements/form-element#select
          span: 6
          label:
            title: Land *
            for: country
          children:
            $ref: elements/select#with-placeholder
            id: country
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          span: 6
          label:
            title: Staatsangehörigkeit *
            for: nationality
          children:
            id: nationality
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#select
          span: 6
          label:
            title: Muttersprache *
            for: mother-tongue
          children:
            $ref: elements/select#with-placeholder
            id: mother-tongue
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element#input
          extra_gap: medium
          label:
            title: Sonstige Sprachkenntnisse
            for: other-languages
          children:
            id: other-languages
        - $tpl: elements/form-element
          $ref: elements/form-element
          label:
            title: Bewerbungstext *
            for: application-text
          children:
            $tpl: elements/form-element/textarea
            $ref: elements/form-element/textarea
            id: application-text
            required: true
        - $tpl: elements/form-element
          $ref: elements/form-element
          label:
            title: Dateiupload
            large: true
            for: file-upload
          children:
            $tpl: elements/file-uploader
            $ref: elements/file-uploader
            id: file-upload
        - $tpl: elements/rich-text
          $ref: elements/rich-text
          content: >
            <p>Zustimmung zur Datenverarbeitung (mehr Infos zum Datenschutz)</p><br>
            <p>Nach vollständiger Einsichtnahme in das Informations-schreiben gemäß Art. 13 der EU- Verordnung 2016/679 erteile ich hiermit die Einwilligung zur Verarbeitung meiner – auch besonderen – personenbezogenen Daten innerhalb der Grenzen und zu den Zwecken, die im Informations-schreiben angeführt sind.</p>
        - $tpl: elements/form/fieldset
          $ref: elements/form/fieldset
          is_title_hidden: true
          children:
            $render:
              - $tpl: elements/form-element
                $ref: elements/form-element#checkbox
                label:
                  title: Ich bestätige die Kenntnisnahme der Informationen zum&nbsp;<a href="foo.bar">Datenschutz für Bewerber</a>.&nbsp;*
                  for: dataprotection
                children:
                  id: dataprotection
                  required: true
              - $tpl: elements/form-element
                $ref: elements/form-element#checkbox
                children:
                  id: policy
                label:
                  title: Für den Fall, dass meine Bewerbung nicht erfolgreich ist, dürfen meine Bewerberdaten für weitere Stellenausschreibungen aufbewahrt werden. Die konkrete Dauer finden Sie&nbsp;<a href="foo.bar">hier</a>
                  for: policy
              - $tpl: elements/form-element
                $ref: elements/form-element#checkbox
                children:
                  id: corporate-group-applicant-data
                label:
                  title: Meine Bewerberdaten dürfen auch für offene Stellenausschreibungen anderer Unternehmen der Unternehmensgruppe berücksichtigt werden.
                  for: corporate-group-applicant-data
        - $tpl: elements/form-element
          $ref: elements/form-element#submit
          centered: true
          children:
            label: Jetzt bewerben
