# Icon Component

A reusable SVG icon component that uses sprite sheets for efficient icon rendering.

## Usage

Include the icon component in your Twig template:

```twig
{% include "@elements/icon/icon.twig" with {
    name: "home",
    size: "medium",
    classes: ["MyComponent-icon"],
    label: "Home page"
} only %}
```

## Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `name` | string | ✅ | - | The icon name from the sprite sheet |
| `sprite_sheet` | string | ❌ | `"icons"` | The sprite sheet to use (`"icons"`, `"file-icons"`, `"landing-pages"`) |
| `size` | string | ❌ | `"medium"` | Icon size: `"small"`, `"medium"`, `"large"`, `"xlarge"`, `"xxlarge"` |
| `classes` | array | ❌ | `[]` | Additional CSS classes |
| `label` | string | ❌ | - | Accessible label for screen readers (adds `aria-labelledby` and `<title>`) |

## Available Sizes

- `small`: 16px (var(--size-16))
- `medium`: 24px (var(--size-24)) - **default**
- `large`: 32px (var(--size-32))
- `xlarge`: 48px (var(--size-48))
- `xxlarge`: 64px (var(--size-64))

## Available Sprite Sheets

### Icons (default)
General UI icons: `admin`, `arrow_back`, `arrow_forward`, `arrow_up`, `arrow_down`, `audio`, `bulletpoint`, `check`, `check_circle`, `check_circle_filled`, `chevron_down`, `chevron_left`, `chevron_right`, `chevron_up`, `circle`, `circle_filled`, `circle_warning`, `close`, `cloud`, `delete`, `delete_filled`, `divider`, `edit`, `email`, `error`, `error_circle`, `error_circle_filled`, `facebook`, `filter`, `handbook`, `info`, `instagram`, `job`, `linkedin`, `login`, `menu`, `muted`, `note`, `notes`, `pause`, `phone`, `pin`, `pinterest`, `play`, `replay`, `search`, `settings`, `switch_off`, `switch_on`, `switch_pending`, `upload`, `user`, `user_create`, `warning`, `x`, `youtube`

### File Icons
File type icons: `pdf`

### Landing Pages
Landing page specific icons: `email`, `home`, `location`

## Examples

### Basic Usage
```twig
{% include "@elements/icon/icon.twig" with {
    name: "search"
} only %}
```

### With Size and Classes
```twig
{% include "@elements/icon/icon.twig" with {
    name: "chevron_right",
    size: "small",
    classes: ["Breadcrumb-icon"]
} only %}
```

### With Accessibility Label
```twig
{% include "@elements/icon/icon.twig" with {
    name: "close",
    label: "Close dialog"
} only %}
```

### Using Different Sprite Sheet
```twig
{% include "@elements/icon/icon.twig" with {
    name: "pdf",
    sprite_sheet: "file-icons"
} only %}
```

### In Button Component
```twig
{% include "@elements/button/button.twig" with {
    label: "Download",
    icon: {
        name: "upload",
        size: "medium"
    }
} only %}
```

## Accessibility

- Icons are marked as `aria-hidden="true"` by default
- When `label` is provided, the icon becomes accessible with `aria-labelledby` and a `<title>` element
- Icons have `role="img"` and `focusable="false"` for proper screen reader behavior

## Styling

- Icons use `fill: currentColor` to inherit text color
- Icons maintain 1:1 aspect ratio
- Size is controlled via CSS custom properties

## Notes

- Icons are rendered as SVG `<use>` elements referencing sprite sheets
- The component automatically generates unique IDs for accessibility when labels are provided
