{%- if label is defined -%}
	{%- set random = random() -%}
{%- endif -%}
<svg
	xmlns="http://www.w3.org/2000/svg"
	class="Icon Icon--{{ name }} {{ classes|join(" ") }} Icon--{{ size|default("medium") }}"
	focusable="false"
	role="img"
	{%- if not label %} aria-hidden="true"{% else %} aria-labelledby="Icon--{{ name }}-{{ random }}"{% endif -%}>
	{%- if label is defined -%}<title id="Icon--{{ name }}-{{ random }}">{{ label }}</title>{% endif -%}
	<use
		xmlns:xlink="http://www.w3.org/1999/xlink"
		xlink:href="{{ asset_path() }}/svg/{{ sprite_sheet|default("icons") }}.sprite.svg#{{ name }}"
	>
	</use>
</svg>
