## Image Styles

Whenever <PERSON><PERSON><PERSON> needs to provide an image in specific dimenions, FE needs to provide an image style.

Create a file in `/.tools/es/responsive_image` and define the height and width of the image for each breakpoint. Afterwards run `phab scaffold .tools/es/index.yml` to create the image styles. They are getting created in `/config/sync`. Don't forget to commit them and let the BE devs know what the relevant file is.

To see the changes locally:

1. the configurations generated above must be imported via:
   ```
   phab -cmbb drush cim
   ```
2. image's cache has to be cleaned via (press `1` when you are prompted to):
   ```
   phab -cmbb drush 'image-flush --all'
   ```
3. clean cache via:
   ```
   phab -cmbb drush cr
   ```

### Troubleshooting:

If you get any php errors during running `phab scaffold .tools/es/index.yml` you might need to update the scaffolder via `composer update factorial-io/phab-entity-scaffolder`
