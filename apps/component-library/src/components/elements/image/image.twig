{% if sources and sources is not empty %}
	<picture {% if picture_classes %}class="{{ picture_classes|join(" ") }}"{% endif %}>
		{% for source in sources %}
			<source
				{% if source.type %}
					type="{{ source.type }}"
				{% endif %}
				{% if source.media %}
					media="{{ source.media }}"
				{% endif %}
				{% if source.width %}
					width="{{ source.width }}"
				{% endif %}
				{% if source.height %}
					height="{{ source.height }}"
				{% endif %}
				srcset="{{ source.srcset }}"
			>
		{% endfor %}
		<img
			{% if classes %}
				class="{{ classes|join(" ") }}"
			{% endif %}
			src="{{ uri }}"
			alt="{{ alt }}"
			{% if alt == "" %}
				aria-hidden="true"
			{% endif %}
			{% if width %}
				width="{{ width }}"
			{% endif %}
			{% if height %}
				height="{{ height }}"
			{% endif %}
			{% if is_lazy %}
				loading="lazy"
				decoding="async"
			{% elseif is_priority %}
				fetchpriority="high"
			{% endif %}
		>
	</picture>
{% else %}
	<img
		{% if classes %}
			class="{{ classes|join(" ") }}"
		{% endif %}
		src="{{ uri }}"
		alt="{{ alt }}"
		{% if alt == "" %}
			aria-hidden="true"
		{% endif %}
		{% if width %}
			width="{{ width }}"
		{% endif %}
		{% if height %}
			height="{{ height }}"
		{% endif %}
		{% if is_lazy %}
			loading="lazy"
			decoding="async"
		{% elseif is_priority %}
			fetchpriority="high"
		{% endif %}
	>
{% endif %}
