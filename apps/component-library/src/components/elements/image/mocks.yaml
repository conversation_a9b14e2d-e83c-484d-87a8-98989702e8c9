uri: /build/assets/img/dummy/320x240.png
height: 240
width: 320
alt: Sample alt text
$variants:
  - $name: src with lazy
    is_lazy: true

  - $name: standard slp hero
    uri: /build/assets/img/dummy/standalone-landing/finstral-slp-hero.jpg
    height: 1080
    width: 1920
    alt: Sample alt text

  - $name: carousel slider 9x16
    uri: /build/assets/img/dummy/carousel-slider/carousel-001.jpg
    height: 540
    width: 432
    alt: Sample alt text

  - $name: carousel slider icon
    uri: /build/assets/img/dummy/carousel-slider/experience.svg
    height: 64
    width: 64
    alt: 20 years experience

  - $name: partner logo
    uri: /build/assets/img/dummy/partner/kroiher.webp
    height: 68
    width: 362
    alt: Kroiher logo

  - $name: srcset with same ratio
    uri: /build/assets/img/dummy/320x240.png
    alt: alt text
    sources:
      - srcset: /build/assets/img/dummy/2560x1920.png
        media: "(min-width: 100em)"
        size: xxl
        height: 1920
        width: 2560
      - srcset: /build/assets/img/dummy/1440x1080.png
        media: "(min-width: 80em) and (max-width: 99.9375em)"
        size: xl
        height: 1080
        width: 1440
      - srcset: /build/assets/img/dummy/1024x768.png
        media: "(min-width: 64em) and (max-width: 79.9375em)"
        size: lg
        height: 768
        width: 1024
      - srcset: /build/assets/img/dummy/768x576.png
        media: "(min-width: 48em) and (max-width: 63.9375em)"
        size: md
        height: 576
        width: 768
      - srcset: /build/assets/img/dummy/570x427.png
        media: "(max-width: 47.9375em)"
        size: sm
        height: 427
        width: 570
  - $name: srcset with different ratio
    uri: /build/assets/img/dummy/320x240.png
    alt: alt text
    sources:
      - srcset: /build/assets/img/dummy/2560x1920.png
        media: "(min-width: 100em)"
        size: xxl
        height: 1920
        width: 2560
      - srcset: /build/assets/img/dummy/1440x1080.png
        media: "(min-width: 80em) and (max-width: 99.9375em)"
        size: xl
        height: 1080
        width: 1440
      - srcset: /build/assets/img/dummy/1024x768.png
        media: "(min-width: 64em) and (max-width: 79.9375em)"
        size: lg
        height: 768
        width: 1024
      - srcset: /build/assets/img/dummy/768x576.png
        media: "(min-width: 48em) and (max-width: 63.9375em)"
        size: md
        height: 768
        width: 576
      - srcset: /build/assets/img/dummy/427x570.png
        media: "(max-width: 47.9375em)"
        size: sm
        height: 570
        width: 427
  - $name: srcset with lazy loading
    is_lazy: true
    uri: /build/assets/img/dummy/320x240.png
    alt: alt text
    sources:
      - srcset: /build/assets/img/dummy/2560x1920.png
        media: "(min-width: 100em)"
        size: xxl
        height: 1920
        width: 2560
      - srcset: /build/assets/img/dummy/1440x1080.png
        media: "(min-width: 80em) and (max-width: 99.9375em)"
        size: xl
        height: 1080
        width: 1440
      - srcset: /build/assets/img/dummy/1024x768.png
        media: "(min-width: 64em) and (max-width: 79.9375em)"
        size: lg
        height: 768
        width: 1024
      - srcset: /build/assets/img/dummy/576x768.png
        media: "(min-width: 48em) and (max-width: 63.9375em)"
        size: md
        height: 768
        width: 576
      - srcset: /build/assets/img/dummy/427x570.png
        media: "(max-width: 47.9375em)"
        size: sm
        height: 570
        width: 427
  - $name: 16x9
    uri: /build/assets/img/dummy/16x9.png
    height: 406
    width: 720
    alt: Sample alt text
  - $name: 3x4
    uri: /build/assets/img/dummy/3x4.png
    height: 256
    width: 342
    alt: Sample alt text
  - $name: 16x9_9x16
    uri: /build/assets/img/dummy/1584x891.png
    width: 1584
    height: 891
    sources:
      - srcset: /build/assets/img/dummy/1584x891.png
        media: "(min-width: 100em)"
        size: xxl
        width: 1584
        height: 891
      - srcset: /build/assets/img/dummy/1584x891.png
        media: "(min-width: 80em) and (max-width: 99.9375em)"
        size: xl
        width: 1584
        height: 891
      - srcset: /build/assets/img/dummy/1584x891.png
        media: "(min-width: 64em) and (max-width: 79.9375em)"
        size: lg
        width: 1584
        height: 891
      - srcset: /build/assets/img/dummy/1406x2500.png
        media: "(min-width: 48em) and (max-width: 63.9375em)"
        size: md
        width: 1406
        height: 2500
      - srcset: /build/assets/img/dummy/1406x2500.png
        media: "(max-width: 47.9375em)"
        size: sm
        width: 1406
        height: 2500
  - $name: 2x1_9x16
    uri: /build/assets/img/dummy/768x1365.png
    alt: alt text
    sources:
      - srcset: /build/assets/img/dummy/1440x720.png
        media: "(min-width: 100em)"
        size: xxl
        height: 720
        width: 1440
      - srcset: /build/assets/img/dummy/1440x720.png
        media: "(min-width: 80em) and (max-width: 99.9375em)"
        size: xl
        height: 720
        width: 1440
      - srcset: /build/assets/img/dummy/1024x512.png
        media: "(min-width: 64em) and (max-width: 79.9375em)"
        size: lg
        height: 512
        width: 1024
      - srcset: /build/assets/img/dummy/768x1365.png
        media: "(min-width: 48em) and (max-width: 63.9375em)"
        size: md
        height: 768
        width: 1365
      - srcset: /build/assets/img/dummy/569x1012.png
        media: "(max-width: 47.9375em)"
        size: sm
        height: 1012
        width: 569

  - $name: 9x16
    uri: /build/assets/img/dummy/9x16.webp
    height: 626
    width: 375
    alt: Sample alt text
  - $name: 9x16_picture
    uri: /build/assets/img/dummy/9x16.webp
    alt: ""
    sources:
      - srcset: /build/assets/img/dummy/story-slider/intro-slide.webp
        media: "(min-width: 200em)"
        size: xxl
        height: 626
        width: 375

  - $name: Story slider intro slide
    uri: /build/assets/img/dummy/story-slider/intro-slide.webp
    height: 626
    width: 375
    alt: Sample alt text
  - $name: Story slider picture
    uri: /build/assets/img/dummy/story-slider/intro-slide.webp
    alt: Sample alt text
    picture_classes: ["StorySliderSlideIntro-introImage"]
    sources:
      - srcset: /build/assets/img/dummy/story-slider/intro-slide.webp
        media: "(min-width: 200em)"
        size: xxl
        height: 626
        width: 375
  - $name: Avatar
    uri: /build/assets/img/dummy/avatar/6.webp
    width: 200
    height: 200
  - $name: benefit overview
    uri: /build/assets/img/dummy/566x454.png
    alt: alt text
    width: 566
    height: 454
  - $name: benefit 1
    uri: /build/assets/img/dummy/626x770.png
    alt: alt text
    width: 626
    height: 770
  - $name: benefit 2
    uri: /build/assets/img/dummy/831x709.png
    alt: alt text
    width: 831
    height: 709
  - $name: benefit 3
    uri: /build/assets/img/dummy/589x657.png
    alt: alt text
    width: 589
    height: 657
  - $name: benefit 4
    uri: /build/assets/img/dummy/482x560.png
    alt: alt text
    width: 482
    height: 560
  - $name: benefit 5
    uri: /build/assets/img/dummy/775x775.png
    alt: alt text
    width: 775
    height: 775
  - $name: Mixed story slide
    uri: /build/assets/img/dummy/story-slider/mixed287x216.webp
    height: 216
    width: 287
    alt: Sample alt text
