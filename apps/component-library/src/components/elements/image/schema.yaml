$schema: "http://json-schema.org/draft-07/schema#"
$id: /elements/image
additionalProperties: false
type: object
required:
  - uri
  - width
  - height
  - alt
properties:
  uri:
    type: string
    format: uri-reference
  width:
    type: number
  height:
    type: number
  alt:
    type: string
  picture_classes:
    type: array
    items:
      type: string
  sources:
    type: array
    items:
      type: object
      properties:
        srcset:
          type: string
        media:
          type: string
        size:
          type: string
          enum:
            - xs
            - sm
            - md
            - lg
            - xl
            - xxl
        width:
          type: number
        height:
          type: number
      required:
        - srcset
        - media
        - size
        - width
        - height
  classes:
    type: array
    items:
      type: string
  wrapper_classes:
    type: array
    items:
      type: string
  is_lazy:
    type: boolean
