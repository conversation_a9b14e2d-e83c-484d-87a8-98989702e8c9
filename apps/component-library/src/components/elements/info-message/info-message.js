export class InfoMessage extends HTMLElement {
	// eslint-disable-next-line jsdoc/require-jsdoc
	static get observedAttributes() {
		return ["end-date", "start-date"];
	}

	#state = {
		endDate: null,
		message: "",
		startDate: null,
		tone: "info",
	};

	// eslint-disable-next-line jsdoc/require-jsdoc
	constructor() {
		super();

		this.#state.endDate = new Date(this.getAttribute("end-date")).getTime();
		this.#state.startDate = new Date(this.getAttribute("start-date")).getTime();

		this.#setActiveState();
	}

	/**
	 * Sets the active state of the info message based on the current date and the start and end dates.
	 * @returns {void}
	 * @private
	 */
	#setActiveState() {
		const now = Date.now();
		const isActive = this.#state.startDate <= now && now <= this.#state.endDate;

		this.hidden = !isActive;
	}

	/**
	 * Callback function that is called when an attribute is changed.
	 * @param {string} name - The name of the attribute that was changed.
	 * @param {*} _oldValue - The previous value of the attribute.
	 * @param {*} newValue - The new value of the attribute.
	 */
	attributeChangedCallback(name, _oldValue, newValue) {
		if (name === "end-date") {
			this.#state.endDate = new Date(newValue).getTime();
			this.#setActiveState();
		}

		if (name === "start-date") {
			this.#state.startDate = new Date(newValue).getTime();
			this.#setActiveState();
		}
	}
}

customElements.define("info-message", InfoMessage);
