/** @define LinkCard; weak; */

.LinkCard {
	display: block;
	overflow: hidden;
}

.LinkCard picture {
	block-size: 100%;
	display: block;
	inline-size: 100%;
	overflow: clip;
}

.LinkCard img {
	background-color: var(--shared-color-surface-sandstein-light);
	inline-size: 100%;
	transition: transform 0.2s ease-in-out;
}

.LinkCard:hover img,
.LinkCard:focus img {
	transform: scale(103%);
}

.LinkCard-label {
	display: block;
	margin-block-start: var(--size-12);
	text-decoration: none;
}

@media (width < 48rem) {
	.LinkCard {
		display: block;
	}

	.LinkCard-label {
		word-break: break-word;
	}
}
