{% set size_class %}
	{% if size == 'sm' %}
		u-typo-static-TextS-bold
	{% elseif size == 'lg' %}
		u-typo-static-TextL-bold
	{% else %}
		u-typo-static-TextM-bold
	{% endif %}
{% endset %}

<a
	href="{{ url }}"
	class="Link u-link
		{{ size_class }}
		{%- if icon %}
			Link--icon Link--icon{{ icon.position|default("end")|capitalize }}
		{%- endif -%}
		{%- if tone %} u-link--{{ tone }}{% endif -%}
		{%- if classes %} {{ classes|join(" ") }}{% endif -%}"

	{%- if itemprop %} itemprop="{{ itemprop }}" {% endif -%}
	{%- if target %} target="{{ target }}" {% endif -%}
	{%- if target == "_blank" %} rel="noreferrer noopener"{% endif -%}
	{%- if aria_label %} aria-label="{{ aria_label }}"{%- endif -%}
>
	{{ label }}
	{% if icon %}
		{% include "@elements/icon/icon.twig" with {
			name: icon.name,
			classes: ["Link-icon"],
		} only %}
	{% endif %}
</a>
