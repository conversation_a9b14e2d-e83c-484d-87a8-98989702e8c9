$schema: http://json-schema.org/draft-07/schema
$id: /elements/link

type: object

required:
  - url
  - label

additionalProperties: false

properties:
  aria_label:
    type: string
  url:
    type: string
    format: uri-reference
  label:
    type: string
  target:
    type: string
    enum:
      - _blank
      - _self
      - _parent
      - _top
  classes:
    type: array
    items:
      type: string
  tone:
    type: string
    enum:
      - default
      - neutral
      - success
      - critical
    default: default
  size:
    type: string
    enum:
      - sm
      - md
      - lg
    default: md
  icon:
    type: object
    additionalProperties: false
    required:
      - name
    properties:
      name:
        type: string
      position:
        type: string
        enum:
          - start
          - end
        default: end
  itemprop:
    type: string
