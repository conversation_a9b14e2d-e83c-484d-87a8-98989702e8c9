.Notecard:not([hidden]) {
	align-items: start;
	background-color: var(
		--Notecard-background-color,
		var(--shared-color-surface-primary)
	);
	display: flex;
	gap: var(--size-16);
	padding: var(--size-16);
}

.Notecard-icon {
	flex: 0 0 var(--size-24);
}

.Notecard-error,
.Notecard-info,
.Notecard-note,
.Notecard-success {
	color: var(--shared-color-text-invert);
}

.Notecard-info {
	--Notecard-background-color: var(--shared-color-surface-schlern);
}

.Notecard-note {
	--Notecard-background-color: var(--shared-color-surface-latsche);
}

.Notecard-success {
	--Notecard-background-color: var(--shared-color-surface-success);
}

.Notecard-warning {
	--Notecard-background-color: var(--shared-color-surface-jakobskraut);
}

.Notecard-error {
	--Notecard-background-color: var(--shared-color-surface-critical);
}
