$schema: http://json-schema.org/draft-07/schema
$id: /elements/notecard

type: object

required:
  - text
  - type

additionalProperties: false

properties:
  aria_live:
    type: string
    enum:
      - assertive
      - polite
  hidden:
    type: boolean
  text:
    type: string
  type:
    type: string
    enum:
      - info
      - note
      - success
      - warning
      - error
  classes:
    type: array
    items:
      type: string
