# Option Card Component

A flexible card component for displaying selectable options with radio buttons or checkboxes. Supports different visual styles and can include media assets like icons or images.

## Usage

Include the option card component in your Twig template:

```twig
{% include "@elements/option-card/option-card.twig" with {
    input: {
        type: "radio",
        name: "contact-type",
        checked: false
    },
    label: "Showroom appointment",
    text: "Individual expert advice in our showroom from Finstral experts",
    card_type: "rich",
    asset: icon_html,
    as_list: true
} only %}
```

## Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `input` | object | ✅ | - | Input configuration object |
| `input.type` | string | ✅ | - | Input type: `"radio"` or `"checkbox"` |
| `input.name` | string | ✅ | - | Input name attribute for form grouping |
| `input.checked` | boolean | ❌ | `false` | Whether the input should be pre-selected |
| `label` | string | ✅ | - | The option label text |
| `text` | string | ❌ | - | Help text displayed below the label |
| `card_type` | string | ❌ | `"inline"` | Visual style: `"rich"`, `"basic"`, or `"inline"` |
| `asset` | string | ❌ | - | HTML content for media (icon or image) |
| `as_list` | boolean | ❌ | `true` | Whether to render as `<li>` element (for use in lists) |

## Card Types

### Inline (default)
- Horizontal layout with label and optional media side by side
- Compact design suitable for simple selections

### Basic
- Vertical layout with media above content
- Good for options with descriptive text

### Rich
- Vertical layout with media above content
- Enhanced styling for prominent option displays

## Examples

### Basic Radio Button
```twig
{% include "@elements/option-card/option-card.twig" with {
    input: {
        type: "radio",
        name: "preferred-day"
    },
    label: "Monday Afternoon"
} only %}
```

### Pre-selected Checkbox
```twig
{% include "@elements/option-card/option-card.twig" with {
    input: {
        type: "checkbox",
        name: "additional-services",
        checked: true
    },
    label: "Monday Afternoon",
    text: "Check the condition of your windows with four simple tests.",
    card_type: "basic"
} only %}
```

### Rich Card with Icon
```twig
{% set home_icon %}
{% include "@elements/icon/icon.twig" with {
    name: "home",
    size: "xxlarge",
    sprite_sheet: "landing-pages"
} only %}
{% endset %}

{% include "@elements/option-card/option-card.twig" with {
    input: {
        type: "radio",
        name: "contact-type"
    },
    label: "On-site consultation",
    text: "We'll visit you at your location",
    card_type: "rich",
    asset: home_icon
} only %}
```

### Rich Card with Image
```twig
{% set image %}
{% include "@elements/image/image.twig" with {
    uri: "/build/assets/img/dummy/570x427.png",
    height: 96,
    width: 96,
    alt: ""
} only %}
{% endset %}

{% include "@elements/option-card/option-card.twig" with {
    input: {
        type: "radio",
        name: "appointment-type"
    },
    label: "Showroom appointment",
    text: "Individual expert advice in our showroom from Finstral experts",
    card_type: "rich",
    asset: image
} only %}
```

### Standalone Card (not in list)
```twig
{% include "@elements/option-card/option-card.twig" with {
    input: {
        type: "checkbox",
        name: "standalone-option"
    },
    label: "Monday Afternoon",
    as_list: false
} only %}
```

### In a Form Fieldset
```twig
<fieldset class="Fieldset">
    <legend>Choose your preferred contact method</legend>
    <ul class="Form-group">
        {% for option in contact_options %}
            {% include "@elements/option-card/option-card.twig" with {
                input: {
                    type: "radio",
                    name: "contact-type"
                },
                label: option.label,
                text: option.help_text,
                card_type: "rich",
                asset: option.icon
            } only %}
        {% endfor %}
    </ul>
</fieldset>
```

## Styling

- Cards have a border that becomes more prominent when selected
- Responsive design with different layouts for mobile and desktop
- Rich and basic cards stack vertically on desktop (media above content)
- Inline cards maintain horizontal layout on all screen sizes

## Accessibility

- Proper form structure with labels wrapping inputs
- Unique IDs generated from label text
- Screen reader friendly with proper labeling

## Notes

- The component automatically generates IDs from the label text (lowercase, spaces replaced with hyphens)
- When `as_list` is true, the component renders as an `<li>` element for proper list semantics
- The `asset` parameter accepts any HTML content, typically using the `icon` or `image` component.