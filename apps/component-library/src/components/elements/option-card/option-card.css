/* @define OptionCard; */
.OptionCard {
	align-items: center;
	border: var(--border-distinct);
	display: flex;
	gap: var(--OptionCard-gap);
	justify-content: var(--OptionCard-justify-content, space-between);
	padding: var(--OptionCard-padding);

	&:has(:checked) {
		border: var(--border-full);
		outline: var(--focus-outline-sm);
	}
}

.OptionCard-label {
	align-items: center;
	display: flex;
	gap: var(--size-12);

	/* Because the label wraps the input, we remove the current
		`line-height` to ensure the input is aligned with the text */
	line-height: 0;
}

.OptionCard-helpText {
	color: var(--shared-color-text-secondary);
	margin-block-start: var(--size-8);
}

.OptionCard-media {
	flex-shrink: 0;
}

/* mobile */
@media (width < 48rem) {
	.OptionCard {
		--OptionCard-gap: var(--size-16);
		--OptionCard-padding: var(--size-16);
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.OptionCard {
		--OptionCard-gap: var(--size-20);
		--OptionCard-padding: var(--size-32) var(--size-24);
	}

	.OptionCard--rich,
	.OptionCard--basic {
		flex-direction: column-reverse;
		--OptionCard-justify-content: center;

		.OptionCard-main {
			display: grid;
			place-items: center;
		}

		.OptionCard-helpText {
			text-align: center;
		}
	}
}
