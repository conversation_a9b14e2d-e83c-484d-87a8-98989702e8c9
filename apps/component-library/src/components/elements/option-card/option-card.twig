{% set card_type = card_type == "rich" ? "OptionCard--rich" : (card_type == "basic" ? "OptionCard--basic" : "") %}
{% set input_id = input.id ?: label|lower|replace({' ': '-'}) %}


{% set card_body %}
<div class="OptionCard-main">
	<label class="OptionCard-label {{ input.type|capitalize }} Option-next u-typo-TextM" for="{{ input_id }}">
		<input type="{{ input.type }}" id="{{ input_id }}" name="{{ input.name }}" {% if input.value %} value="{{ input.value }}" {% endif %} {% if input.checked %} checked {% endif %}>
		{{ label }}
	</label>
	{% if text %}
		<p class="OptionCard-helpText u-typo-TextS">{{ text }}</p>
	{% endif %}
</div>
{% if asset %}
	<div class="OptionCard-media">
		{{ asset }}
	</div>
{% endif %}
{% endset %}

{% if as_list|default(true) %}
	<li class="OptionCard {{ card_type }}">{{ card_body }}</li>
{% else %}
	<div class="OptionCard {{ card_type }}">{{ card_body }}</div>
{% endif %}
