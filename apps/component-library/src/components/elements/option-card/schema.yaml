$schema: http://json-schema.org/draft-07/schema
$id: /elements/option-card

type: object

required:
  - input
  - label

additionalProperties: false

properties:
  as_list:
    type: boolean
    default: true

  card_type:
    type: string
    enum:
      - rich
      - basic
      - inline
    default: inline

  asset:
    type: string
    format: html
    description: elements/image or elements/icon

  input:
    type: object
    required:
      - name
      - type
    additionalProperties: false
    properties:
      checked:
        type: boolean
        default: false
      name:
        type: string
      type:
        type: string
        enum:
          - radio
          - checkbox
      id:
        type: string
      value:
        type: string
        description: Set a custom value over the default of "on", "off", "undefined" for checkboxes and radio buttons

  label:
    type: string

  text:
    type: string
