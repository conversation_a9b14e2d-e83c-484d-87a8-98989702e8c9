/** @define PageTitle; */
.PageTitle {
	display: grid;
	gap: var(--size-8);
}

@media (width < 48rem) {
	.PageTitle {
		grid-template-columns: 1fr;
		grid-template-rows: repeat(3, auto);
	}
}

@media (width >= 48rem) {
	.PageTitle {
		grid-template-columns: auto 1fr;
		grid-template-rows: repeat(2, auto);
	}
}

.PageTitle-title {
	grid-column: 1/2;
	grid-row: 1/2;
}

.PageTitle-subText {
	grid-row: 2/3;
}

@media (width < 48rem) {
	.PageTitle-subText {
		grid-column: 1/2;
	}
}

@media (width >= 48rem) {
	.PageTitle-subText {
		grid-column: 1/3;
	}
}

@media (width < 48rem) {
	.PageTitle-helpText {
		grid-column: 1/2;
		grid-row: 3/4;
		margin-block-start: var(--size-32);
	}
}

@media (width >= 48rem) {
	.PageTitle-helpText {
		grid-column: 2/3;
		grid-row: 1/2;
		justify-self: end;
		margin-block-start: initial;
	}
}
