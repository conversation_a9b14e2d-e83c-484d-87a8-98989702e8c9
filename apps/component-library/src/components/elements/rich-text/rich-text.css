.RichText {
	container: rich-text / inline-size;
}

.RichText th,
.RichText td {
	border: 1px solid var(--shared-color-border-disabled);
	padding: var(--size-4) var(--size-8);
	vertical-align: top;
}

@media (width < 48em) {
	.RichText th,
	.RichText td {
		hyphens: auto;
		word-wrap: break-word;
	}
}

.RichText :is(h1, h2, h3, h4, h5, h6) {
	text-wrap: balance;
}

/* spacings */

/* HEADING + HEADING */

.RichText h1 + h2,
.RichText h2 + h3,
.RichText h3 + h4 {
	margin-block-start: var(--size-16);
}

.RichText h2 + h5 {
	margin-block-start: var(--size-16);
}

/* PARAGRAPH + PARAGRAPH */

.RichText
	:is(p, ul, ol, pre, table, blockquote, img, iframe)
	+ :is(p, ul, ol, pre, table, blockquote, img, iframe) {
	margin-block-start: var(--size-16);
}

/* PARAGRAPH + MEDIA */

.RichText :is(p, ul, ol, pre, table, blockquote, img, iframe) + .Picture {
	margin-block-start: var(--size-16);
}

/* H1 + PARAGRAPH */

.RichText h1 + :is(p, ul, ol, pre, table, blockquote, img, iframe) {
	margin-block-start: var(--size-24);
}

/* H2 + PARAGRAPH */

.RichText h2 + :is(p, ul, ol, pre, table, blockquote, img, iframe) {
	margin-block-start: var(--size-24);
}

/* H3 + PARAGRAPH */

.RichText h3 + :is(p, ul, ol, pre, table, blockquote, img, iframe) {
	margin-block-start: var(--size-24);
}

/* H4 + PARAGRAPH */

.RichText h4 + :is(p, ul, ol, pre, table, blockquote, img, iframe) {
	margin-block-start: var(--size-24);
}

/* H5 + PARAGRAPH */

.RichText h5 + :is(p, ul, ol, pre, table, blockquote, img, iframe) {
	margin-block-start: var(--size-24);
}

@media (48em > width) {
	.RichText h5 + :is(p, ul, ol, pre, table, blockquote, img, iframe) {
		margin-block-start: var(--size-16);
	}
}

/* PARAGRAPH + H1 */

.RichText :is(p, ul, ol, pre, table, blockquote, img, iframe) + h1 {
	margin-block-start: var(--size-40);
}

/* PARAGRAPH + H2 */

.RichText :is(p, ul, ol, pre, table, blockquote, img, iframe) + h2 {
	margin-block-start: var(--size-40);
}

/* PARAGRAPH + H3 */

.RichText :is(p, ul, ol, pre, table, blockquote, img, iframe) + h3 {
	margin-block-start: var(--size-40);
}

/* PARAGRAPH + H4 */

.RichText :is(p, ul, ol, pre, table, blockquote, img, iframe) + h4 {
	margin-block-start: var(--size-40);
}

/* PARAGRAPH + H5 */

.RichText :is(p, ul, ol, pre, table, blockquote, img, iframe) + h5 {
	margin-block-start: var(--size-40);
}

@media (48em > width) {
	.RichText :is(p, ul, ol, pre, table, blockquote, img, iframe) + h5 {
		margin-block-start: var(--size-32);
	}
}

.RichText li + li,
.RichText :is(ul, ol) :is(ul, ol) li:first-child {
	margin-block-start: var(--size-16);
}

.RichText :is(p, ol, li) + :is(p, ol, li) {
	margin-block-start: var(--size-16);
}

@container (inline-size < 48em) {
	.RichText h1,
	.RichText h2,
	.RichText h3 {
		hyphens: auto;
		word-break: break-word;
	}
}

.RichText :is(pre, pre code, iframe, img, table) {
	inline-size: 100%;
}

/* Codeblock */

.RichText pre {
	align-items: center;
	display: flex;
	margin: 0;
	white-space: pre-wrap;
	word-wrap: break-word;
}

.RichText pre code {
	display: block;
}

.RichText pre,
.RichText code:not(:is(pre > code)) {
	background-color: var(--shared-color-surface-subtle-lighter);
	display: inline-flex;
}

.RichText pre {
	padding-block: var(--size-8);
	padding-inline: var(--size-12);
}

.RichText code:not(:is(pre > code)) {
	padding-inline: var(--size-8);
}

/* Lists */

.RichText :is(ol, ul) {
	list-style: revert;
	padding-inline-start: 1.2em;
}

.RichText ul > li {
	list-style-type: square;
	padding-inline-start: 0.35em;
}

.RichText li + li {
	margin-block-start: var(--size-16);
}

/* Other tags */

.RichText :is(strong, b) {
	font-weight: 500;
}

.RichText :is(em, i) {
	font-style: italic;
}

.RichText :is(sub, sup) {
	all: revert;
	line-height: 1;
}

.RichText blockquote {
	border-inline-start: 0.4rem solid var(--shared-color-border-brand);
	padding-inline-start: 1em;
}
