id: select-default-id
options:
  - type: option
    value: a
    label: Value a
  - type: option
    value: b
    label: Value b
$variants:
  - $name: With Placeholder
    required: true
    $opts:
      options: overwrite
    options:
      - type: option
        label: Choose an option
      - type: option
        value: a
        label: Value a
      - type: option
        value: b
        label: Value b
  - $name: Invalid
    invalid: true
    id: select-invalid
  - $name: Disabled
    disabled: true
    id: select-disabled
  - $name: Invalid and disabled
    disabled: true
    invalid: true
    id: select-invalid-and-disabled
  - $name: With optgroups
    id: select-optgroups
    options:
      - type: optgroup
        label: Group a
        options:
          - type: option
            value: a1
            label: Value a1
          - type: option
            value: a2
            label: Value a2
      - type: optgroup
        label: Group b
        options:
          - type: option
            value: b1
            label: Value b1
          - type: option
            value: b2
            label: Value b2
