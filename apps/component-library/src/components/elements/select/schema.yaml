$schema: http://json-schema.org/draft-07/schema#
$id: /elements/select
additionalProperties: false
required:
  - options
properties:
  attributes:
    type: string
  classes:
    type: array
    items:
      type: string
  disabled:
    type: boolean
  id:
    type: string
  invalid:
    type: boolean
  name:
    type: string
  required:
    type: boolean
  options:
    type: array
    items:
      type: object
      properties:
        type:
          type: string
          enum:
            - option
            - optgroup
