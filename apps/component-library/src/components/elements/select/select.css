/** @define Input; */

.Input--select {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24'%3E%3Cpath d='M12 15.038 6.345 9.384 7.4 8.331l4.6 4.6 4.6-4.6 1.053 1.053z'/%3E%3C/svg%3E");
	background-position: right var(--size-8) center;
	background-repeat: no-repeat;
	background-size: var(--size-24);
	padding-inline-end: var(--size-40);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
[dir="rtl"] .Input--select {
	background-position-x: var(--size-8);
}

/* To style "fake" placeholder */
.Input--select:not(.is-touched):required:invalid {
	color: var(--shared-color-text-secondary);
}
