{% apply spaceless %}
	<select
		{{ attributes }}
		class="Input Input--select {{ classes|join(" ") }}"
		{% if disabled %} disabled{% endif %}
		{% if required %} required{% endif %}
		{% if invalid %}aria-invalid="true"{% endif %}
		{% if id %}id="{{ id }}"{% endif %}
		{% if name %}name="{{ name }}"{% endif %}
	>
		{% for option in options %}
			{% set option_type = option.type|default("option") %}
			{% if option_type == 'optgroup' %}
				<optgroup label="{{ option.label }}">
					{% for sub_option in option.options %}
						<option value="{{ sub_option.value }}"{{ sub_option.selected ? ' selected' : '' }}>{{ sub_option.label }}</option>
					{% endfor %}
				</optgroup>
			{% elseif option_type == 'option' %}
				<option value="{{ option.value }}"{{ option.selected ? ' selected' : '' }}>{{ option.label }}</option>
			{% endif %}
		{% endfor %}
	</select>
{% endapply %}
