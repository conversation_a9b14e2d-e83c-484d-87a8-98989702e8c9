class SkeletonLoader extends HTMLElement {
	static observedOttributes = ["skeleton-count"];

	#skeletonCount = 10;

	/**
	 * Creates an instance of SkeletonLoader and initializes the skeleton count.
	 */
	constructor() {
		super();

		this.#skeletonCount = this.getAttribute("skeleton-count") || 10;
	}

	/**
	 * Returns the HTML string for a single skeleton placeholder item.
	 * @returns {string} The HTML for a skeleton placeholder.
	 */
	#getPlaceholderItem() {
		return `
    <div class="container">
        <div class="SkeletonLoader-boxDefault SkeletonLoader-shimmerLoadingPlaceholder"></div>
        <div class="SkeletonLoader-boxThin SkeletonLoader-shimmerLoadingPlaceholder"></div>
    </div>
  `;
	}

	/**
	 * Returns an array of skeleton placeholder HTML strings based on the skeleton count.
	 * @returns {string[]} Array of skeleton placeholder HTML strings.
	 */
	#getPlaceholderItems() {
		return Array.from({ length: this.#skeletonCount }, () =>
			this.#getPlaceholderItem(),
		);
	}

	/**
	 * Renders the skeleton placeholder items inside the component.
	 * @private
	 */
	#render() {
		this.innerHTML = this.#getPlaceholderItems().join("");
	}

	/**
	 * Called when an observed attribute has changed.
	 * @param {string} name - The name of the attribute that changed.
	 * @param {*} _oldValue - The old value of the attribute.
	 * @param {*} newValue - The new value of the attribute.
	 */
	attributeChangedCallback(name, _oldValue, newValue) {
		if (name === "skeleton-count") {
			this.#skeletonCount = newValue;
			this.#render();
		}
	}

	/**
	 * Called when the element is inserted into the DOM.
	 * @private
	 */
	connectedCallback() {
		this.#render();
	}
}

customElements.define("skeleton-loader", SkeletonLoader);
