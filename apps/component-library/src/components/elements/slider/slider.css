/** @define <PERSON>lider; */

.Slider {
	container-name: slider;
	container-type: inline-size;
	display: block;

	/**
	 * If the slider is placed in flex or grid layouts,
	 * it needs to be able to shrink below its intrinsic size.
	 * Otherwise it would be as wide as its children and overflow.
	 */
	min-inline-size: 0;

	position: relative;
}

/* Navigation */

.Slider-navigation {
	display: flex;
	gap: var(--size-4);
	inset-block: var(--slider-navigation-inset-block);
	inset-inline: var(--slider-navigation-inset-inline);
	position: absolute;
}

/**
 * Hide scrollbar on hover capable devices.
 * The "hover: none" media feature is a best-guess proxy for "touch".
 */
@media (hover: none) and (width < 48em) {
	.Slider-navigation {
		display: none;
	}
}

.Slider-navigationButton {
	background: var(--shared-color-surface-subtle);
	border: var(--shared-border-width-md) solid var(--shared-color-border-invert);
	color: var(--shared-color-text-invert);
	padding: var(--size-12);
	transition-duration: var(--default-transition-duration);
	transition-property: background-color, opacity;
}

.Slider-navigationButton:not(:disabled):hover {
	background: var(--prim-color-neutral-500);
}

.Slider-navigationButton:disabled {
	opacity: 0.6;
}

.Slider-navigationButton[hidden] {
	opacity: 0;
	visibility: hidden;
}

/* Track */

.Slider-track {
	display: flex;
	gap: var(--slider-track-gap);
	overflow-x: auto;

	/* Prevent accidental history navigation when swiping. */
	overscroll-behavior-x: contain;

	padding-inline: var(--slider-track-padding-inline);
	scroll-snap-type: x mandatory;
}

.Slider-track:focus-visible {
	/**
	 * The track is focusable so keyboard users can scroll with arrow keys.
	 * The focus ring would be cut off by the overflow setting,
	 * so it needs to be pulled in slightly.
	 */
	outline-offset: -0.125rem;
}

@media not (prefers-reduced-motion) {
	.Slider-track {
		scroll-behavior: smooth;
	}
}

/**
 * Hide scrollbar on hover capable devices.
 * The "hover: hover" media feature is a best-guess proxy for "desktop".
 */
@media (hover: hover) {
	.Slider-track {
		scrollbar-width: none;
	}
}

/* Slide */

.Slider-slide {
	scroll-snap-align: center;
}

.Slider-slide > :only-child {
	inline-size: var(--slider-slide-inline-size);
	max-inline-size: 100cqi;
}
