class Slider extends HTMLElement {
	static #selectors = {
		container: ".js-Slider",
		buttonPrevious: ".js-Slider-navigationButton--previous",
		buttonNext: ".js-Slider-navigationButton--next",
		track: ".js-Slider-track",
		slide: ".js-Slider-slide",
	};

	#elements;

	/**
	 * Depending on resolution and scaling factor,
	 * the track size and position might not perfectly align.
	 * A small rounding offset ensures buttons are always correctly disabled.
	 */
	#roundingOffset = 2;

	/**
	 * Create new slider
	 */
	constructor() {
		super();

		this.#elements = {
			buttonPrevious: /** @type {HTMLButtonElement} */ (
				this.querySelector(Slider.#selectors.buttonPrevious)
			),
			buttonNext: /** @type {HTMLButtonElement} */ (
				this.querySelector(Slider.#selectors.buttonNext)
			),
			track: /** @type {HTMLDivElement} */ (
				this.querySelector(Slider.#selectors.track)
			),
			slides: /** @type {NodeList<HTMLDivElement>} */ (
				this.querySelectorAll(Slider.#selectors.slide)
			),
		};

		this.#addEventListeners();
	}

	/**
	 * Add event listeners
	 */
	#addEventListeners() {
		this.#elements.buttonPrevious.addEventListener(
			"click",
			this.#showPreviousSlide.bind(this),
		);
		this.#elements.buttonNext.addEventListener(
			"click",
			this.#showNextSlide.bind(this),
		);

		this.#elements.track.addEventListener("scroll", this.#onScroll.bind(this));

		/**
		 * Resize observers are triggerd once on observe start, so the initial
		 * button state and visibility doesn't need to be set in the constructor.
		 */
		const resizeObserver = new ResizeObserver(() => this.#onResize());
		resizeObserver.observe(this.#elements.track);
	}

	/**
	 * Handle scroll event
	 */
	#onScroll() {
		requestAnimationFrame(() => this.#setButtonState());
	}

	/**
	 * Handle resize event
	 */
	#onResize() {
		requestAnimationFrame(() => {
			this.#setButtonState();
			this.#setButtonVisibility();
		});
	}

	/**
	 * Show previous slide
	 */
	#showPreviousSlide() {
		this.#elements.track.scrollLeft -= this.#slideSize;
	}

	/**
	 * Show next slide
	 */
	#showNextSlide() {
		this.#elements.track.scrollLeft += this.#slideSize;
	}

	/**
	 * Set button state
	 */
	#setButtonState() {
		if (this.#trackVisibleSize === this.#trackTotalSize) {
			this.#elements.buttonPrevious.toggleAttribute("disabled", true);
			this.#elements.buttonNext.toggleAttribute("disabled", true);
			return;
		}

		if (this.#trackPosition - this.#roundingOffset <= 0) {
			this.#elements.buttonPrevious.toggleAttribute("disabled", true);
			this.#elements.buttonNext.toggleAttribute("disabled", false);
			return;
		}

		if (
			this.#trackPosition + this.#trackVisibleSize + this.#roundingOffset >=
			this.#trackTotalSize
		) {
			this.#elements.buttonPrevious.toggleAttribute("disabled", false);
			this.#elements.buttonNext.toggleAttribute("disabled", true);
			return;
		}

		this.#elements.buttonPrevious.toggleAttribute("disabled", false);
		this.#elements.buttonNext.toggleAttribute("disabled", false);
	}

	/**
	 * Set button visibility
	 */
	#setButtonVisibility() {
		const isVisible =
			this.#trackVisibleSize + this.#roundingOffset < this.#trackTotalSize;

		this.#elements.buttonPrevious.toggleAttribute("hidden", !isVisible);
		this.#elements.buttonNext.toggleAttribute("hidden", !isVisible);
	}

	/**
	 * @returns {number}
	 */
	get #trackTotalSize() {
		return this.#elements.track.scrollWidth;
	}

	/**
	 * @returns {number}
	 */
	get #trackVisibleSize() {
		return this.#elements.track.offsetWidth;
	}

	/**
	 * @returns {number}
	 */
	get #trackPosition() {
		return this.#elements.track.scrollLeft;
	}

	/**
	 * @returns {number}
	 */
	get #slideSize() {
		return this.#elements.slides[0].offsetWidth;
	}
}

customElements.define("finstral-slider", Slider);
