{% set id = "slider-" ~ random %}
{% set label_id = id ~ "-heading" %}
{% set track_id = id ~ "-track" %}

<finstral-slider
	class="Slider js-Slider"
	role="group"
	aria-roledescription="slider"
	aria-brailleroledescription="sldr"
	aria-labelledby="{{ label_id }}"
>
	<span id="{{ label_id }}" hidden>{{ label }}</span>

	<div class="Slider-navigation">
		<button
			class="Slider-navigationButton Slider-navigationButton--previous js-Slider-navigationButton--previous"
			type="button"
			aria-label="{{ "slider.previous"|tc }}"
			aria-controls="{{ track_id }}"
			disabled
		>
			{% include "@elements/icon/icon.twig" with {name: 'chevron_left'} only %}
		</button>

		<button
			class="Slider-navigationButton Slider-navigationButton--next js-Slider-navigationButton--next"
			type="button"
			aria-label="{{ "slider.next"|tc }}"
			aria-controls="{{ track_id }}"
			disabled
		>
			{% include "@elements/icon/icon.twig" with {name: 'chevron_right'} only %}
		</button>
	</div>

	<div
		class="Slider-track js-Slider-track"
		id="{{ track_id }}"
		tabindex="0"
	>
		{% for slide in slides %}
			<div
				class="Slider-slide js-Slider-slide"
				role="group"
				aria-roledescription="slide"
				aria-brailleroledescription="sld"
				aria-label="{{ "slider.slideNumber"|tc({'@current': loop.index, '@total': loop.length}) }}"
			>
				{{ slide }}
			</div>
		{% endfor %}
	</div>
</finstral-slider>
