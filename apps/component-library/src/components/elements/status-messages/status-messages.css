/** @define StatusMessages; */

.StatusMessages {
	margin-block-end: var(--size-32);
}

.StatusMessages-container {
	padding: var(--size-24);
}

.StatusMessages-heading {
	font-weight: 500;
}

.StatusMessages-container + .StatusMessages-container {
	margin-block-start: 1rem;
}

.StatusMessages-container--error {
	background: var(--c-decoration-error);
	color: var(--c-typo-on-error);
}

.StatusMessages-container--success {
	background: var(--c-decoration-success);
	color: var(--c-typo-on-success);
}

.StatusMessages-container--info,
.StatusMessages-container--status {
	background: var(--c-decoration-bg-light);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
:is(.StatusMessages-container--error, .StatusMessages-container--success)
	.StatusMessages-messages
	li::marker {
	color: currentColor !important;
}
