<div class="StatusMessages">
	{% for entry in list %}
		<div class="StatusMessages-container StatusMessages-container--{{ entry.type }}"{% if entry.type == "error" %} role="alert"{% endif %}>
				<div class="StatusMessages-messages">
					{% set content %}
						{% if headings[entry.type] %}
							<p class="StatusMessages-heading">
								{{ headings[entry.type] }}
							</p>
						{% endif %}
						{% if entry.messages|length > 1 %}
							<ul>
								{% for message in entry.messages %}
									<li>
										<p>{{ message|raw }}</p>
									</li>
								{% endfor %}
							</ul>
						{% else %}
							<p>{{ entry.messages|first|raw }}</p>
						{% endif %}
					{% endset %}
					{% include "@elements/rich-text/rich-text.twig" with {
						content: content
					} only %}
				</div>
		</div>
	{% endfor %}
</div>
