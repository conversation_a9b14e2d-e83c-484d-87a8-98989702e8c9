/** @define Table; weak */

.Table {
	display: block;
	overflow-x: auto;

	th,
	td {
		border-block-end: var(--border-subtle);
		padding-inline: var(--size-8);
	}

	:is(th, td):first-child {
		padding-inline-start: 0;
	}

	:is(th, td):last-child {
		padding-inline-end: 0;
	}

	th {
		color: var(--shared-color-text-secondary);
		padding-block-end: var(--size-12);
		padding-block-start: var(--size-8);
	}

	td {
		padding-block: var(--size-16);
	}
}
