The toast component is used to provide short and succint feedback to a user after they have completed an interaction or an asynchronous task completed.

## The default Toast

The default Toast will:

- Use the default style
- Be light dismissable (click outside)
- Be dismissable by pressing the escape key
- Have an ARIA `role` of `status` which maps to an `aria-live` state of polite.

## An alert Toast

When the Toast's ARIA `role` is set to `alert`, the Toast will:

- Use the default style unless a `toast_style` of `critical` or `success` was set.
- Will not be light dismissable
- Be dismissable by pressing the escape key
- Be dismissable by pressing the close button
- Have an ARIA `role` of `alert` which maps to an `aria-live` state of assertive.

## A timer-based Toast

A timer based Toast is enabled by setting the `dismissable-timer` value of the `dismissable_by` property. You can also set a `timer_duration`. If no `timer_duration` is provided the Toast will use a default of five seconds.

A timer based Toast will:

- Use the default style unless a `toast_style` of `critical` or `success` was set. (Note: One should generally avoid combining `critical` with a timer only toast as a user might miss the message. For Toast messages which are critical it is best to use the alert style Toast.)
- Will not be light dismissable
- Will not be dismissable by pressing the escape key
- Will not be dismissable by pressing the close button
- Have an ARIA `role` of `status` which maps to an `aria-live` state of polite.

You can further customize how a Toast can be dismissed if the defined Toast types above does not fit your needs.

- To allow a Toast to be light dismissed, set the `dismissable-light` value of the `dismissable_by` property.
- To allow a Toast to be dismissed by the Escape key and a close button, set the `dismissable-user` value of the `dismissable_by` property.

The above can be combined with or without the `dismissable-timer` value of the `dismissable_by` property.
