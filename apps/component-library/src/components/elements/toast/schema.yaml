$schema: http://json-schema.org/draft-07/schema
$id: elements/toast

type: object

required:
  - id

additionalProperties: false

properties:
  dismissible_by:
    type: array
    items:
      type: string
      enum:
        - dismissable-light
        - dismissable-timer
        - dismissable-user
  id:
    type: string
  timer_duration:
    type: number
  toast_style:
    type: string
    enum:
      - critical
      - default
      - success
  toast_role:
    type: string
    enum:
      - alert
      - status
