/* stylelint-disable plugin/selector-bem-pattern */

/** @define Toast; */

.Toast:popover-open {
	border: none;
	display: flex;
	gap: var(--size-16);
	inset: unset;
	margin: unset;
	position: fixed;

	a {
		color: var(--shared-color-text-invert);
	}

	a:hover,
	a:focus {
		text-decoration: none;
	}

	@media (width < 48rem) {
		inline-size: calc(100% - 2 * var(--size-16));
		inset-block-end: var(--size-16);
		inset-inline: var(--size-16);
		justify-content: space-between;
		padding: var(--size-16);
	}

	@media (width >= 48rem) {
		inset-block-end: var(--size-24);
		inset-inline-end: var(--size-24);
		max-inline-size: 22.5rem;
		padding: var(--size-24);
	}

	/* needed to better align the text and the icon optically */
	output {
		line-height: 1.5;
	}
}

.Toast--default,
.Toast--critical,
.Toast--success {
	color: var(--shared-color-text-invert);
}

.Toast--default {
	background: var(--shared-color-surface-invert);
}

.Toast--critical {
	background: var(--shared-color-surface-critical);
}

.Toast--default a:link,
.Toast--critical a:link {
	color: var(--shared-color-text-invert);
}

.Toast--success {
	background: var(--shared-color-surface-success);
}

.Toast-close {
	align-self: start;
}
