export default class Toast extends HTMLElement {
	// eslint-disable-next-line jsdoc/require-jsdoc
	static get observedAttributes() {
		return ["message", "toast-role"];
	}

	static #selectors = {
		toastMessageContainer: "output",
		toastCloseButton: "button",
	};

	#elements;

	#state = {
		currentStyleClass: "Toast--default",
		popoverState: "closed",
	};

	/**
	 * This is a public method that can be called by a user and will show the
	 * toast popover using the options set declaritively on the component.
	 * @example
	 * ```js
	 * const toast = document.querySelector("#finstral-toast");
	 * toast.showToast("message");
	 * ```
	 * @param {string} message - If a message is not declaritively set on the custom element,
	 * a user can pass the message as part of the function call.
	 * @param {boolean} _dangerouslySetInnerHTML - If true, the message will be set as HTML.
	 * NOTE: This is dangerous and should be used with caution when accepting user generated input.
	 * @public
	 * @returns {void}
	 */
	showToast = (message, _dangerouslySetInnerHTML = false) => {
		if (_dangerouslySetInnerHTML) {
			this.#elements.toastMessageContainer.innerHTML = message;
		} else {
			this.#elements.toastMessageContainer.textContent = message;
		}

		this.showPopover();
	};

	/**
	 * This is a public method that can be called by a user and will close the
	 * toast popover. The function will first check to ensure that the toast
	 * popover is open before attempting to close it.
	 * ## Example
	 *
	 * ```js
	 * const toast = document.querySelector("#finstral-toast");
	 * toast.closeToast();
	 * ```
	 * @public
	 * @returns {void}
	 */
	closeToast = () => {
		const defaultToastClasses = "Toast Toast--default";
		const isClone = this.getAttribute("cloned");
		if (isClone && this.#state.popoverState === "open") {
			this.hidePopover();
			this.remove();
		} else if (this.#state.popoverState === "open") {
			// We should prefer always using a template and cloning the
			// toast as this will ensure we start with a toast in its
			// default state. For now we still have instances where
			// this is not the case. What could happen though is
			// that a user encounters an error (critical toast
			// style), resubmits the form without error, but
			// the error style is still used. When closing
			// the toast, also clear out the style class.
			this.className = defaultToastClasses;
			this.hidePopover();
		}
	};

	/**
	 * This is a public method that can be called by a user and will set a custom
	 * style class on the toast popover by replacing the existing style class.
	 * ## Example
	 *
	 * ```js
	 * const toast = document.querySelector("#finstral-toast");
	 * toast.setToastStyle("success");
	 * ```
	 *
	 * Because this returns an instance of the Toast component, you can chain
	 * this method with the showToast method.
	 * ## Example
	 *
	 * ```js
	 * const toast = document.querySelector("#finstral-toast");
	 * toast.setToastStyle("success").showToast("This is a success message");
	 * ```
	 * @param {string} style - The style class to be set on the toast popover. One of `default`, `critical`, `success`.
	 * @public
	 * @returns {Toast} The instance of the Toast class.
	 */
	setToastStyle = (style) => {
		this.classList.replace(this.#state.currentStyleClass, `Toast--${style}`);
		this.#state.currentStyleClass = `Toast--${style}`;
		return this;
	};

	/**
	 * When cloning the toast component, the event listeners are not cloned.
	 * As such, this method can be called to reattach the event listeners.
	 * @public
	 * @returns {void}
	 */
	reattachEventListeners = () => {
		this.#addEventListeners();
	};

	#options = {
		lightDismissable: false,
		timerDismissable: false,
		timerDuration: 0,
		userDismissable: false,
		toastRole: "status",
	};

	#timer;

	/**
	 * Represents the Toast class.
	 * @class
	 */
	constructor() {
		super();
		this.#elements = this.#getElements();
		this.#setOptions();

		if (this.#elements.toastMessageContainer) {
			this.#init();
		}
	}

	/**
	 * Callback function that is called when an attribute is changed.
	 * @param {string} name - The name of the attribute that was changed.
	 * @param {*} _oldValue - The previous value of the attribute.
	 * @param {*} newValue - The new value of the attribute.
	 */
	attributeChangedCallback(name, _oldValue, newValue) {
		if (name === "message") {
			this.#elements.toastMessageContainer.textContent = newValue;
		}

		if (name === "toast-role") {
			this.#elements.toastMessageContainer.role = newValue;
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setOptions() {
		this.#options = {
			lightDismissable: this.hasAttribute("dismissable-light"),
			timerDismissable: this.hasAttribute("dismissable-timer"),
			userDismissable: this.hasAttribute("dismissable-user"),
			toastRole: this.getAttribute("toast-role"),
		};

		if (this.#options.timerDismissable) {
			this.#options.timerDuration = this.getAttribute("timer-duration");
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			toastMessageContainer: this.querySelector(
				Toast.#selectors.toastMessageContainer,
			),
			toastCloseButton: this.querySelector(Toast.#selectors.toastCloseButton),
		};
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#init() {
		this.#elements.toastMessageContainer.role = this.#options.toastRole;

		if (this.#options.toastRole === "alert") {
			this.#addAlertToastEventListeners();
		} else {
			this.#addEventListeners();
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#handleUserAction() {
		this.hidePopover();
		clearTimeout(this.#timer);
	}

	/**
	 * Handles the escape key event.
	 * @private
	 * @returns {void}
	 */
	#handleEscapeKey() {
		document.addEventListener("keyup", (event) => {
			if (event.key === "Escape") {
				this.#handleUserAction();
			}
		});
	}

	/**
	 * Adds event listeners for the alert toast role. By default,
	 * the alert toast is dismissable by the user. It is not
	 * light dismissable (click outside) or dismissable by a timer.
	 */
	#addAlertToastEventListeners() {
		const closeButton = this.#elements.toastCloseButton;

		if (closeButton) {
			closeButton.addEventListener("click", () => {
				this.#handleUserAction();
			});
		}

		this.#handleEscapeKey();
	}

	/**
	 * Closes the toast after a specified timeout. The timeout
	 * is defined on this.#options.timerDuration read from
	 * the timer-duration attribute.
	 * @private
	 * @returns {void}
	 */
	#closeAfterTimeout() {
		this.#timer = setTimeout(() => {
			this.hidePopover();
		}, this.#options.timerDuration);
	}

	/**
	 * Adds event listeners to the toast component.
	 * Depending on the options set, the toast can be
	 * dismissable by the user, light dismissable (click
	 * outside) or dismissable by a timer.
	 * @private
	 * @returns {void}
	 */
	#addEventListeners() {
		const closeButton = this.#elements.toastCloseButton;

		this.addEventListener("toggle", (event) => {
			const isClone = this.getAttribute("cloned");
			this.#state.popoverState = event.newState;

			if (isClone && event.newState === "closed") {
				this.remove();
			}
		});

		if (this.#options.timerDismissable) {
			this.addEventListener("beforetoggle", (event) => {
				if (event.newState === "open") {
					this.#closeAfterTimeout();
				}
			});
		}

		if (this.#options.userDismissable && closeButton) {
			closeButton.addEventListener("click", () => {
				this.#handleUserAction();
			});

			this.#handleEscapeKey();
		}

		if (this.#options.lightDismissable) {
			document.addEventListener("click", (event) => {
				if (event.target !== this && !this.contains(event.target)) {
					this.#handleUserAction();
				}
			});
		}
	}
}

customElements.define("finstral-toast", Toast);
