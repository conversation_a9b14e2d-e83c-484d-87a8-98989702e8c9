document.addEventListener("DOMContentLoaded", () => {
	const documentURL = document.location.href;
	const toast = document.querySelector("finstral-toast");
	const url = new URL(documentURL);

	// in dev mode we can get this from the search params
	let variation = url.searchParams.get("variation");

	if (!variation) {
		// in presentation mode we must get this from the pathname and we
		// are only really interested in knowing whether this is a
		// callback variant or not.
		variation = url.pathname.indexOf("callback") > -1 ? "callback" : "default";
	}

	if (variation === "callback") {
		if (toast) {
			this.setTimeout(() => {
				toast.showToast("This is a toast message from a callback");
			}, 1500);
		}
	} else {
		const button = document.createElement("button");
		button.innerText = "Show Toast";
		button.classList.add("Button");
		button.setAttribute("popovertarget", "support_email_sent");

		button.addEventListener("click", () => {
			toast.setAttribute(
				"message",
				"Qui mollitia dolore aut et aut est accusantium. Ut quisquam iusto praesentium laborum. Molestiae maiores iure ipsam hic deserunt non. Sint expedita vel voluptatem. Sapiente quia voluptatibus occaecati provident eos.",
			);
		});

		document.body.appendChild(button);
	}
});
