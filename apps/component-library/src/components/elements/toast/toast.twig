{{ attach_library('finstral_global/element-toast') }}

{% set role = toast_role|default("status") %}
{% set popover_type = role == "alert" or dismissible_by ? "manual" : "auto" %}
{% set user_dismissable = role == "alert" or "dismissable-user" in dismissible_by %}

<finstral-toast
	class="Toast Toast--{{ toast_style|default('default') }}"
	id="{{ id }}"
	message=""
	popover="{{ popover_type }}"
	toast-role="{{ role }}"
	{% if "dismissable-timer" in dismissible_by %} timer-duration="{{ timer_duration|default(5000) }}" {%- endif -%}
	{% if dismissible_by %} {{ dismissible_by|join(" ") }} {%- endif -%}
>
	<output></output>
	<button class="Toast-close" type="button" {% if not user_dismissable %} hidden {%- endif -%}>
		<span class="visually-hidden">{{ "global.close_button"|tc }}</span>
		{% include "@elements/icon/icon.twig" with {
			"name": "close",
		} only %}
	</button>
</finstral-toast>
