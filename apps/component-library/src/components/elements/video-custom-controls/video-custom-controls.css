/** @define VideoCustomControls; */

.VideoCustomControls {
	display: block;
	position: relative;
}

.VideoCustomControls-video {
	display: contents;
}

.VideoCustomControls-controls:not([hidden]) {
	align-items: center;
	color: var(--shared-color-surface-primary);
	display: flex;
	gap: var(--size-16);
	inset-block-start: 1rem;
	inset-inline-end: 1rem;
	position: absolute;
	z-index: var(--bring-to-front);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.VideoCustomControls-control > * {
	/* this ensures that neither the SVG nor the span elements inside the buttons trigger events */
	pointer-events: none;
}

/* Icon states */
.VideoCustomControls-muted,
.VideoCustomControls-unmuted,
.VideoCustomControls-play,
.VideoCustomControls-pause {
	display: none;
}

.VideoCustomControls-control[data-audio="true"] .VideoCustomControls-muted,
.VideoCustomControls-control[data-playback="true"] .VideoCustomControls-pause {
	display: block;
}

.VideoCustomControls-control[data-audio="false"] .VideoCustomControls-unmuted,
.VideoCustomControls-control[data-playback="false"] .VideoCustomControls-play {
	display: block;
}
