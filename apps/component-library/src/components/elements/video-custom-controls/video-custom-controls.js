export default class VideoCustomControls extends HTMLElement {
	static #selectors = {
		controlAudioButton: "[data-audio]",
		controlAudioButtonLabel: "[data-label='audio']",
		controlPlaybackButton: "[data-playback]",
		controlPlaybackButtonLabel: "[data-label='playback']",
		customControlsContainer: ".VideoCustomControls-controls",
		videoMobileElement: ".Video-asset--mobile",
		videoStandardElement: ".Video-asset--standard",
	};

	#elements;

	#strings;

	/**
	 * Initializes the VideoCustomControls custom element.
	 *
	 * This constructor creates an instance of the VideoCustomControls class and initializes
	 * the necessary elements.
	 * @returns {void}
	 */
	constructor() {
		super();

		this.#elements = {
			audio: {
				buttonRef: /** @type {HTMLButtonElement} */ this.querySelector(
					VideoCustomControls.#selectors.controlAudioButton,
				),
				buttonLabelRef: /** @type {HTMLSpanElement} */ this.querySelector(
					VideoCustomControls.#selectors.controlAudioButtonLabel,
				),
			},
			playback: {
				buttonRef: /** @type {HTMLButtonElement} */ this.querySelector(
					VideoCustomControls.#selectors.controlPlaybackButton,
				),
				buttonLabelRef: /** @type {HTMLSpanElement} */ this.querySelector(
					VideoCustomControls.#selectors.controlPlaybackButtonLabel,
				),
			},
			customControlsContainerRef:
				/** @type {HTMLUListElement} */ this.querySelector(
					VideoCustomControls.#selectors.customControlsContainer,
				),
			videoMobileElementRef: /** @type {HTMLVideoElement} */ this.querySelector(
				VideoCustomControls.#selectors.videoMobileElement,
			),
			videoStandardElementRef:
				/** @type {HTMLVideoElement} */ this.querySelector(
					VideoCustomControls.#selectors.videoStandardElement,
				),
		};

		this.#strings = {};
		if (this.#elements.audio.buttonRef) {
			this.#strings.audio = {
				activeMessage: this.#elements.audio.buttonRef.dataset.active,
				inactiveMessage: this.#elements.audio.buttonRef.dataset.inactive,
			};
		}

		if (this.#elements.playback.buttonRef) {
			this.#strings.playback = {
				activeMessage: this.#elements.playback.buttonRef.dataset.active,
				inactiveMessage: this.#elements.playback.buttonRef.dataset.inactive,
			};
		}

		this.type = this.getAttribute("type");

		if (
			this.#elements.videoStandardElementRef &&
			this.#elements.customControlsContainerRef
		) {
			this.#init();
		}
	}

	/**
	 * Initializes the component by hiding the default controls, and calling
	 * the appropriate function based on the video type. It also attaches
	 * event handlers common to both video types.
	 * @returns {void}
	 * @private
	 */
	#init() {
		const videoElements = this.querySelectorAll("video");

		// because there can be multiple video elements on the page, we need to
		// loop through them and remove the default controls for each element.
		videoElements.forEach((videoElement) => {
			videoElement.removeAttribute("controls");
		});

		if (this.type === "decorative") {
			this.#initDecorativeVideo();
		} else if (this.type === "standard") {
			this.#initStandardVideo();
		}

		this.#addCommonEventListeners();
	}

	/**
	 * Handles the playback control button click event by toggling the video playback
	 * and calling #updateUIState.
	 * @returns {void}
	 * @private
	 */
	#handlePlaybackControl() {
		const videoElement = this.#getVisibleVideo();

		if (videoElement.paused) {
			videoElement.play();
		} else {
			videoElement.pause();
		}

		this.#updateUIState("playback", videoElement);
	}

	/**
	 * Adds common event listeners for controlling video playback
	 * from external components.
	 * @private
	 * @returns {void}
	 */
	#addCommonEventListeners() {
		// allows external components to control the video playback
		this.addEventListener("mediacontrol", (event) => {
			if (event.detail.action === "playback") {
				this.#handlePlaybackControl();
			}
		});
	}

	/**
	 * Determines whether the HTMLMediaElement is active or not. For
	 * audio muted maps to false and for video paused maps to false.
	 * @param {HTMLMediaElement} element - The element to check the state for.
	 * @param {string} customControlType - The media type to check ("audio" or "video").
	 * @private
	 * @returns {boolean} - False if muted or paused, true otherwise.
	 */
	#isMediaElementActive(element, customControlType) {
		return customControlType === "audio" ? element.muted : !element.paused;
	}

	/**
	 * Returns the visible video element.
	 * @private
	 * @returns {HTMLElement} The visible video element.
	 */
	#getVisibleVideo() {
		const mobileVideo = this.#elements.videoMobileElementRef;
		const standardVideo = this.#elements.videoStandardElementRef;

		return standardVideo && standardVideo.checkVisibility()
			? standardVideo
			: mobileVideo;
	}

	/**
	 * Updates the custom controls UI state of the video component based on the custom control type
	 * and the state of the video.
	 * @param {string} customControlType - The type of custom control to update. This is one of playback or audio.
	 * @param {HTMLVideoElement} videoElement - The video element to update the UI state
	 * @private
	 * @returns {void}
	 */
	#updateUIState(customControlType, videoElement) {
		const controlTypes = ["playback", "audio"];

		if (!controlTypes.includes(customControlType)) {
			return;
		}

		const { buttonRef: button, buttonLabelRef: buttonLabel } =
			this.#elements[customControlType];
		const { activeMessage, inactiveMessage } = this.#strings[customControlType];
		const isActive = this.#isMediaElementActive(
			videoElement,
			customControlType,
		);

		buttonLabel.textContent = isActive ? inactiveMessage : activeMessage;
		button.dataset[customControlType] = isActive;
	}

	/**
	 * For decorative videos, the video is autoplayed if the user has not
	 * requested reduced motion.
	 * @returns {void}
	 * @private
	 */
	#handleAutoplay() {
		const mql = window.matchMedia("(prefers-reduced-motion: reduce)");
		const videoElement = this.#getVisibleVideo();

		if (!mql.matches) {
			videoElement.play();
			this.#updateUIState("playback", videoElement);
		}
	}

	/**
	 * Initializes the decorative video. The decorative video will auto play
	 * unless the user has requested reduced motion. The decorative video
	 * only has a playback control button.
	 * @private
	 * @returns {void}
	 */
	#initDecorativeVideo() {
		const playbackControl = this.#elements.playback.buttonRef;

		this.#handleAutoplay();

		playbackControl.hidden = false;
		playbackControl.addEventListener("click", () => {
			this.#handlePlaybackControl();
		});
	}

	/**
	 * Sets the mode of the track element for the provided video element.
	 * @param {HTMLVideoElement} videoElement - The video element.
	 * @param {TextTrackList} tracks - The list of text tracks.
	 * @private
	 * @returns {void}
	 */
	#setTrackMode(videoElement, tracks) {
		if (videoElement.muted) {
			tracks[0].mode = "showing";
		} else if (tracks.length) {
			tracks[0].mode = "hidden";
		}
	}

	/**
	 * Handles the audio control button click event by toggling the video audio
	 * and calling #updateUIState. It also shows and hides the text track based
	 * on the audio state.
	 * @returns {void}
	 * @private
	 */
	#handleAudioControl() {
		const videoElement = this.#getVisibleVideo();
		const tracks = videoElement.textTracks;

		videoElement.muted = !videoElement.muted;

		if (tracks.length) {
			this.#setTrackMode(videoElement, tracks);
		}

		this.#updateUIState("audio", videoElement);
	}

	/**
	 * Handles the button click events by delegating the event to the appropriate
	 * handler based on the target element.
	 * @param {HTMLButtonElement} target - The button element that was clicked
	 * @returns {void}
	 * @private
	 */
	#handleButtonClick(target) {
		if (target === this.#elements.audio.buttonRef) {
			this.#handleAudioControl();
		} else if (target === this.#elements.playback.buttonRef) {
			this.#handlePlaybackControl();
		}
	}

	/**
	 * Adds event listeners to the custom controls container for standard videos.
	 * This method listens for click events on the custom controls container and
	 * delegates the event to the appropriate handler.
	 * @returns {void}
	 * @private
	 */
	#initStandardVideo() {
		const customControlsContainer = this.#elements.customControlsContainerRef;
		const mobileVideo = this.#elements.videoMobileElementRef;
		const standardVideo = this.#elements.videoStandardElementRef;

		customControlsContainer.querySelectorAll("button").forEach((button) => {
			button.hidden = false;
		});

		customControlsContainer.addEventListener("click", (event) => {
			const { target } = event;
			if (target instanceof HTMLButtonElement) {
				this.#handleButtonClick(target);
			}
		});

		// this will not impact decorative video as these are set to loop and will
		// therefore never fire the ended event.
		if (standardVideo) {
			standardVideo.addEventListener("ended", () =>
				this.#updateUIState("playback", standardVideo),
			);
		}

		if (mobileVideo) {
			mobileVideo.addEventListener("ended", () =>
				this.#updateUIState("playback", mobileVideo),
			);
		}
	}
}

customElements.define("video-custom-controls", VideoCustomControls);
