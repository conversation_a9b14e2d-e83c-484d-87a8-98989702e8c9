{{ attach_library('finstral_global/element-video-custom-controls') }}


<video-custom-controls class="VideoCustomControls {% if classes %} {{ classes|join(' ') }} {% endif %}" type="{{ type|default('standard') }}">
	{{ video|mira({
		classes: ["VideoCustomControls-video"],
		class_prefix: "VideoCustomControls",
		autoplay: false,
		controls: true,
		loop: type == "decorative",
		muted: true,
		preload: "metadata"
	}) }}

	<ul class="VideoCustomControls-controls">
		<li>
			<button
				class="VideoCustomControls-control"
				data-active="{{ "global.mute_video"|tc }}"
				data-inactive="{{ "global.unmute_video"|tc }}"
				data-audio="true"
				type="button" hidden>
					<span class="VideoCustomControls-muted">
						{% include "@elements/icon/icon.twig" with {
							name: "muted"
						} only %}
					</span>
					<span class="VideoCustomControls-unmuted">
						{% include "@elements/icon/icon.twig" with {
							name: "audio"
						} only %}
					</span>
					<span class="visually-hidden" data-label="audio">{{ "global.unmute_video"|tc }}</span>
				</button>
		</li>
		<li>
			<button
				class="VideoCustomControls-control"
				data-active="{{ "global.play_video"|tc }}"
				data-inactive="{{ "global.pause_video"|tc }}"
				data-playback="false"
				type="button" hidden>
					<span class="VideoCustomControls-play">
						{% include "@elements/icon/icon.twig" with {
							name: "play"
						} only %}
					</span>
					<span class="VideoCustomControls-pause" hidden>
						{% include "@elements/icon/icon.twig" with {
							name: "pause"
						} only %}
					</span>
					<span class="visually-hidden" data-label="playback">{{ "global.play_video"|tc }}</span>
				</button>
		</li>
	</ul>
</video-custom-controls>
