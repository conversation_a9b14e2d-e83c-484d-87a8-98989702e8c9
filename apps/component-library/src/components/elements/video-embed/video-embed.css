/** @define VideoEmbed; */

.VideoEmbed {
	background: var(--shared-color-surface-subtle);
	display: grid;
	grid-template-areas: "cover";
	inline-size: 100%;
	place-items: center;
}

.VideoEmbed-embed,
.VideoEmbed-fallback {
	grid-area: cover;
}

.VideoEmbed-embed {
	aspect-ratio: 16 / 9;
	border: 0;
	inline-size: 100%;
}

.VideoEmbed-fallback {
	align-items: center;
	display: flex;
	flex-direction: column;
	gap: var(--size-m);
	padding: var(--size-16);
}

.VideoEmbed-fallbackButton {
	align-items: center;
	background: var(--shared-color-surface-brand-secondary);
	block-size: var(--size-80);
	border-radius: 50%;
	color: var(--shared-color-surface-primary);
	display: flex;
	font-size: var(--typo-HeadlineXL-font-size);
	inline-size: var(--size-80);
	justify-content: center;
	padding-inline-start: var(--size-2); /* Optical alignment for icon */
}

.VideoEmbed-fallbackText {
	text-align: center;
}
