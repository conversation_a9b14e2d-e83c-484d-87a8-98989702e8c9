$schema: http://json-schema.org/draft-07/schema#
$id: /elements/video

type: object

additionalProperties: false

required:
  - src
  - width
  - height

properties:
  subtitles:
    type: array
    items:
      additionalProperties: false
      type: object
      required:
        - language
        - src
      properties:
        default:
          type: boolean
        language:
          type: string
        src:
          type: string
          format: uri-reference
  src:
    type: string
    format: uri-reference
  width:
    type: number
  height:
    type: number
  type:
    type: string
  mobile:
    type: object
    properties:
      src:
        type: string
        format: uri-reference
      type:
        type: string
      width:
        type: number
      height:
        type: number
      poster:
        type: string
        format: uri-reference
    required:
      - src
      - width
      - height
  controls:
    type: boolean
  autoplay:
    type: boolean
  muted:
    type: boolean
  loop:
    type: boolean
  poster:
    type: string
    format: uri-reference
  classes:
    type: array
    items:
      type: string
  preload:
    type: string
    enum:
      - none
      - metadata
      - auto
  skip_cookiebot_consent:
    type: boolean
