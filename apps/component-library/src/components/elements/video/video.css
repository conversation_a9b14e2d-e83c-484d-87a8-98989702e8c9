/** @define Video; */

/* we are using grid-areas to place the .Video-fallback on top of the video without adding a new stacking context */
.Video-wrapper {
	block-size: 100%;
	display: grid;
	grid-template-areas: "cover";
}

.Video-asset {
	background: var(--shared-color-surface-subtle-darker);
	block-size: auto;
	grid-area: cover;
	inline-size: 100%;
	object-fit: cover;
}

.Video-fallback {
	align-items: center;
	background-color: var(--shared-color-surface-estrich);
	display: flex;
	flex-direction: column;
	gap: var(--size-24);
	grid-area: cover;
	inset: 0;
	justify-content: center;
	padding: 1rem;
	z-index: 1;
}

.Video-fallbackButton {
	--size: 5rem;

	align-items: center;
	background: var(--shared-color-surface-brand);
	block-size: var(--size);
	border-radius: 50%;
	color: var(--shared-color-text-invert);
	display: flex;
	inline-size: var(--size);
	justify-content: center;
	padding-inline-start: 0.5em; /* Optical alignment for icon */
}

.Video-fallbackButton::before {
	border-block-end: calc(var(--size) / 4) solid transparent;
	border-block-start: calc(var(--size) / 4) solid transparent;
	border-inline-start: calc(var(--size) / 2.5) solid
		var(--shared-color-surface-primary);
	content: "";
}

@media (48em > width) {
	.Video-asset--mobile + .Video-asset {
		display: none;
	}
}

@media (width >= 48em) {
	.Video-asset--mobile {
		display: none;
	}
}
