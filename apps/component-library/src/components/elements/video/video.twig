{% set src_attribute = skip_cookiebot_consent ? "src" : "data-cookieblock-src" %}

<div class="Video-wrapper {% if classes %} {{ classes|join(' ') }} {% endif %}">
	{% if mobile is defined and mobile.src is defined and mobile.src is not empty %}
		{%- if autoplay %}<!-- [html-validate-disable-block no-autoplay] -->{% endif -%}
		<video
			width="{{ mobile.width }}"
			height="{{ mobile.height }}"
			style="aspect-ratio: {{ mobile.width }} / {{ mobile.height }};"
			playsinline
			class="js-Video-asset Video-asset Video-asset--mobile
			{%- if class_prefix %} {{ class_prefix ~ "-asset" }}{% endif -%}"
			{%- if preload %} preload="{{ preload }}"{% endif -%}
			{%- if controls %} controls controlslist="nodownload" {% endif -%}
			{%- if autoplay %} autoplay{% endif -%}
			{%- if muted %} muted{% endif -%}
			{%- if loop %} loop{% endif -%}
			{%- if mobile.poster is defined %} poster="{{ mobile.poster }}"{% endif -%}
		>
			<source {{ src_attribute }}="{{ mobile.src }}" type="{{ mobile.type|default('video/mp4') }}">
			{%- if mobile.subtitles -%}
				{% for track in mobile.subtitles %}
					<track src="{{ track.src }}" srclang="{{ track.language }}" {% if track.default %} default {%- endif -%}>
				{% endfor %}
			{%- endif -%}
		</video>
	{% endif %}
	{%- if autoplay %}<!-- [html-validate-disable-block no-autoplay] -->{% endif -%}
	<video
		width="{{ width }}"
		height="{{ height }}"
		style="aspect-ratio: {{ width }} / {{ height }};"
		playsinline
		class="js-Video-asset Video-asset Video-asset--standard
		{%- if class_prefix %} {{ class_prefix ~ "-asset" }}{% endif -%}"
		{%- if preload %} preload="{{ preload }}"{% endif -%}
		{%- if controls %} controls controlslist="nodownload" {% endif -%}
		{%- if autoplay %} autoplay{% endif -%}
		{%- if muted %} muted{% endif -%}
		{%- if loop %} loop{% endif -%}
		{%- if poster is defined %} poster="{{ poster }}"{% endif -%}
	>
		<source {{ src_attribute }}="{{ src }}" type="{{ type|default('video/mp4') }}">
		{%- if subtitles -%}
			{% for track in subtitles %}
				<track src="{{ track.src }}" srclang="{{ track.language }}" {% if track.default %} default {%- endif -%}>
			{% endfor %}
		{%- endif -%}
	</video>
	{% if not skip_cookiebot_consent %}
		<div class="Video-fallback cookieconsent-optout-marketing">
			<button
				class="Video-fallbackButton"
				aria-label="{{ "video.fallback_button"|tc }}"
				onclick="javascript:Cookiebot.renew()"
				type="button"
			></button>
			<p>{{ "video.cookie_consent_text"|tc }}</p>
		</div>
	{% endif %}
</div>
