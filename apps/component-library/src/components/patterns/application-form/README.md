This component is used for the two kinds of job application forms we have in `jobs`.
The `Initiativbewerbung` form has it's own page and uses a `text-teaser` component for its title.
Job specific forms are rendered inside an iframe in a dialog and use a <header> with a title.
Both variants use the same `elements/form#Application-form`.

Currently there is some style logic in both the `/elements/form` (.Form-addressFields class coming from drupal) and the `/elements/form-elements` (setting --span via inline styles). This is not ideal and should get reworked at some point.

Also, currently the headings and the form are not connected. They should get IDs and aria-labelledby respectively to ensure their accessibility. As the form here is a render-array this was not possible to implement on the fly.
