/** @define ApplicationForm; */

.ApplicationForm {
	--u-breakout-background: var(--ApplicationForm-background, transparent);

	background: var(--u-breakout-background);
	padding-block-end: var(--ApplicationForm-padding-block-end);
}

.ApplicationForm-form {
	display: grid;
	gap: var(--ApplicationForm-row-gap);

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	& > .Form-copy {
		margin-block-end: var(--size-24);
	}
}

@media (width < 48em) {
	.ApplicationForm {
		--ApplicationForm-padding-block-end: var(--size-48);
		--ApplicationForm-header-padding-block: var(--size-24) var(--size-48);
	}

	.ApplicationForm-form {
		--ApplicationForm-row-gap: var(--size-24);
	}
}

@media (width >= 48em) {
	.ApplicationForm {
		--ApplicationForm-padding-block-end: var(--size-64);
		--ApplicationForm-header-padding-block: var(--size-48);
	}

	.ApplicationForm-form {
		--ApplicationForm-row-gap: var(--size-24);
		grid-template-columns: repeat(12, auto);

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		& > .Form-copy {
			grid-column: 1 / span 12;
		}

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		p:not(div > p) {
			grid-column: 1 / span 12;
		}
	}
}

.ApplicationForm-textTeaser,
.ApplicationForm-header {
	grid-column: var(--ApplicationForm-child-grid-column);
}

/*
	position: sticky is currently causing a rendering problem inside a scrolling iframe in Safari IOS.
	That's why we can't use `position: sticky`. Please try it again in the future.
*/
.ApplicationForm-header {
	padding-block: var(--ApplicationForm-header-padding-block);
}

.ApplicationForm-additionalInfo {
	margin-block-start: var(--size-16);
}

.ApplicationForm-additionalInfoItem {
	display: inline;
}

.ApplicationForm-additionalInfoItem:not(:last-child)::after {
	content: "|";
	margin-inline: var(--size-8);
}
