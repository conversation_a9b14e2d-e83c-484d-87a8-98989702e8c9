import {
	countryWithProvince,
	initCountryAndNationality,
	updateProvinceState,
} from "../../../js/utils/helper/forms";

const [country, province, nationality] = document.querySelectorAll(
	"#edit-country, #edit-province, #edit-nationality",
);

if (country && province && nationality) {
	initCountryAndNationality();

	country.addEventListener("change", (event) => {
		const countryCode = event.target.value;
		const countryHasProvince = countryWithProvince.includes(countryCode);
		updateProvinceState(countryHasProvince, province);
	});
}
