{{ attach_library('finstral_global/pattern-application-form') }}

<div
	{# class="ApplicationForm u-container u-grid u-breakout" #}
	class="ApplicationForm u-container u-breakout"
	{% if background_color %} style="--ApplicationForm-background: {{ background_color }};"{% endif %}
>
	{% if text_teaser %}
		{{ text_teaser|mira({
			classes: ["ApplicationForm-textTeaser"],
			element: true
		}) }}
	{% endif %}

	{% if in_dialog %}
		<header class="ApplicationForm-header">
			{% if title %}
				<h2 class="ApplicationForm-title u-typo-HeadlineM">{{ title }}</h2>
			{% endif %}

			{% if additional_info %}
				<ul class="ApplicationForm-additionalInfo u-typo-TextM">
					{% for item in additional_info %}
						<li class="ApplicationForm-additionalInfoItem">{{ item }}</li>
					{% endfor %}
				</ul>
			{% endif %}
		</header>
	{% endif %}

	{{ form|mira({
		classes: ["ApplicationForm-form", in_dialog ? "ApplicationForm-form--inDialog" : ""]
	}) }}
</div>
