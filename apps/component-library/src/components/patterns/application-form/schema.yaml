$schema: http://json-schema.org/draft-07/schema
$id: /patterns/application-form
additionalProperties: false
type: object
required:
  - form
properties:
  form:
    type: string
    format: html
    description: /elements/form
  text_teaser:
    type: string
    format: html
    description: /patterns/text-teaser
  background_color:
    type: string
    description: "Format: #rrggbb"
  title:
    type: string
  additional_info:
    type: array
    items:
      type: string
  in_dialog:
    type: boolean
    description: "When it's in a dialog the form gets a header and additional spacing."
