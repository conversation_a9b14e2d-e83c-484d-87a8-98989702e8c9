/** @define CardsList; */

.CardsList {
	--CardsList-child-grid-column: 2/-2;

	background-color: var(--theme-background-color);
	padding-block: var(--CardsList-padding-block, var(--size-128));
	row-gap: var(--CardList-row-gap, var(--size-40));

	&:not(&.Search-cardsList) {
		--u-breakout-background: var(--theme-background-color);

		background-color: var(--u-breakout-background);
	}
}

.CardsList-heading {
	grid-column: var(--CardsList-child-grid-column);
}

.CardsList-list {
	display: grid;
	gap: var(--size-16);
	grid-column: var(--CardsList-child-grid-column);
	grid-template-columns: repeat(
		var(--CardsList-list-grid-columns-amount, 3),
		1fr
	);
}

.CardsList-item {
	min-inline-size: 0;
}

@media (48em > width) {
	.CardsList {
		--CardList-row-gap: var(--size-32);
		--CardsList-child-grid-column: 1 / -1;
		--CardsList-list-grid-columns-amount: 1;
		--CardsList-padding-block: var(--size-48);
	}
}

@media (48em <= width < 64em) {
	.CardsList {
		--CardsList-child-grid-column: 1 / -1;
		--CardsList-list-grid-columns-amount: 2;
	}
}
