{{ attach_library('finstral_global/pattern-cards-list') }}

<div
	class="CardsList u-container u-grid u-breakout {{ classes|join(" ") }}"
	{% if color_scheme %} data-theme="{{ color_scheme }}"{% endif %}
>
	<h2 class="CardsList-heading u-typo-HeadlineXL{% if hidden_heading %} u-hiddenVisually{% endif %}">
		{{ heading }}
	</h2>
	<ol class="CardsList-list js-CardsList-list">
		{% for item in list %}
			<li class="CardsList-item">{{ item }}</li>
		{% endfor %}
	</ol>
</div>
