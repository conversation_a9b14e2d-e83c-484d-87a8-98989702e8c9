$schema: http://json-schema.org/draft-07/schema
$id: /patterns/cards-list
additionalProperties: false
type: object
required:
  - list
  - heading
properties:
  heading:
    type: string
  hidden_heading:
    type: boolean
  list:
    type: array
    items:
      type: string
      format: html
      description: patterns/teaser-list/teaser
  classes:
    type: array
    items:
      type: string
  color_scheme:
    type: string
    enum:
      - white
      - sandsteinlight
