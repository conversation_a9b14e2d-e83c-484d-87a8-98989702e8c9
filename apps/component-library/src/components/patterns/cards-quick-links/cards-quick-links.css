/** @define CardsQuickLinks; */

.CardsQuickLinks {
	--CardsQuickLinks-background: var(--shared-color-surface-schlern-light);
	--u-breakout-background: var(--CardsQuickLinks-background);

	align-items: center;
	background-color: var(--CardsQuickLinks-background);
	display: flex;
	flex-direction: column;
	padding-block: var(--CardsQuickLinks-padding-block);
}

.CardsQuickLinks-heading {
	margin-block-end: var(--size-32);
}

.CardsQuickLinks-list {
	column-gap: var(--CardsQuickLinks-column-gap);
	display: grid;
	row-gap: var(--size-32);
}

@media (width < 48rem) {
	.CardsQuickLinks {
		--CardsQuickLinks-padding-block: var(--size-40);
		--CardsQuickLinks-column-gap: var(--size-12);
	}

	.CardsQuickLinks-list {
		grid-template-columns: repeat(auto-fill, minmax(35vw, 1fr));
	}
}

@media (width >= 48rem) {
	.CardsQuickLinks {
		--CardsQuickLinks-padding-block: var(--size-96);
		--CardsQuickLinks-column-gap: var(--size-16);
	}

	.CardsQuickLinks-list {
		grid-template-columns: repeat(auto-fill, minmax(20vw, 1fr));
		max-width: 75vw;
	}
}

@media (width >= 75rem) {
	.CardsQuickLinks-list {
		grid-template-columns: repeat(auto-fill, minmax(17.25rem, 1fr));
		max-width: 85vw;
	}
}
