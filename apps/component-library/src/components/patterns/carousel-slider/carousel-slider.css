/* @define CarouselSlider; */

@import url("./slide/slide.css") layer(components);

.CarouselSlider {
	--slide-size-sm: 15.75rem;
	--slide-size-md: 17.8125rem;
}

@media screen and (width < 48rem) {
	.CarouselSlider {
		display: flex;
		gap: var(--size-16);
		overflow-x: auto;
		scroll-snap-type: x mandatory;
	}
}

@media (hover: none) {
	.CarouselSlider {
		scrollbar-width: none;
	}
}

@media screen and (width >= 48rem) {
	.CarouselSlider {
		display: grid;
		gap: var(--size-24);
		margin: auto;
	}

	.CarouselSlider--md {
		grid-template-columns: repeat(auto-fit, minmax(var(--slide-size-md), 1fr));
	}

	.CarouselSlider--sm {
		grid-template-columns: repeat(auto-fit, minmax(var(--slide-size-sm), 1fr));

		/* When there are more than (today) four slides we need to increase the size
		   of each slide to ensure we do not only have a single slide on a line when
			 there are, for example, five slides. */
		&.CarouselSlider-sm--extended {
			grid-template-columns: repeat(
				auto-fit,
				minmax(var(--slide-size-md), 1fr)
			);
		}
	}
}
