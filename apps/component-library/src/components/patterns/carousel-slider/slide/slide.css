/* @define CarouselSliderSlide; */

.CarouselSliderSlide {
	.CarouselSliderSlide-image {
		margin-block-end: var(--CarouselSliderSlide-image-end-spacing);
	}

	.CarouselSliderSlide-title {
		font-size: var(--typo-HeadlineS-font-size);
		margin-block-end: var(--size-8);
	}

	.CarouselSliderSlide-description {
		color: var(--shared-color-text-secondary);
		font-size: var(--typo-TextM-font-size);
	}
}

@media screen and (width < 48rem) {
	.CarouselSliderSlide {
		--CarouselSliderSlide-image-end-spacing: var(--size-16);

		flex: 1 0 min(80vw, 430px);
		scroll-snap-align: start;
	}
}

@media screen and (width >= 48rem) {
	.CarouselSliderSlide {
		--CarouselSliderSlide-image-end-spacing: var(--size-24);
	}
}
