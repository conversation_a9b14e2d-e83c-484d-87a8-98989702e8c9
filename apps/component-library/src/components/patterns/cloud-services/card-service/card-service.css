/** @define CloudServicesCardService; */

.CloudServicesCardService {
	background-color: var(--shared-color-surface-primary);
	border: var(--border-subtle);
	color: var(--shared-color-text-primary);
	display: flex;
	flex-flow: column;
	gap: var(--size-32);
	justify-content: space-between;
	padding: var(--size-16);
}

.CloudServicesCardService-description {
	color: var(--shared-color-text-secondary);
	margin-block-start: var(--CloudServicesCardService-description-spacing);
}

@media (width < 48em) {
	.CloudServicesCardService {
		--CloudServicesCardService-description-spacing: var(--size-8);

		inline-size: 100%;
		min-block-size: 8.3125rem;
	}

	.CloudServicesCardService-links {
		display: flex;
		justify-content: space-between;
	}
}

@media (width >= 48em) {
	.CloudServicesCardService {
		--CloudServicesCardService-description-spacing: var(--size-16);

		min-block-size: 16rem;
	}
}
