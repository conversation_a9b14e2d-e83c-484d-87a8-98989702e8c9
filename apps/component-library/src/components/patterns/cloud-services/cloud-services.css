@import "./welcome-message/welcome-message.css";
@import "./card-service/card-service.css";

/** @define CloudServices; */

.CloudServices {
	--CloudServices-background: var(--shared-color-surface-leinen-light);
	--u-breakout-background: var(--CloudServices-background);

	background: var(--CloudServices-background);
	padding-block-end: var(--CloudServices-padding-block-end);
	padding-block-start: var(--CloudServices-padding-block-start);
}

.CloudServices-wrapper {
	display: flex;
}

.CloudServices-services {
	display: flex;
	flex: 1 1 100%;
	flex-flow: column;
	gap: var(--CloudServices-services-gap-size);
}

@media (width < 48rem) {
	.CloudServices {
		--CloudServices-padding-block-end: var(--size-40);
		--CloudServices-padding-block-start: var(--size-32);
		--CloudServices-services-gap-size: var(--size-12);
	}
}

@media (width >= 48rem) {
	.CloudServices {
		--CloudServices-padding-block-end: var(--size-96);
		--CloudServices-padding-block-start: var(--size-80);
		--CloudServices-services-gap-size: var(--size-16);
	}

	.CloudServices-services {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(10rem, 20rem));
		justify-content: center;
	}
}
