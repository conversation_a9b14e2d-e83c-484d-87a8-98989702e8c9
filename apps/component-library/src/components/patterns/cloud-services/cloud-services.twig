{{ attach_library('finstral_global/pattern-cloud-services') }}

{% set heading_id = "cloud-services-" ~ random() ~ "-heading" %}

{% for info_message in info_messages %}
	{{ info_message|raw }}
{% endfor %}

<div class="CloudServices u-container u-breakout">
  {% include "@patterns/cloud-services/welcome-message/welcome-message.twig" with welcome_message only %}

	{% if services is not empty %}
		<section aria-labelledby="{{ heading_id }}" class="CloudServices-wrapper">
			<span id="{{ heading_id }}" hidden>{{ services_section_label }}</span>
			<ul class="CloudServices-services">
				{% for service in services %}
					<li>{{ service }}</li>
				{% endfor %}
			</ul>
		</section>
	{% endif %}
</div>
