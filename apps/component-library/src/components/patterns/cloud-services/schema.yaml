$schema: http://json-schema.org/draft-07/schema
$id: /patterns/cloud-services

type: object

required:
  - services_section_label
  - services
  - welcome_message

additionalProperties: false

properties:
  services_section_label:
    type: string

  services:
    type: array
    items:
      type: string
      format: html
      description: /patterns/cloud-services/card-service

  info_messages:
    type: array
    items:
      type: string
      format: html
      description: elements/info-message

  welcome_message:
    type: object
    additionalProperties: false
    required:
      - message
    properties:
      heading_id:
        type: string
      message:
        type: string
