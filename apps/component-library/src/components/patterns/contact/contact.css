@import url("details/details.css");

/** @define Contact; */

.Contact {
	--Contact-background: var(--shared-color-surface-primary);
	--u-breakout-background: var(--Contact-background);

	align-items: center;
	background-color: var(--Contact-background);
}

@media (48em > width) {
	.Contact {
		padding-block: var(--size-40);
		row-gap: var(--size-48);
	}

	.Contact-media,
	.Contact-details {
		grid-column: 1 / -1;
	}

	.Contact-media {
		margin-inline-start: calc(var(--component-spacing-inline) * -1);
	}
}

@media (width >= 48em) {
	.Contact {
		padding-block: var(--size-96);
	}
}

@media (64em > width >= 48em) {
	.Contact-media,
	.Contact-details {
		grid-column: span 4;
	}

	.Contact-details {
		padding-inline: var(--size-40);
	}
}

@media (width >= 64em) {
	.Contact-media {
		grid-column: 1 / 7;
	}

	.Contact-details {
		grid-column: 8 / 12;
	}
}
