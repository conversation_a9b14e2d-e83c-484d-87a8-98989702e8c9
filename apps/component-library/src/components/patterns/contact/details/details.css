/** @define ContactDetails; */

/* Description */

.ContactDetails-description {
	color: var(--shared-color-text-secondary);
	margin-block-start: var(--size-12);
}

/* List */

.ContactDetails-list {
	display: grid;
}

@media (48em > width) {
	.ContactDetails-list {
		gap: var(--size-48);
		margin-block-start: var(--size-36);
	}
}

@media (width >= 48em) {
	.ContactDetails-list {
		gap: var(--size-40);
		margin-block-start: var(--size-32);
	}
}

/* Link */

.ContactDetails-link {
	display: flex;
}

@media (48em > width) {
	.ContactDetails-link {
		gap: var(--size-16);
	}
}

@media (width >= 48em) {
	.ContactDetails-link {
		gap: var(--size-24);
	}
}

.ContactDetails-linkIcon {
	flex-shrink: 0;
}

.ContactDetails-linkContent {
	display: grid;

	/**
	 * The design trimmed the text box which is not yet possible in CSS.
	 * To keep the designed distance between heading and description,
	 * a smaller gap compared to the one set in the design is necessary.
	 */
	gap: var(--size-4);
}

.ContactDetails-linkText {
	color: var(--shared-color-text-secondary);

	/* Force wrap long non-breakable strings, for example email addresses. */
	overflow-wrap: anywhere;

	transition: color var(--default-transition-duration) ease;
}

.ContactDetails-link:is(:hover, :active) .ContactDetails-linkText {
	color: var(--shared-color-text-brand);
}
