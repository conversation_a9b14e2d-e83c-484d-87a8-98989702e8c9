/** @define Faq; */

.Faq {
	--Faq-child-grid-column: 1 / -1;
	--u-breakout-background: var(--Faq-background);

	background: var(--Faq-background);
	color: var(--Faq-color);
	padding-block: var(--Faq-padding-block, var(--size-96));
	row-gap: var(--size-32);
}

.Faq-child {
	grid-column: var(--Faq-child-grid-column);
}

@media (48em > width) {
	.Faq {
		--Faq-padding-block: var(--size-48) var(--size-64);
	}
}

@media (width >= 64em) {
	.Faq {
		--Faq-child-grid-column: 2 / -2;
	}
}
