/* @define PersonalInformation; */

.PersonalInformation {
	display: grid;
	gap: var(--size-24);
}

/* @define Form; */

.Form-fieldGroup,
/* stylelint-disable-next-line plugin/selector-bem-pattern */
.Form-fieldGroup-subgridItem {
	grid-column: span var(--colspan);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.Form-fieldGroup-subgrid {
	display: grid;
	grid-column: 1 / -1;
	grid-template-columns: subgrid;
}

@media (width < 48rem) {
	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.Form-fieldGroup-subgrid {
		grid-column: span 6;
	}
}

@media (width >= 48rem) {
	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.PersonalInformation {
		grid-template-columns: repeat(12, 1fr);
	}
}
