{{ attach_library('finstral_global/pattern-forms-personal-information') }}

<fieldset class="Fieldset PersonalInformation">
	<legend>{{ 'global.form.legend.personal_data'|tc }}</legend>
	<div class="Form-fieldGroup-subgrid">
		<div class="Form-fieldGroup-subgridItem" style="--colspan: 6;">
			<label for="title">{{ "global.form.label.title"|tc }}<span aria-hidden="true">*</span></label>
			<select class="Input Input--select" name="title" id="title">
				<option value="mr">{{ "global.select.title_mr"|tc }}</option>
				<option value="mrs">{{ "global.select.title_mrs_ms"|tc }}</option>
			</select>
		</div>
		<p class="Error" id="titleError" hidden></p>
	</div>
	<div class="Form-fieldGroup" style="--colspan: 6;">
		<label for="firstName">{{ "global.form.label.first_name"|tc }}<span aria-hidden="true">*</span></label>
		<input class="Input" type="text" id="firstName" name="firstName" required/>
		<p class="Error" id="firstNameError" hidden></p>
	</div>
	<div class="Form-fieldGroup" style="--colspan: 6;">
		<label for="lastName">{{ "global.form.label.last_name"|tc }}<span aria-hidden="true">*</span></label>
		<input class="Input" type="text" id="lastName" name="lastName" required/>
		<p class="Error" id="lastNameError" hidden></p>
	</div>
	<div class="Form-fieldGroup" style="--colspan: 6;">
		<label for="email">{{ "global.form.label.email"|tc }}<span aria-hidden="true">*</span></label>
		<input class="Input" type="email" id="email" name="email" required/>
		<p class="Error" id="emailError" hidden></p>
	</div>
	<div class="Form-fieldGroup" style="--colspan: 6;">
		<label for="phone">{{ "global.form.label.phone"|tc }}</label>
		<input class="Input" type="tel" id="phone" name="phone"/>
	</div>
</fieldset>
