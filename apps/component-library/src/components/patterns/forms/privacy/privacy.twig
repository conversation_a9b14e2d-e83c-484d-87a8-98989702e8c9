{{ attach_library('finstral_global/pattern-forms-privacy') }}

{% set options = [{
	id: 'data_processing',
	name: "dataProcessing",
	label: 'global.form.label.data_processing'|tc,
	help_text: 'global.form.label.data_processing.help_text'|tc,
}, {
	id: 'consent_to_contact',
	name: "consentToContact",
	label: 'global.form.label.consent_to_contact'|tc,
	help_text: 'global.form.label.consent_to_contact.help_text'|tc,
}, {
	id: 'consent_to_contact_partner',
	name: "consentToContactPartner",
	label: 'global.form.label.consent_to_contact_partner'|tc,
	help_text: 'global.form.label.consent_to_contact_partner.help_text'|tc,
}] %}

<fieldset class="Fieldset Privacy">
	<legend>
		<span class="Privacy-heading">{{ 'global.form.legend.data_processing'|tc }}</span>
		<span class="Privacy-link">{% include "@elements/link/link.twig" with {
			label: "global.form.legend.privacy_policy"|tc,
			size: "TextM",
			url: "/privacy",
		} only %}</span>
	</legend>
	<ul class="Privacy-options">
		{% for option in options %}
			<li>
					<label class="Privacy-label Checkbox Option-next u-typo-TextM" for="{{ option.id }}">
						<input type="checkbox" id="{{ option.id }}" name="{{ option.name }}" {% if loop.first %}required{% endif %}>
						{{ option.label|tc }}{% if loop.first %}<span aria-hidden="true">*</span>{% endif %}
					</label>
				<p class="InformationRequestForm-helpText">{{ option.help_text|tc }}</p>
				<p class="Error" id="{{ option.name }}Error" hidden></p>
			</li>
		{% endfor %}
	</ul>
</fieldset>
