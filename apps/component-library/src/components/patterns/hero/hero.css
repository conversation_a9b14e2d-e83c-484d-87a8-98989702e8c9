/** @define Hero; weak */

.<PERSON> {
	block-size: max(80svh, 37.6rem); /* magic numbers from figma design */
	color: var(--shared-color-text-invert);
	container-type: size;
	overflow: hidden;
	position: relative;
}

.Hero-media,
.Hero-media img {
	block-size: 100%;
	inline-size: 100%;
}

.Hero-media img {
	object-fit: cover;
}

.Hero-media video {
	block-size: 100%;
}

.Hero-content {
	inset: auto 0 var(--Hero-content-inset-block-end, var(--size-32));
	position: absolute;
}

.Hero:has(.Hero-content)::before {
	background-color: var(--shared-color-surface-invert);
	content: "";
	inset: 0;
	opacity: var(--Hero-content-before-opacity, 0.2);
	position: absolute;
}

.Hero:has(.Hero-partner) {
	color: var(--shared-color-text-primary);
	display: grid;
	grid-template-areas: "hero";
	overflow: clip;

	.Hero-media,
	.Hero-partner {
		grid-area: hero;
	}

	.Hero-media,
	.Hero-media img {
		block-size: inherit;
	}

	.Hero-partner {
		align-self: end;
		background-color: var(--shared-color-surface-primary);
		block-size: auto;
		inline-size: var(--Hero-partner-inline-size, 100%);
		margin-block-end: var(--Hero-partner-block-end);
		margin-inline-start: var(--Hero-partner-inline-start);
		padding: var(--Hero-partner-padding);
	}

	.Hero-partner img {
		inline-size: var(--Hero-partner-logo-size);
		margin-block-end: var(--Hero-partner-logo-spacing);
	}

	.Hero-partnerTitle {
		margin-block-end: var(--Hero-partner-title-spacing);
	}
}

.Hero-additionalInfo {
	padding-block-end: var(--size-16);
}

.Hero-additionalInfoItem {
	display: inline;
}

.Hero-additionalInfoItem:not(:last-child)::after {
	content: "|";
	margin-inline: var(--size-8);
}

.Hero-cta {
	--Hero-cta-block-start: var(--size-16);
	--Hero-cta-inline-end: var(--size-16);
	transition: inset var(--default-transition-duration);
}

@media screen and (width < 48rem) {
	.Hero-cta {
		inline-size: 100%;
	}
}

@media screen and (width >= 48rem) {
	.Hero-cta {
		inset-block-start: var(--Hero-cta-block-start);
		inset-inline-end: var(--Hero-cta-inline-end);
		position: absolute;
		z-index: var(--top-layer);
	}
}

@media (width < 64em) {
	.Hero-partner {
		--Hero-partner-inline-size: 32.5rem;
		--Hero-partner-block-end: var(--size-32);
		--Hero-partner-inline-start: 0;
		--Hero-partner-padding: var(--size-24);

		--Hero-partner-logo-size: 12.5rem;
		--Hero-partner-logo-spacing: var(--size-16);
		--Hero-partner-title-spacing: var(--size-16);

		max-inline-size: 21.875rem;
	}
}

@media (width >= 64em) {
	.Hero {
		--Hero-content-inset-block-end: var(--size-96);
	}

	.Hero-cta {
		--Hero-cta-block-start: var(--size-24);
		--Hero-cta-inline-end: var(--size-48);
	}

	.Hero-partner {
		--Hero-partner-inline-size: 32.5rem;
		--Hero-partner-block-end: var(--size-128);
		--Hero-partner-inline-start: var(--size-128);
		--Hero-partner-padding: var(--size-48);

		--Hero-partner-logo-size: 16.875rem;
		--Hero-partner-logo-spacing: var(--size-40);
		--Hero-partner-title-spacing: var(--size-24);
	}
}
