{{ attach_library('finstral_global/pattern-hero') }}

<div class="Hero"
	{%- if overlay_opacity %} style="--Hero-content-before-opacity: {{ overlay_opacity }}"{% endif -%}
>
	<div class="Hero-media">{{ media }}</div>
	{% if additional_info or heading %}
		<div class="Hero-content u-container">
			{% if additional_info %}
				<ul class="Hero-additionalInfo">
					{% for item in additional_info %}
						<li class="Hero-additionalInfoItem">{{ item }}</li>
					{% endfor %}
				</ul>
			{% endif %}
			<h1 class="Hero-heading">{{ heading }}</h1>
		</div>
	{% endif %}

	{% if partner %}
		<div class="Hero-partner">
			{% if partner.logo %}
				{{ partner.logo }}
			{% endif %}
			<h2 class="Hero-partnerTitle">{{ partner.title }}</h2>
			<p class="Hero-partnerDescription u-typo-TextL">{{ partner.description }}</p>
		</div>
	{% endif %}
</div>

{% if cta %}
	{{ cta|mira({
		classes: ["Hero-cta"],
		url: cta.url,
		label: cta.label,
	}) }}
{% endif %}
