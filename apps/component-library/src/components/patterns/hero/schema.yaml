$schema: http://json-schema.org/draft-07/schema
$id: /patterns/hero

type: object

required:
  - media

additionalProperties: false

properties:
  media:
    type: string
    format: html
    description: elements/image or elements/video

  heading:
    type: string

  additional_info:
    type: array
    items:
      type: string

  cta:
    $ref: /elements/button

  partner:
    type: object
    required:
      - title
      - description
    additionalProperties: false
    properties:
      logo:
        type: string
        format: html
        description: elements/image
      title:
        type: string
      description:
        type: string

  overlay_opacity:
    type: number
    min: 0.1
    max: 0.8
