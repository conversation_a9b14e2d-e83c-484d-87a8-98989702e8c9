{% set additional_material = [
	{
		"id": "window-check",
		"name": "windowCheck<PERSON>it",
		"label": "form.additional_material.window_check"|tc,
		"help_text": "form.additional_material.window_check.help_text"|tc,
		"image": asset_path() ~ "/img/landing-pages/window-testing-kit.webp"
	},
	{
		"id": "catalog",
		"name": "glassWallsCatalog",
		"label": "form.additional_material.catalog"|tc,
		"help_text": "form.additional_material.catalog.help_text"|tc,
		"image": asset_path() ~ "/img/landing-pages/glass-walls-catalog.webp"
	}
] %}

<fieldset class="Fieldset">
	<legend>{{ "form.legend.additional_material"|tc }}</legend>
	<ul class="InformationRequestForm-formGroup">
		{% for material in additional_material %}
			{% set material_image %}
			{% include "@elements/image/image.twig" with {
					uri: material.image,
					height: 96,
					width: 96,
					alt: "",
				} only %}
			{% endset %}

			{% include "@elements/option-card/option-card.twig" with {
				as_list: true,
				asset: material_image,
				input: {
					type: "checkbox",
					name: material.name,
				},
				label: material.label,
				text: material.help_text,
			} only %}
		{% endfor %}
	</ul>
</fieldset>
