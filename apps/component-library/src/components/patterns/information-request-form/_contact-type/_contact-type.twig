{% set home_icon %}
{% include "@elements/icon/icon.twig" with {
    name: "home",
    size: "xxlarge",
		sprite_sheet: "landing-pages",
} only %}
{% endset %}

{% set location_icon %}
{% include "@elements/icon/icon.twig" with {
    name: "location",
    size: "xxlarge",
		sprite_sheet: "landing-pages",
} only %}
{% endset %}

{% set mail_icon %}
{% include "@elements/icon/icon.twig" with {
    name: "email",
    size: "xxlarge",
		sprite_sheet: "landing-pages",
} only %}
{% endset %}

{% set contact_types = [
	{
		"id": "on-site",
		"label": "form.contact_type.on_site"|tc,
		"help_text": "form.contact_type.on_site.help_text"|tc,
		"icon": home_icon
	},
	{
		"id": "showroom",
		"label": "form.contact_type.showroom"|tc,
		"help_text": "form.contact_type.showroom.help_text"|tc,
		"icon": location_icon
	},
	{
		"id": "information",
		"label": "form.contact_type.information"|tc,
		"help_text": "form.contact_type.information.help_text"|tc,
		"icon": mail_icon
	}
] %}

<fieldset class="Fieldset" id="information-request-form-contact-type">
	<legend>{{ "form.legend.contact_type"|tc }}</legend>
	<ul class="InformationRequestForm-formGroup">
		{% for type in contact_types %}
			{% include "@elements/option-card/option-card.twig" with {
				as_list: true,
				asset: type.icon,
				card_type: "rich",
				input: {
					checked: loop.first,
					name: "contactType",
					type: "radio",
					id: type.id,
				},
				label: type.label,
				text: type.help_text,
			} only %}
		{% endfor %}
	</ul>
</fieldset>
