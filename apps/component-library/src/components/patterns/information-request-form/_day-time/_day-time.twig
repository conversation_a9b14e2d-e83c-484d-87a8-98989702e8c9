{% set days = ["global.day_of_week.monday"|tc, "global.day_of_week.tuesday"|tc, "global.day_of_week.wednesday"|tc, "global.day_of_week.thursday"|tc, "global.day_of_week.friday"|tc, "global.day_of_week.saturday"|tc] %}
{% set time_of_day = ["global.time_of_day.morning"|tc, "global.time_of_day.mid_morning"|tc, "global.time_of_day.afternoon"|tc, "global.time_of_day.evening"|tc] %}

<fieldset class="Fieldset" id="day-time">
	<legend>Choose your preferred day and time of day</legend>
	<fieldset class="Fieldset-nested">
		<legend>Choose your preferred day<span aria-hidden="true">*</span></legend>
		<p class="Error" id="preferredDayError" hidden></p>
		<ul class="InformationRequestForm-formGroup">
			{% for day in days %}
				{% include "@elements/option-card/option-card.twig" with {
					as_list: true,
					input: {
						type: "checkbox",
						name: "preferredDay",
						value: day,
					},
					label: day
				} only %}
			{% endfor %}
		</ul>
	</fieldset>
	<fieldset class="Fieldset-nested">
		<legend>Choose your preferred time of day<span aria-hidden="true">*</span></legend>
		<p class="Error" id="preferredTimeError" hidden></p>
		<ul class="InformationRequestForm-formGroup">
			{% for time in time_of_day %}
				{% include "@elements/option-card/option-card.twig" with {
					as_list: true,
					input: {
						type: "checkbox",
						name: "preferredTime",
						value: time,
					},
					label: time
				} only %}
			{% endfor %}
		</ul>
	</fieldset>
</fieldset>
