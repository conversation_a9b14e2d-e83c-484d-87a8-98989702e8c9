/* @define InformationRequestForm; */

.InformationRequestForm {
	display: block;
	max-inline-size: 72rem;
	padding-block: var(--shared-spacing-sections-xl);

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	[type="radio"],
	[type="checkbox"] {
		margin: 0;
	}

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.Fieldset {
		margin-block-end: var(--size-48);

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		legend {
			font: var(--typo-HeadlineS);
			margin-block-end: var(--size-24);
			padding: 0;
		}

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		.Fieldset-nested {
			border: 0;
			padding: 0;

			/* stylelint-disable-next-line plugin/selector-bem-pattern */
			legend {
				font: var(--typo-TextM);
				margin-block-end: var(--Fieldset-nested-legend-block-end);
			}
		}

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		.Fieldset-nested:not(:last-of-type) {
			margin-block-end: var(--size-24);
		}
	}

	.InformationRequestForm-helpText {
		color: var(--shared-color-text-secondary);
		font-size: var(--typo-TextS-font-size);
	}

	.InformationRequestForm-formGroup {
		display: grid;
		gap: var(--InformationRequestForm-group-gap);
	}
}

@media (width < 48rem) {
	.InformationRequestForm {
		--InformationRequestForm-group-gap: var(--size-8);
		--Fieldset-nested-legend-block-end: var(--size-12);
	}
}

@media (width >= 48rem) {
	.InformationRequestForm {
		--InformationRequestForm-group-gap: var(--size-24);
		--Fieldset-nested-legend-block-end: var(--size-24);

		.InformationRequestForm-formGroup {
			grid-template-columns: repeat(auto-fit, minmax(17.5rem, 1fr));
		}
	}
}
