import { validateInformationRequestForm } from "../../../js/utils/helper/validators.js";

export class InformationRequestForm extends HTMLElement {
	static #selectors = {
		dayTimeFieldset: "#day-time",
		contactTypeFieldset: "#information-request-form-contact-type",
	};

	/**
	 * Retrieves the elements based on the defined selectors.
	 * @returns {object} An object containing the selected elements.
	 */
	#getElements() {
		return {
			dayTimeFieldset: this.querySelector(
				InformationRequestForm.#selectors.dayTimeFieldset,
			),
			contactTypeFieldset: this.querySelector(
				InformationRequestForm.#selectors.contactTypeFieldset,
			),
		};
	}

	#elements;

	/**
	 * Creates an instance of InformationRequestForm.
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();
		this.#addEventListeners();
	}

	/**
	 * Displays validation errors on the form.
	 * @param {object} errors - The errors object containing field errors.
	 */
	#displayErrors(errors) {
		const { fieldErrors } = errors;

		Object.keys(fieldErrors).forEach((field) => {
			const errorField = this.querySelector(`#${field}`);
			const errorElement = this.querySelector(`#${field}Error`);
			if (errorElement) {
				errorElement.textContent = fieldErrors[field][0];
				errorElement.hidden = false;
			}
			if (errorField) {
				errorField.setAttribute("aria-invalid", true);
			}
		});
	}

	/**
	 * Hides all error messages in the form.
	 */
	#hideAllErrors() {
		const errorFields = this.querySelectorAll("[aria-invalid=true]");
		const errorElements = this.querySelectorAll(".Error");
		errorFields.forEach((errorField) => {
			errorField.removeAttribute("aria-invalid");
		});
		errorElements.forEach((errorElement) => {
			errorElement.hidden = true;
			errorElement.textContent = "";
		});
	}

	/**
	 * Handles the form submission event, validates the form, displays errors, and submits the form data.
	 * @param {Event} event - The form submission event.
	 */
	async #handleFormSubmit(event) {
		event.preventDefault();

		const form = this.querySelector("form");
		this.#hideAllErrors();

		// deselects all day time options before submitting when day time should not be available.
		const { dayTimeFieldset } = this.#elements;
		let validationSchema;
		if (dayTimeFieldset.hidden) {
			validationSchema = "reduced";
			const options = Array.from(
				dayTimeFieldset.querySelectorAll("[type='checkbox']"),
			);

			options.forEach((option) => {
				if (option.checked) {
					option.checked = false;
				}
			});
		}

		const formData = new FormData(form);
		const constructedData = Object.fromEntries(formData);

		if (!dayTimeFieldset.hidden) {
			constructedData.preferredDay = formData.getAll("preferredDay");
			constructedData.preferredTime = formData.getAll("preferredTime");
		}

		const validation = validateInformationRequestForm(
			constructedData,
			validationSchema,
		);
		if (!validation.success) {
			this.#displayErrors(validation.errors);
			return;
		}

		try {
			const response = await fetch(form.action, {
				method: "POST",
				body: JSON.stringify(constructedData),
				headers: {
					"Content-Type": "application/json",
				},
			});
			if (response.ok) {
				console.log(response);
			}
		} catch (error) {
			throw new Error(`Error submitting form: ${error.message}`);
		}
	}

	/**
	 * Adds event listeners to information request form.
	 */
	#addEventListeners() {
		const { dayTimeFieldset, contactTypeFieldset } = this.#elements;
		const contactTypeOptions = Array.from(
			contactTypeFieldset.querySelectorAll("[type='radio']"),
		);
		const contactTypeIds = [];

		contactTypeOptions.forEach((option) => {
			contactTypeIds.push(option.id);
		});

		// we will handle validation manually, so we disable the browser's default validation.
		const form = this.querySelector("form");
		form.setAttribute("novalidate", "novalidate");

		this.addEventListener("change", (event) => {
			const target = event.target;

			// hides day time options when selecting "ordering info material".
			if (target.id === contactTypeIds[2]) {
				dayTimeFieldset.hidden = true;
			} else if (contactTypeIds.includes(target.id) && dayTimeFieldset.hidden) {
				dayTimeFieldset.hidden = false;
			}
		});
		this.addEventListener("submit", this.#handleFormSubmit);
	}
}

customElements.define("information-request-form", InformationRequestForm);
