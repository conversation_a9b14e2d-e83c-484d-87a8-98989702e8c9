{{ attach_library('finstral_global/pattern-information-request-form') }}

{% set form_error_messages = {
	street: "global.form.errors.js.field.required"|tc,
	postalCode: "global.form.errors.js.field.required"|tc,
	city: "global.form.errors.js.field.required"|tc,
	country: "global.form.errors.js.field.required"|tc,
	firstName: "global.form.errors.js.field.required"|tc,
	lastName: "global.form.errors.js.field.required"|tc,
	email: "global.form.errors.email.invalid"|tc,
	terms: "global.form.errors.data.processing.required"|tc,
	day: "global.form.errors.js.field.option.required"|tc,
	time: "global.form.errors.js.field.option.required"|tc,
} %}

<information-request-form class="InformationRequestForm u-container">
	<form name="information-request-form" action="{{ form_action }}" method="post">
		{% include "@patterns/information-request-form/_contact-type/_contact-type.twig" %}
		{% include "@patterns/information-request-form/_day-time/_day-time.twig" %}
		{% include "@patterns/information-request-form/_additional-material/_additional-material.twig" %}
		{% include "@patterns/forms/personal-information/personal-information.twig" %}
		{% include "@patterns/forms/address/address.twig" %}
		{% include "@patterns/forms/privacy/privacy.twig" %}
		{% include "@elements/button/button.twig" with {
			label: "form_contact_support.form_button"|tc,
			type: "submit",
		} only %}
	</form>
</information-request-form>

<script id="form-error-messages" type="application/json">{{ form_error_messages|json_encode()|raw }}</script>
