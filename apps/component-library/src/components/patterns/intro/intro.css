/** @define Intro; */

.Intro-content {
	display: flex;
	gap: var(--Intro-gap);
	padding-block: var(--shared-spacing-sections-lg);
}

.Intro-overline {
	margin-block-end: var(--Intro-overline-margin-end);
}

.Intro-heading {
	margin-block-end: var(--size-16);
	max-inline-size: 32ch;
	text-wrap: balance;
}

.Intro-copy {
	color: var(--shared-color-text-secondary);
}

.Intro--divider {
	border-block-end: var(--border-subtle);
}

/* mobile and tablet */
@media (width < 64rem) {
	.Intro-content {
		--Intro-overline-margin-end: var(--size-12);
		--Intro-gap: var(--size-32);

		flex-direction: column;
	}
}

/* desktop */
@media (width >= 64rem) {
	/* indent component on large viewports only */
	.Intro {
		padding-inline: var(--component-spacing-inline);
	}

	.Intro-content {
		--Intro-overline-margin-end: var(--size-16);
		--Intro-gap: var(--size-96);
	}
}
