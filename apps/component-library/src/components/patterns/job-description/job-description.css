/** @define JobDescription; */

.JobDescription {
	--JobDescription-content-grid-column: 1 / -1;

	padding-block: var(--JobDescription-padding-block, var(--size-128));
}

.JobDescription-content {
	grid-column: var(--JobDescription-content-grid-column);
}

.JobDescription-contact {
	padding-block-start: var(--JobDescription-contact-padding-block);
}

@media screen and (width < 48em) {
	.JobDescription-contact {
		--JobDescription-contact-padding-block: var(--size-32);
	}
}

@media screen and (width >= 48em) {
	.JobDescription-contact {
		--JobDescription-contact-padding-block: var(--size-40);
	}
}

.JobDescription-heading {
	margin-block-end: var(--size-16);
}

@media (48em > width) {
	.JobDescription {
		--JobDescription-padding-block: var(--size-48) var(--size-64);
	}
}

@media (64em > width) {
	.JobDescription-cta {
		display: none;
	}
}

@media (width >= 64em) {
	.JobDescription {
		--JobDescription-content-grid-column: 2 / -2;
	}

	.JobDescription-cta {
		margin-block-start: var(--size-112);
	}
}
