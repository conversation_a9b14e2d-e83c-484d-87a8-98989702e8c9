/** @define MediaContentBlock; */

.MediaContentBlock {
	--u-breakout-background: var(--theme-background-color);

	background-color: var(--theme-background-color);
	color: var(--theme-foreground-color);
	display: flex;
	gap: var(--MediaContentBlock-flex-gap);
	justify-content: center;
	padding-block-end: var(--MediaContentBlock-padding-block-end);
}

.MediaContentBlock-text {
	flex: 1 1 40%;
	padding-block-start: var(--size-48);
	padding-inline-end: var(--MediaContentBlock-text-padding-end);
	padding-inline-start: var(--MediaContentBlock-text-padding-start);
}

.MediaContentBlock-heading {
	margin-block-end: var(--MediaContentBlock-heading-margin-block-end);
}

.MediaContentBlock[data-theme="white"] .MediaContentBlock-copy {
	color: var(--shared-color-text-secondary);
}

.MediaContentBlock-cta {
	margin-block-start: var(--size-24);
}

.MediaContentBlock-media {
	aspect-ratio: 4 / 3;
	flex: 1 1 60%;
	max-inline-size: 56.375rem;
}

/* mobile */
@media (width < 48rem) {
	.MediaContentBlock {
		--MediaContentBlock-flex-gap: var(--size-48);
		--MediaContentBlock-padding-block-end: var(--size-96);
		--MediaContentBlock-heading-margin-block-end: var(--size-8);
		--MediaContentBlock-text-padding-end: var(--component-spacing-inline);
		--MediaContentBlock-text-padding-start: var(--component-spacing-inline);

		flex-direction: column;
	}

	.MediaContentBlock-media {
		padding-inline-end: var(--MediaContentBlock-text-padding-end);
	}

	.MediaContentBlock--reversed .MediaContentBlock-media {
		padding-inline-end: 0;
		padding-inline-start: var(--MediaContentBlock-text-padding-start);
	}
}

/* tablet and desktop */
@media (width >= 48rem) {
	.MediaContentBlock {
		--MediaContentBlock-flex-gap: var(--size-80);
		--MediaContentBlock-padding-block-end: var(--size-128);
		--MediaContentBlock-heading-margin-block-end: var(--size-16);
		--MediaContentBlock-text-padding-end: var(--component-spacing-inline);
		--MediaContentBlock-text-padding-start: 0;
	}

	.MediaContentBlock-text {
		align-self: center;
	}

	.MediaContentBlock--reversed {
		--MediaContentBlock-text-padding-end: 0;
		--MediaContentBlock-text-padding-start: var(--component-spacing-inline);

		flex-direction: row-reverse;
	}
}

/* large desktop: apply u-container sizing */
@media (width >= 100rem) {
	.MediaContentBlock {
		--MediaContentBlock-text-padding-end: 0;
		--MediaContentBlock-text-padding-start: 0;

		inline-size: min(
			100%,
			calc(var(--container-max-inline-size) + var(--component-spacing-inline))
		);
		margin-inline: auto;
	}
}
