{{ attach_library('finstral_global/pattern-media-content-block') }}

{% set cta_style = cta.style|default("button") %}
{% set cta_variant = cta.variant|default("secondary") %}
{% set cta_options = cta %}

{% if cta and cta_style == "button" %}
	{% set cta_options = cta_options|merge({variant: cta_variant,}) %}
{% endif %}

{% if cta and cta.external|default(false) %}
	{% set cta_options = cta_options|merge({icon: {name: "arrow_up", position: "end",},}) %}
{% endif %}

<div class="MediaContentBlock u-breakout {% if reversed %} MediaContentBlock--reversed{% endif %}" {% if color_scheme %} data-theme="{{ color_scheme }}" {% endif %}>
	<div class="MediaContentBlock-media">
		{{ media }}
	</div>
	<div class="MediaContentBlock-text">
		<h2 class="MediaContentBlock-heading">
			{{ heading }}
		</h2>
		{% include "@elements/rich-text/rich-text.twig" with {
			content: text,
			classes: ["u-typo-TextXL", "MediaContentBlock-copy"],
		} only %}

		<div class="MediaContentBlock-cta">
			{% if cta and cta_style == "button" %}
				{% include "@elements/button/button.twig" with cta_options only %}
			{% endif %}

			{% if cta and cta_style == "text" %}
				{% include "@elements/link/link.twig" with cta_options only %}
			{% endif %}
		</div>
	</div>
</div>
