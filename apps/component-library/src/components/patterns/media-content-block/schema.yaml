$schema: http://json-schema.org/draft-07/schema
$id: /patterns/media-content-block
additionalProperties: false
type: object
required:
  - heading
  - media
  - text
properties:
  heading:
    type: string

  media:
    type: string
    format: html
    description: elements/image or elements/video

  text:
    type: string
    format: html
    description: elements/rich-text

  cta:
    type: object
    required:
      - label
      - url
    additionalProperties: false
    properties:
      external:
        type: boolean
        default: false
        description: Indicates if the link is external
      label:
        type: string
        description: The text for the call to action button
      style:
        type: string
        enum:
          - text
          - button
        default: button
      variant:
        type: string
        enum:
          - primary
          - secondary
        default: secondary
      url:
        type: string
        format: uri-reference

  reversed:
    type: boolean

  color_scheme:
    type: string
    enum:
      - white
      - sandsteinlight
      - brand
