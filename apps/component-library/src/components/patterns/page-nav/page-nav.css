/** @define PageNav; */

.PageNav {
	--gap: var(--size-32);

	display: grid;
	gap: var(--gap);
	grid-template-areas: "previous next";
	grid-template-columns: repeat(2, min(20rem, calc(50% - var(--gap) / 2)));
	justify-content: space-between;
}

.PageNav-link {
	border: 0.0625rem solid var(--c-a);
	color: var(--c-a);
	font-weight: 500;
	padding-block: var(--input-button-padding-block);
	padding-inline: 1.5em;
	text-decoration: none;
	text-wrap: balance;
}

.PageNav-link:hover {
	background: var(--c-a);
	color: var(--c-typo-on-a);
}

.PageNav-link--previous {
	grid-area: previous;
}

.PageNav-link--next {
	grid-area: next;
	text-align: end;
}

.PageNav-linkLabel {
	display: block;
	font-weight: 400;
}

@media (width < 64em) {
	.PageNav-link {
		font-size: var(--typo-DefaultSmall-font-size);
		padding: 1rem;
	}
}
