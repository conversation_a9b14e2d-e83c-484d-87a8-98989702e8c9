/* @define Partner */

.Partner {
	--Partner-background: var(--shared-color-surface-sandstein-light);
	--u-breakout-background: var(--Partner-background);

	background-color: var(--Partner-background);
}

.Partner-directions {
	margin-block-start: var(--size-16);
}

.Partner-address,
.Partner-links {
	margin-block-start: var(--size-32);
}

.Partner-badges {
	align-items: end;
	display: grid;
	gap: var(--size-16);
	grid-template-columns: repeat(3, 5.625rem);
}

.Partner-badges,
.Partner-cta {
	margin-block-start: var(--size-48);
}

.Partner-gallery {
	--slider-navigation-inset-block: auto var(--size-16);
	--slider-track-gap: var(--size-4);
}

@media (48em > width) {
	.Partner {
		padding-block-end: var(--size-24);
		padding-block-start: var(--size-48);
	}

	.Partner-content,
	.Partner-gallery {
		grid-column: 1 / -1;
	}

	.Partner-badge {
		max-inline-size: var(--size-96);
	}

	.Partner-cta {
		/* Stretch CTA button to container width */
		display: grid;
	}

	.Partner-gallery {
		--slider-navigation-inset-inline: auto
			calc(var(--component-spacing-inline) * 2);
		--slider-track-padding-inline: var(--component-spacing-inline);
		--slider-slide-inline-size: calc(
			100cqi - var(--component-spacing-inline) * 2
		);

		margin-block-start: var(--size-48);
		margin-inline: calc(var(--component-spacing-inline) * -1);
	}
}

@media (width >= 48em) {
	.Partner {
		align-items: end;
		padding-block-start: var(--size-96);
	}

	.Partner-content {
		padding-block-end: var(--size-88);
	}

	.Partner-gallery {
		--slider-navigation-inset-inline: auto var(--component-spacing-inline);
		--slider-slide-inline-size: 100cqi;

		margin-inline-end: calc(var(--component-spacing-inline) * -1);
	}
}

@media (64em > width >= 48em) {
	.Partner-content {
		grid-column: 1 / 4;
		padding-inline-end: var(--size-32);
	}

	.Partner-gallery {
		grid-column: 4 / 9;
	}
}

@media (width >= 64em) {
	.Partner-content {
		grid-column: 1 / 4;
	}

	.Partner-gallery {
		grid-column: 6 / 13;
	}
}
