{{ attach_library('finstral_global/pattern-partner') }}

<article class="Partner u-container u-breakout u-grid">
	<div class="Partner-content">
		<div class="Partner-logo">
			{{ logo }}
		</div>

		<address class="Partner-address u-typo-TextM">
			{{ address }}
		</address>

		{% if route %}
			<div class="Partner-directions">
				{{ route }}
			</div>
		{% endif %}

		<ul class="Partner-links u-typo-TextM">
			{% for link in links %}
				<li class="Partner-link">{{ link }}</li>
			{% endfor %}
		</ul>

		{% if badges %}
			<ul class="Partner-badges">
				{% for badge in badges %}
					<li class="Partner-badge">
						{% include "@elements/image/image.twig" with {
							uri: asset_path() ~ "/img/certifications/" ~ badge ~ ".svg",
							is_lazy: true,
						} only %}
					</li>
				{% endfor %}
			</ul>
		{% endif %}

		{% if cta %}
			<div class="Partner-cta">
				{{ cta }}
			</div>
		{% endif %}
	</div>

	<div class="Partner-gallery">
		{% include "@elements/slider/slider.twig" with {
			label: "partner.gallery.heading"|tc,
			slides: slides,
		} only %}
	</div>
</article>
