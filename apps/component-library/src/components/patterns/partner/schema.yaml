$schema: http://json-schema.org/draft-07/schema
$id: /patterns/partner

type: object

required:
  - logo
  - address
  - links
  - route
  - slides

additional_properties: false

properties:
  logo:
    type: string
    format: html
    description: /elements/partner-logo

  address:
    type: string
    format: html

  links:
    type: array
    items:
      type: string
      format: html
      description: patterns/link

  route:
    type: string
    format: html
    description: /elements/link

  badges:
    type: array
    items:
      type: string

  cta:
    type: string
    format: html
    description: /elements/button

  slides:
    type: array
    items:
      type: string
      format: html
