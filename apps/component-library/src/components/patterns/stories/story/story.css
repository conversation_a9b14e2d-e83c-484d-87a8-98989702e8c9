/** @define Story; */

.Story {
	block-size: var(--Story-block-size);
}

.Story--withText {
	display: flex;
	flex-direction: column;
	justify-content: var(--Story-withText-justify-content, flex-start);
	padding-block: 5%;
}

.Story--withText:last-child {
	--Story-withText-justify-content: flex-end;
}

.Story:not(.Story--withText) {
	--Story-block-size: calc(100lvh - var(--header-min-block-size));

	inset-block-start: var(--header-min-block-size);
	position: sticky;
}

.Story-textBlock {
	align-items: flex-start;
	background: var(--shared-color-surface-sandstein-light);
	display: flex;
	flex-direction: column;
	gap: var(--size-16);
	grid-column: var(--Story-textBlock-grid-column);
	margin-inline: var(--Story-textBlock-margin-inline, 0);
	padding: var(--Story-textBlock-padding);
	position: relative;
}

.Story-heading {
	hyphens: auto;
}

.Story-copy {
	margin-block-end: var(--Story-copy-margin-block-end);
}

.Story-picture {
	block-size: 100%;
	display: block;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.Story img {
	block-size: 100%;
	inline-size: 100%;
	object-fit: cover;
}

@media (48em > width) {
	.Story {
		--Story-copy-margin-block-end: var(--size-8);
		--Story-textBlock-grid-column: 1 / -1;
		--Story-textBlock-margin-inline: 0
			calc(-1 * var(--component-spacing-inline));
		--Story-textBlock-padding: var(--size-24);
	}

	.Story:nth-child(4n) {
		--Story-textBlock-margin-inline: calc(-1 * var(--component-spacing-inline))
			0;
	}
}

@media (width >= 48em) {
	.Story {
		--Story-copy-margin-block-end: var(--size-16);
		--Story-textBlock-grid-column: 8 / -1;
		--Story-textBlock-padding: var(--size-48);
	}

	.Story:nth-child(4n) {
		--Story-textBlock-grid-column: 1 / 6;
	}
}

@media (48em <= width < 64em) {
	.Story {
		--Story-textBlock-grid-column: 5 / -1;
	}

	.Story:nth-child(4n) {
		--Story-textBlock-grid-column: 1 / 5;
	}
}

@media (64em > width) {
	.Story--withText {
		--Story-block-size: 90lvh;
	}
}

@media (width >= 64em) {
	.Story:not(.Story--withText) {
		--Story-block-size: calc(100lvh - var(--header-min-block-size));
	}

	.Story--withText {
		--Story-block-size: 120lvh;
	}
}
