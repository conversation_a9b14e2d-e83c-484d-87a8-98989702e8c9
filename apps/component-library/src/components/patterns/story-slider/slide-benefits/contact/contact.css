/** @define SlideBenefitContact; */

.SlideBenefitContact {
	align-items: center;
	display: grid;
	font-size: clamp(0.889rem, 5cqi, 1.125rem);
	gap: var(--size-16);
	grid-template-areas: "avatar details";
	grid-template-columns: minmax(var(--size-32), var(--size-80)) 60%;

	.SlideBenefitContact-avatar {
		grid-area: avatar;

		/* stylelint-disable-next-line plugin/selector-bem-pattern */
		img {
			border-radius: 50%;
		}
	}

	.SlideBenefitContact-details {
		grid-area: details;
	}
}
