$schema: http://json-schema.org/draft-07/schema
$id: patterns/story-slider/slide-benefits

type: object

required:
  - benefits
  - color_scheme
  - title

additionalProperties: false

properties:
  color_scheme:
    type: string
    enum:
      - white
      - jakobskraut
      - terrakotta
      - schwarzesche
      - latsche
      - schlern
      - estrich
      - le<PERSON>
      - sandstein
  overline:
    type: string
  title:
    type: string
  benefits:
    type: array
    items:
      type: string
  contact:
    format: html
    type: string
    description: /pattern/story-slider/slide-benefits/contact
