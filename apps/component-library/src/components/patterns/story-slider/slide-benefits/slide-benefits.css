@import "contact/contact.css";

/** @define StorySliderSlideBenefits; */

.StorySliderSlideBenefits {
	align-items: end;
	background-color: var(--theme-background-color);
	color: var(--theme-foreground-color);
	display: grid;
	grid-template-rows: auto 1fr;
	padding: var(--size-24);
}

.StorySliderSlideBenefits-content {
	display: grid;
	gap: var(--size-24);
}

.StorySliderSlideBenefits-item {
	align-items: center;
	display: flex;
	gap: var(--size-8);
}
