{% set label = "slide-benefits-" ~ random() %}

<section aria-labelledby="{{ label }}" aria-roledescription="slide" aria-brailleroledescription="sld" class="StorySliderSlideBenefits StorySlider-content" data-theme="{{ color_scheme }}" role="group">
	<div class="StorySliderSlideBenefits-content">
		<h2 class="StorySlider-secondaryTitle" id="{{ label }}">
			{%- if overline -%}
				<span class="StorySlider-slideOverline">{{ overline }}</span>
				<span class="visually-hidden">
					:
				</span>
			{% endif %}

			{{ title }}
		</h2>

		<ul class="StorySlider-list">
			{%- for benefit in benefits -%}
				<li class="StorySliderSlideBenefits-item">
					{% include "@elements/icon/icon.twig" with {
						classes: ["StorySliderSlideBenefits-icon"],
						name: "arrow_forward",
					} only %}
					{{ benefit }}
				</li>
			{%- endfor -%}
		</ul>
	</div>

	{{ contact }}
</section>
