/** @define StorySliderSlideImage; */

.StorySliderSlideImage {
	color: var(--shared-color-text-invert);
	display: grid;
	grid-template-areas: "main";
	place-items: center;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.StorySlider-slideOverlay,
.StorySliderSlideImage-content,
.StorySliderSlideImage-image {
	grid-area: main;
}

.StorySliderSlideImage-content {
	padding-inline: var(--size-32);
	position: relative;
	text-align: center;
}

.StorySliderSlideImage-image {
	aspect-ratio: 9 / 16;
	block-size: 100%;
	inline-size: 100%;
	object-fit: cover;
}
