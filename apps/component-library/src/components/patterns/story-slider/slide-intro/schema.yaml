$schema: http://json-schema.org/draft-07/schema
$id: /patterns/story-slider/slide-intro

type: object

required:
  - color_scheme
  - media
  - next_slide_button_label
  - title

additionalProperties: false

properties:
  color_scheme:
    type: string
    enum:
      - white
      - jakobskraut
      - terrakotta
      - schwarzesche
      - latsche
      - schlern
      - est<PERSON>
      - le<PERSON>
      - sandstein
  next_slide_button_label:
    type: string
  overline:
    type: string
  title:
    type: string
  media_type:
    type: string
    enum:
      - image
      - video
  media:
    format: html
    type: string
    description: /elements/video or /elements/image
