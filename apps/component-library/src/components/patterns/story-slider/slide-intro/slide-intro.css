/* stylelint-disable plugin/selector-bem-pattern */

/** @define StorySliderSlideIntro; */

.StorySliderSlideIntro {
	background-color: var(--theme-background-color);
	color: var(--theme-foreground-color);
	padding-inline-end: var(--size-32);
}

.StorySliderSlideIntro-introMedia {
	color: var(--shared-color-text-invert);
	display: grid;
	grid-template-areas: "content";
	place-items: end start;
}

.StorySliderSlideIntro-mediaOverlay {
	padding-block-end: var(--size-24);
	padding-inline: var(--size-24);
	position: relative;
	z-index: var(--bottom-layer);
}

.StorySliderSlideIntro-mediaOverlay,
.StorySliderSlideIntro-introImage,
.StorySliderSlideIntro-introVideo {
	grid-area: content;
}

.StorySliderSlideIntro-introImage {
	aspect-ratio: 9 / 16;
}

.StorySliderSlideIntro-nextSlide {
	align-items: center;
	display: flex;
	gap: var(--size-8);
	margin-block-start: var(--size-16);
	padding-inline-start: var(--size-24);
}
