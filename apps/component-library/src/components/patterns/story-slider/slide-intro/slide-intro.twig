{% set label = "slide-intro-" ~ random() %}

<section class="StorySliderSlideIntro StorySlider-content" aria-labelledby="{{ label }}" aria-roledescription="slide" aria-brailleroledescription="sld" data-theme="{{ color_scheme }}" role="group">
	<div class="StorySliderSlideIntro-introMedia">
		<div class="StorySliderSlideIntro-mediaOverlay">
			<h2 class="StorySlider-secondaryTitle" id="{{ label }}">
				{%- if overline -%}
					<span class="StorySlider-slideOverline">{{ overline }}</span>
					<span class="visually-hidden">
						:
					</span>
				{% endif %}

				{{ title }}
			</h2>
		</div>

		{% if media_type == "image" %}
			{{ media|mira({picture_classes: ["StorySliderSlideIntro-introImage"]}) }}
		{% elseif media_type == "video" %}
			{% include '@elements/video-custom-controls/video-custom-controls.twig' with {
				video: media,
				type: "decorative",
				classes: ["StorySliderSlideIntro-introVideo"]
			} only %}
		{% endif %}
	</div>

	<div class="StorySliderSlideIntro-footer">
		<button class="StorySliderSlideIntro-nextSlide" type="button">
			{{ next_slide_button_label }}
			{% include "@elements/icon/icon.twig" with {
				name: "arrow_forward",
			} only %}
		</button>
	</div>
</section>
