/** @define StorySliderSlideMixed; */

.StorySliderSlideMixed {
	background-color: var(--theme-background-color);
	color: var(--theme-foreground-color);
}

.StorySliderSlideMixed-image {
	aspect-ratio: 4 / 3;
	block-size: auto;
	inline-size: 100%;
	object-fit: cover;
	padding-inline-end: var(--size-24);
}

.StorySliderSlideMixed-content {
	display: grid;
	gap: var(--size-16);
	padding-block-start: clamp(5cqi, 2cqi, 15cqi);
	padding-inline: var(--size-24);
}

.StorySliderSlideMixed-bottom {
	display: grid;
	grid-template:
		"content" 2fr
		"media" 1fr;
	padding: 0;
	padding-inline-start: var(--size-24);
	place-items: start end;

	.StorySliderSlideMixed-image {
		grid-area: media;
		padding-inline-end: unset;
	}

	.StorySliderSlideMixed-content {
		grid-area: content;
		padding: 0;
		padding-block-start: clamp(5cqi, 2cqi, 15cqi);
		padding-inline-end: var(--size-24);
	}
}

.StorySliderSlideMixed-list {
	display: grid;
	font-size: var(--size-14);
	gap: var(--size-8);
	margin: 0;
}

.StorySliderSlideMixed-item {
	display: flex;
	gap: var(--size-8);
}
