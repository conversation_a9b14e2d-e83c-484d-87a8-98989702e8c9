{% set label = "slide-mixed-" ~ random() %}

<section class="StorySliderSlideMixed StorySliderSlideMixed-{{ image_position }} StorySlider-content" aria-labelledby="{{ label }}" aria-roledescription="slide" aria-brailleroledescription="sld" data-theme="{{ color_scheme }}" role="group">
	{{ image|mira({picture_classes: ["StorySliderSlideMixed-image"]}) }}

	<div class="StorySliderSlideMixed-content">
		<h2 class="StorySlider-secondaryTitle" id="{{ label }}">
			{%- if overline -%}
				<span class="StorySlider-slideOverline">{{ overline }}</span>
				<span class="visually-hidden">
					:
				</span>
			{% endif %}

			{{ title }}
		</h2>

		<ul class="StorySliderSlideMixed-list">
			{%- for item in list -%}
				<li class="StorySliderSlideMixed-item">
					<div class="StorySliderSlideMixed-icon">
						{% include "@elements/icon/icon.twig" with {
							name: "bulletpoint",
						} only %}
					</div>
					{{ item }}
				</li>
			{%- endfor -%}
		</ul>
	</div>
</section>
