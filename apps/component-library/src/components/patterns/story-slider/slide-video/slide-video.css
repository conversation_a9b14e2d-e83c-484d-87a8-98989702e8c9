/** @define StorySliderSlideVideo; */

.StorySliderSlideVideo {
	color: var(--shared-color-text-invert);
	display: grid;
	grid-template-areas: "main";
	place-items: center;
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.StorySlider-slideOverlay,
.StorySliderSlideVideo-content,
.StorySliderSlideVideo-video {
	grid-area: main;
}

.StorySliderSlideVideo-content {
	padding-inline: var(--size-32);
	position: relative;
	text-align: center;
	z-index: var(--middle-layer);
}
