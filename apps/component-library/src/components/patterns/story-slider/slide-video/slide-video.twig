{% set label = "slide-video-" ~ random() %}

<section class="StorySliderSlideVideo StorySlider-content" {% if title %} aria-labelledby="{{ label }}" {% endif %} aria-roledescription="slide" aria-brailleroledescription="sld" role="group">
	{%- if title -%}
		<div class="StorySlider-slideOverlay"></div>
		<div class="StorySliderSlideVideo-content">
			<h2 class="StorySlider-title" id="{{ label }}">
				{%- if overline -%}
					<span class="StorySlider-slideOverline">{{ overline }}</span>
					<span class="visually-hidden">
						:
					</span>
				{% endif %}

				{{ title }}
			</h2>
		</div>
	{%- endif -%}

	{% include '@elements/video-custom-controls/video-custom-controls.twig' with {
		video: video,
		classes: ["StorySliderSlideVideo-video"],
	} only %}
</section>
