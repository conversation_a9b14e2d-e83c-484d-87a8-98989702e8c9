/* stylelint-disable plugin/selector-bem-pattern */

/** @define StorySlider; */

@import "slide-benefits/slide-benefits.css";
@import "slide-image/slide-image.css";
@import "slide-video/slide-video.css";
@import "slide-mixed/slide-mixed.css";
@import "slide-intro/slide-intro.css";

.StorySlider {
	--StorySlider-background: var(--shared-color-surface-sandstein-light);
	--StorySlider-slidesTrack-gap: var(--size-24);
	--StorySlider-inactive-opacity: 0.3;
	--u-breakout-background: var(--StorySlider-background);

	background: var(--StorySlider-background);
	container: slider / size;
	display: block;
	max-inline-size: 100vw;
	overflow: hidden;
	padding-block: var(--shared-spacing-sections-lg);
	position: relative;
}

.StorySlider-js {
	/* Progress indicator */
	.StorySlider-progressIndicator {
		block-size: var(--size-8);
		display: flex;
		gap: var(--size-8);
		inline-size: 100%;
		margin-block-end: var(--size-16);
		overflow: hidden;
		transition: translate 0.5s ease;
		translate: 0;
	}

	.StorySlider-progressIndicatorItem {
		fill: var(--shared-color-surface-subtle-darker);
		flex: 1 1 100%;
	}

	.StorySlider-progressIndicatorItem.is-active {
		fill: var(--shared-color-surface-invert);
	}
}

/* Slides wrapper */
.StorySlider-slidesWrapper {
	position: relative;
	transition: translate 0.5s ease;
}

/* Slides track */
.StorySlider-slidesTrack {
	display: flex;
}

/* StorySlider slide */
.StorySlider-slide {
	opacity: var(--StorySlider-inactive-opacity);
	position: relative;

	@media (width < 48rem) {
		padding-inline-start: var(--size-24);
		scroll-snap-align: start;

		&:last-of-type {
			padding-inline-end: var(--size-32);
		}
	}

	/* Slide active state */
	&.is-active {
		opacity: 1;
		transition: opacity 1s ease;
	}
}

.StorySlider-slideOverlay {
	align-items: center;
	aspect-ratio: 9 / 16;
	background-color: var(--shared-color-surface-subtle-darker);
	block-size: 100%;
	inline-size: 100%;
	justify-items: center;
	position: relative;
}

/* Slide content */
.StorySlider-content {
	aspect-ratio: 9 / 16;
	block-size: clamp(30rem, 80vh, 45rem);
	container-type: inline-size;
	inline-size: auto;
}

/* StorySlider slide typography */

.StorySlider-slideOverline,
.StorySlider-secondaryTitle {
	text-wrap: balance;
}

.StorySlider-slideOverline {
	display: block;
	font-size: clamp(1rem, 4.5cqi, 1.3125rem);
	margin-block-end: var(--size-12);
}

.StorySlider-title {
	font-size: clamp(1.625rem, 9.25cqi, 2.25rem);
}

.StorySlider-secondaryTitle {
	font-size: clamp(1.3125rem, 7.5cqi, 1.802rem);
}

.StorySlider-list {
	display: grid;
	font-size: clamp(1rem, 5.2cqi, 1.266rem);
	gap: clamp(var(--size-8), 4.5cqi, var(--size-16));
}

@media (prefers-contrast: more) {
	.StorySlider-slideOverlay {
		background-color: var(--shared-color-surface-overlay-dialog);
	}
}

@media (prefers-reduced-motion: reduce) {
	.StorySlider-progressIndicator {
		transition: none;
	}

	.StorySlider-slidesWrapper {
		transition: none;
	}
}

/* StorySlider Mobile */
@media (width < 48rem) {
	.StorySlider-slidesTrack {
		overflow: auto hidden;
		padding-inline-start: var(--size-16);
		scroll-snap-type: inline mandatory;
		scrollbar-width: none;
	}

	.StorySlider-slidesTrack::-webkit-scrollbar {
		display: none;
	}

	.StorySlider-js {
		.StorySlider-controlsWrapper {
			display: none;
		}
	}
}

@media (width < 64rem) {
	.StorySlider {
		--StorySlider-slidesTrack-gap: var(--size-12);
		--StorySlider-inactive-opacity: 0.5;
		block-size: 100lvb;
	}

	.StorySlider-js {
		.StorySlider-progressIndicator {
			padding-inline-start: var(--size-24);
		}
	}
}

/* StorySlider Desktop */
@media (width >= 48rem) {
	.StorySlider-slidesTrack {
		gap: var(--StorySlider-slidesTrack-gap);
	}

	.StorySlider-js {
		.StorySlider-controlsWrapper {
			align-items: center;
			display: flex;
			inset: 0;
			max-block-size: 100vh;
			max-inline-size: 100vw;
			padding-inline: var(--size-32);
			position: absolute;
		}

		.StorySlider-controls {
			display: grid;
			gap: var(--size-4);
			grid-template-areas: "prev next";
			inline-size: 100%;
			justify-content: space-between;
			z-index: var(--bottom-layer);
		}

		/* StorySlider Control */
		.StorySlider-control {
			background-color: var(--shared-color-surface-primary);
			block-size: var(--size-32);
			box-sizing: content-box;
			inline-size: var(--size-32);
			padding: var(--size-8);

			&[disabled] svg {
				color: var(--prim-color-neutral-500);
			}

			svg {
				block-size: 100%;
				inline-size: 100%;
			}
		}
	}
}

@media (width >= 64rem) {
	.StorySlider {
		min-block-size: 100vh;
	}
}
