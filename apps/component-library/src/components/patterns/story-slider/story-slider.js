export default class StorySlider extends HTMLElement {
	static #selectors = {
		storySlide: ".StorySlider-slide",
		storySlideActive: ".is-active",
		storySlideVideo: ".StorySliderSlideVideo",
		storySliderContainer: ".StorySlider-slidesWrapper",
		storySliderControl: ".StorySlider-control",
		storySliderIntroSlideButton: ".StorySliderSlideIntro-nextSlide",
		storySliderProgressIndicator: ".StorySlider-progressIndicator",
		storySliderProgressIndicatorItems: ".StorySlider-progressIndicatorItem",
		storySliderTrack: ".StorySlider-slidesTrack",
		videoCustomControlsComponent: "video-custom-controls",
	};

	#elements;

	#state = {
		isMobile: false,
		slideCounter: 1,
		slideContainerStartPosition: window.innerWidth / 2.5,
		slideContainerNextPosition: 0,
		slideWidth: 0,
	};

	#STORY_SLIDER_CONSTANTS = {
		mql: window.matchMedia("(width < 48rem)"), // 768px
		next: "next",
		previous: "previous",
		slideGap: 24, // 24px
	};

	/**
	 * Represents a StorySlider component.
	 * @class
	 */
	constructor() {
		super();

		this.#elements = this.#getElements();

		if (
			this.#elements.storySliderControls.length &&
			this.#elements.storySlides.length &&
			this.#elements.storySliderProgressIndicator
		) {
			this.#init();
		}
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getElements() {
		return {
			storySliderContainer: this.querySelector(
				StorySlider.#selectors.storySliderContainer,
			),
			storySliderControls: document.querySelectorAll(
				StorySlider.#selectors.storySliderControl,
			),
			storySlides: this.querySelectorAll(StorySlider.#selectors.storySlide),
			storySliderIntroSlideButton: this.querySelector(
				StorySlider.#selectors.storySliderIntroSlideButton,
			),
			storySliderProgressIndicator: this.querySelector(
				StorySlider.#selectors.storySliderProgressIndicator,
			),
			storySliderProgressIndicatorItems: this.querySelectorAll(
				StorySlider.#selectors.storySliderProgressIndicatorItems,
			),
			storySliderTrack: this.querySelector(
				StorySlider.#selectors.storySliderTrack,
			),
		};
	}

	/**
	 * Initializes the StorySlider component and shows the StorySlider UI.
	 * @returns {void}
	 * @private
	 */
	#init() {
		const [previousButton, nextButton] = this.#elements.storySliderControls;
		const progressIndicator = this.#elements.storySliderProgressIndicator;
		const { storySliderContainer } = this.#elements;

		const slide = this.#elements.storySlides[0];
		const individualSlideWidth = slide.getBoundingClientRect().width;

		this.classList.add("StorySlider-js");

		progressIndicator.removeAttribute("hidden");
		nextButton.removeAttribute("hidden");
		previousButton.removeAttribute("hidden");
		previousButton.disabled = true;

		this.#state.slideWidth =
			individualSlideWidth + this.#STORY_SLIDER_CONSTANTS.slideGap;

		progressIndicator.style.inlineSize = `${individualSlideWidth}px`;

		if (this.#STORY_SLIDER_CONSTANTS.mql.matches) {
			this.#state.isMobile = true;
			this.#initForScrollSnap();
		} else {
			storySliderContainer.style.translate = `${this.#state.slideContainerStartPosition}px`;
			progressIndicator.style.translate = `${this.#state.slideContainerStartPosition}px`;
			this.#slideResizeObserver();
		}

		this.#addEventListeners();
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#getSlideBoundingRect() {
		// all slides have the same boundingClientRect so we simply
		// default to the first slide.
		const slides = this.#elements.storySlides;
		return slides[0].getBoundingClientRect();
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setWindowApproximateCenter() {
		this.#state.slideContainerStartPosition =
			parseInt(window.innerWidth, 10) / 2.5;
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setSlideWidthWithGap() {
		const slideWidth = this.#getSlideBoundingRect().width;
		this.#state.slideWidth = slideWidth + this.#STORY_SLIDER_CONSTANTS.slideGap;
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#setNextSliderPosition() {
		const sliderProgress =
			this.#state.slideWidth * (this.#state.slideCounter - 1);
		this.#state.slideContainerNextPosition = `${this.#state.slideContainerStartPosition - sliderProgress}px`;
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#updateStorySliderPosition() {
		const progressIndicator = this.#elements.storySliderProgressIndicator;
		const { storySliderContainer } = this.#elements;
		storySliderContainer.style.translate =
			this.#state.slideContainerNextPosition;
		progressIndicator.style.translate = `${this.#state.slideContainerStartPosition}px`;
	}

	/**
	 * Checks if a slide is a video slide.
	 * @param {Element} slide - The slide element to check.
	 * @private
	 * @returns {boolean} - True if the slide is a video slide, false otherwise.
	 */
	#isVideoSlide(slide) {
		return slide.querySelector(StorySlider.#selectors.storySlideVideo) !== null;
	}

	/**
	 * Handles window resize events to adjust the position of the story slider container
	 * and the progress indicator based on the window's approximate center and the active slide's
	 * left boundary.
	 * @private
	 * @returns {void}
	 */
	#handleWindowResizeEvents() {
		this.#setWindowApproximateCenter();
		this.#setSlideWidthWithGap();
		this.#setNextSliderPosition();
		this.#updateStorySliderPosition();
	}

	/**
	 * Handles resize events for the story slider component.
	 *
	 * This method sets up event listeners and observers to adjust the layout and
	 * progress indicator of the story slider when the window is resized. It uses
	 * the ResizeObserver API to monitor changes in the size of the slides and
	 * updates the relevant styles accordingly.
	 * @private
	 * @returns {void}
	 */
	#slideResizeObserver() {
		const progressIndicator = this.#elements.storySliderProgressIndicator;
		const slides = this.#elements.storySlides;

		const resizeObserver = new ResizeObserver((entries) => {
			entries.forEach(() => {
				this.#setSlideWidthWithGap();
				this.#setWindowApproximateCenter();
				this.#setNextSliderPosition();

				progressIndicator.style.inlineSize = `${this.#getSlideBoundingRect().width}px`;
				this.#updateStorySliderPosition();
			});
		});

		// It does not matter which slide we observe as all
		// slides will resize in the same manner at the
		// same time.
		resizeObserver.observe(slides[0]);
	}

	/**
	 * Pauses the video slide if it was playing.
	 * @param {HTMLElement} slide - The active slide element.
	 * @param {string} action - The action to perform on the video slide. Can be 'play' or 'pause'.
	 * @private
	 * @returns {void}
	 */
	#toggleVideoSlidePlayback(slide, action) {
		const allVideos = slide.querySelectorAll("video");

		let isVideoPaused;

		allVideos.forEach((video) => {
			if (video.checkVisibility()) {
				isVideoPaused = video.paused;
			}
		});

		if (
			(isVideoPaused && action === "pause") ||
			(!isVideoPaused && action === "play")
		) {
			return;
		}

		const mediaControlEvent = new CustomEvent("mediacontrol", {
			detail: {
				action: "playback",
			},
		});
		const videoWebComponent = slide.querySelector(
			StorySlider.#selectors.videoCustomControlsComponent,
		);
		videoWebComponent.dispatchEvent(mediaControlEvent);
	}

	/**
	 * Updates the progress indicator of the story slider.
	 * @param {number} [slideIndex] - The index of the active slide. [default=1]
	 * @private
	 * @returns {void}
	 */
	#updateProgress(slideIndex = 1) {
		this.#elements.storySliderProgressIndicatorItems.forEach((item) => {
			item.classList.remove("is-active");
		});

		for (let i = 0; i < slideIndex; i += 1) {
			this.#elements.storySliderProgressIndicatorItems[i].classList.add(
				"is-active",
			);
		}
	}

	/**
	 * Handles the change of the intersecting element. If the previous active slide was
	 * a video slide, it will pause the video.
	 * @param {IntersectionObserverEntry} entry - The entry of the intersecting element.
	 * @private
	 * @returns {void}
	 */
	#handleIntersectingElementChange(entry) {
		const { storySliderContainer } = this.#elements;
		const previouslyActiveSlide = storySliderContainer.querySelector(
			StorySlider.#selectors.storySlideActive,
		);

		previouslyActiveSlide.classList.toggle(
			StorySlider.#selectors.storySlideActive.slice(1),
		);

		entry.target.classList.toggle(
			StorySlider.#selectors.storySlideActive.slice(1),
		);

		this.#updateProgress(parseInt(entry.target.dataset.index, 10));

		if (this.#isVideoSlide(previouslyActiveSlide)) {
			this.#toggleVideoSlidePlayback(previouslyActiveSlide, "pause");
		}

		if (this.#isVideoSlide(entry.target)) {
			this.#toggleVideoSlidePlayback(entry.target, "play");
		}
	}

	/**
	 * Handles swipe transitions for the slides and handles slide intersection events.
	 */
	#initForScrollSnap() {
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						this.#handleIntersectingElementChange(entry);
					}
				});
			},
			{ threshold: 0.75 },
		);

		this.#elements.storySlides.forEach((slide) => {
			observer.observe(slide);
		});
	}

	/**
	 * Moves the slide container in the specified direction.
	 * @param {string} direction - The direction to move the container. Can be 'next' or 'previous'.
	 * @returns {void}
	 * @private
	 */
	#moveSlide(direction) {
		const slides = this.#elements.storySlides;
		const { storySliderContainer } = this.#elements;

		const previouslyActiveSlide = storySliderContainer.querySelector(
			StorySlider.#selectors.storySlideActive,
		);

		previouslyActiveSlide.classList.toggle(
			StorySlider.#selectors.storySlideActive.slice(1),
		);

		if (this.#isVideoSlide(previouslyActiveSlide)) {
			this.#toggleVideoSlidePlayback(previouslyActiveSlide, "pause");
		}

		this.#state.slideCounter =
			direction === this.#STORY_SLIDER_CONSTANTS.next
				? this.#state.slideCounter + 1
				: this.#state.slideCounter - 1;

		this.#setNextSliderPosition();

		const nextActiveSlide = slides[this.#state.slideCounter - 1];

		if (nextActiveSlide) {
			if (this.#isVideoSlide(nextActiveSlide)) {
				this.#toggleVideoSlidePlayback(nextActiveSlide, "play");
			}

			nextActiveSlide.classList.toggle(
				StorySlider.#selectors.storySlideActive.slice(1),
			);
		}

		this.#updateProgress(this.#state.slideCounter);
		storySliderContainer.style.translate =
			this.#state.slideContainerNextPosition;
	}

	/**
	 * Updates the state of the buttons in the story slider.
	 */
	#updateButtonState() {
		const [previousButton, nextButton, replay] =
			this.#elements.storySliderControls;

		const isFirstSlide = this.#state.slideCounter === 1;
		const isLastSlide =
			this.#state.slideCounter === this.#elements.storySlides.length;

		replay.toggleAttribute("hidden", true);

		if (isFirstSlide) {
			previousButton.disabled = true;
			nextButton.hidden = false;
			nextButton.focus();
		} else if (isLastSlide) {
			nextButton.hidden = true;
			replay.hidden = false;
			replay.focus();
		} else {
			nextButton.hidden = false;
			previousButton.disabled = false;
		}
	}

	/**
	 * Handles next and previous keyboard events for the story slider.
	 * @param {Event} event - The keyboard event.
	 * @private
	 * @returns {void}
	 */
	#handleKeyboardEvents(event) {
		const [previousButton, nextButton] = this.#elements.storySliderControls;

		const hasPreviousSlide = this.#state.slideCounter > 0;
		const isLastSlide =
			this.#state.slideCounter === this.#elements.storySlides.length;
		const isStorySliderControl = document.activeElement.classList.contains(
			StorySlider.#selectors.storySliderControl.slice(1),
		);

		if (event.key === "ArrowRight" && isStorySliderControl && !isLastSlide) {
			nextButton.focus();
			this.#moveSlide(this.#STORY_SLIDER_CONSTANTS.next);
			this.#updateButtonState();
		}

		if (event.key === "ArrowLeft" && isStorySliderControl && hasPreviousSlide) {
			previousButton.focus();
			this.#moveSlide(this.#STORY_SLIDER_CONSTANTS.previous);
			this.#updateButtonState();
		}
	}

	/**
	 * Adds event listeners to the next and previous buttons of the story slider.
	 * @returns {void}
	 * @private
	 */
	#addEventListeners() {
		const [previousButton, nextButton, replay] =
			this.#elements.storySliderControls;
		const { storySliderContainer, storySliderProgressIndicator } =
			this.#elements;
		const storySliderNextSlideButton =
			this.#elements.storySliderIntroSlideButton;

		let resizeController = new AbortController();

		this.addEventListener("keyup", (event) => {
			this.#handleKeyboardEvents(event);
		});

		// if the page is initially loaded on a desktop device, we need to add the resize event listener
		if (!this.#state.isMobile) {
			window.addEventListener(
				"resize",
				this.#handleWindowResizeEvents.bind(this),
				{
					signal: resizeController.signal,
				},
			);
		}

		// we also need to add a MediaQueryList listener so we can make the needed adjustments
		// as the viewports changes from desktop to mobile and vice versa
		this.#STORY_SLIDER_CONSTANTS.mql.addEventListener("change", (event) => {
			if (event.matches) {
				storySliderContainer.style.translate = 0;
				storySliderProgressIndicator.style.translate = 0;

				if (resizeController) {
					resizeController.abort();
				}
			} else {
				this.#setNextSliderPosition();
				this.#updateStorySliderPosition();

				resizeController = new AbortController();
				window.addEventListener(
					"resize",
					this.#handleWindowResizeEvents.bind(this),
					{
						signal: resizeController.signal,
					},
				);
			}
		});

		if (storySliderNextSlideButton && this.#elements.storySlides[1]) {
			const slideWidthInPx =
				this.#state.slideWidth * 16 -
				this.#STORY_SLIDER_CONSTANTS.slideGap * 16;

			storySliderNextSlideButton.addEventListener("click", () => {
				if (this.#state.isMobile) {
					this.#elements.storySliderTrack.scrollBy({
						behavior: "smooth",
						left: slideWidthInPx,
						top: 0,
					});
				} else {
					this.#moveSlide(this.#STORY_SLIDER_CONSTANTS.next);
					this.#updateButtonState();
				}
			});
		}

		if (nextButton) {
			nextButton.addEventListener("click", () => {
				this.#moveSlide(this.#STORY_SLIDER_CONSTANTS.next);
				this.#updateButtonState();
			});
		}

		if (previousButton) {
			previousButton.addEventListener("click", () => {
				this.#moveSlide(this.#STORY_SLIDER_CONSTANTS.previous);
				this.#updateButtonState();
			});
		}

		if (replay) {
			replay.addEventListener("click", () => {
				const customVideoComponents = this.querySelectorAll(
					StorySlider.#selectors.videoCustomControlsComponent,
				);

				storySliderContainer.style.translate = `${this.#state.slideContainerStartPosition}px`;

				this.#elements.storySlides[
					this.#state.slideCounter - 1
				].classList.toggle(StorySlider.#selectors.storySlideActive.slice(1));

				this.#state.slideCounter = 1;

				this.#elements.storySlides[0].classList.toggle(
					StorySlider.#selectors.storySlideActive.slice(1),
				);

				// we need to pause any video that is currently playing but,
				// which is not a decorative video
				if (customVideoComponents.length) {
					Array.from(customVideoComponents).forEach((videoComponent) => {
						const isDecorative =
							videoComponent.getAttribute("type") === "decorative";

						if (!isDecorative) {
							this.#toggleVideoSlidePlayback(videoComponent.parentElement);
						}
					});
				}

				this.#updateProgress();
				this.#updateButtonState();
			});
		}
	}
}

customElements.define("story-slider", StorySlider);
