{{ attach_library('finstral_global/pattern-story-slider') }}

{% set id = "slider-" ~ random() %}
{% set label = id ~ "-heading" %}
{% set track_id = id ~ "-track" %}

<story-slider aria-labelledby="{{ label }}" aria-roledescription="{{ "story_slider.role_description"|tc }}" aria-brailleroledescription="sldshw" class="StorySlider" role="group">
	<span id="{{ label }}" hidden>{{ "story_slider.section_heading"|tc }}</span>
	<div class="StorySlider-controlsWrapper">
		<div class="StorySlider-controls">
			<button aria-controls="{{ track_id }}" class="StorySlider-control StorySlider-control--prev" type="button" hidden>
				{% include "@elements/icon/icon.twig" with {
					name: 'chevron_left',
				} only %}
				<span class="visually-hidden">{{ "slider.previous"|tc }}</span>
			</button>

			<button aria-controls="{{ track_id }}" class="StorySlider-control StorySlider-control--next" type="button" hidden>
				{% include "@elements/icon/icon.twig" with {
					name: 'chevron_right',
				} only %}
				<span class="visually-hidden">{{ "slider.next"|tc }}</span>
			</button>

			<button aria-controls="{{ track_id }}" class="StorySlider-control StorySlider-control--replay" type="button" hidden>
				{% include "@elements/icon/icon.twig" with {
					name: 'replay',
				} only %}
				<span class="visually-hidden">{{ "slider.replay"|tc }}</span>
			</button>
		</div>
	</div>
	<div class="StorySlider-progressIndicator" hidden>
		{% for slide in slides %}
			<svg class="StorySlider-progressIndicatorItem {% if loop.first %} is-active {% endif %}" fill="#333" viewbox="0 0 200 8" xmlns="http://www.w3.org/2000/svg">
				<rect x="0" y="0" width="100%" height="100%"/>
			</svg>
		{% endfor %}
	</div>
	<div class="StorySlider-slidesWrapper">
		<ul class="StorySlider-slidesTrack" id="{{ track_id }}">
			{%- for slide in slides -%}
				<li class="StorySlider-slide {% if loop.first -%} is-active {%- endif -%}" data-index="{{ loop.index }}">
					{{ slide }}
				</li>
			{%- endfor -%}
		</ul>
	</div>
</story-slider>
