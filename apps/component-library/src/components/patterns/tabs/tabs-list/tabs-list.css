/** @define TabsList; */

.TabsList {
	align-items: flex-end;
	container: tabs-list / inline-size;
	display: flex;
	gap: var(--size-32);
}

.TabsList:not(:has(+ .Tabs-tabpanels)) {
	margin-block-end: var(--size-32);
}

.TabsList-tab {
	block-size: 100%;
	border-block-end: var(--shared-border-width-md) solid transparent;
	color: var(--shared-color-text-secondary);
	display: block;
	padding-block: 0.5em;
	text-align: start;
	text-decoration: none;
}

.TabsList-tab:is([aria-selected="true"], [aria-current="page"], .is-active) {
	border-block-end-color: currentColor;
	color: var(--c-typo-primary);
}

@container tabs-list (width < 36em) {
	.TabsList-tab {
		font: var(--typo-HeadingXXXS);
	}
}

@container tabs-list (width >= 36em) {
	.TabsList-tab {
		font: var(--typo-HeadingXXS);
	}
}
