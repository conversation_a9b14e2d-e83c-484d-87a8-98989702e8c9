class TabsList {
	/**
	 * @param {HTMLElement} element
	 * @param {object} Tabs
	 */
	constructor(element, Tabs) {
		this.element = element;
		this.Tabs = Tabs;
		this.elements = {
			tabs: Array.from(this.element.querySelectorAll("button")),
		};

		this.elements.tabs.forEach((button) => {
			button.addEventListener("click", this.#onClick.bind(this));
			button.addEventListener("keydown", this.#onKeydown.bind(this));
		});
	}

	/**
	 * @param {Event} object
	 * @param {HTMLButtonElement} object.currentTarget
	 */
	#onClick({ currentTarget }) {
		this.Tabs.setActiveTab(this.elements.tabs.indexOf(currentTarget));
	}

	/**
	 * @param {Event} event
	 */
	#onKeydown(event) {
		const dir = event.target.closest("[dir]")?.dir ?? "ltr";
		let flag = false;

		switch (event.key) {
			case "ArrowLeft":
				if (dir === "rtl") {
					this.#setNextTab();
				} else {
					this.#setPreviousTab();
				}
				flag = true;
				break;

			case "ArrowRight":
				if (dir === "rtl") {
					this.#setPreviousTab();
				} else {
					this.#setNextTab();
				}
				flag = true;
				break;

			case "Home":
				this.Tabs.setActiveTab(0);
				flag = true;
				break;

			case "End":
				this.Tabs.setActiveTab(this.elements.tabs.length - 1);
				flag = true;
				break;

			default:
				break;
		}

		if (flag) {
			event.stopPropagation();
			event.preventDefault();
		}
	}

	/**
	 * @returns {void}
	 */
	#setNextTab() {
		this.Tabs.setActiveTab(
			this.Tabs.state.active === this.elements.tabs.length - 1
				? 0
				: this.Tabs.state.active + 1,
		);
	}

	/**
	 * @returns {void}
	 */
	#setPreviousTab() {
		this.Tabs.setActiveTab(
			this.Tabs.state.active === 0
				? this.elements.tabs.length - 1
				: this.Tabs.state.active - 1,
		);
	}

	/**
	 * @returns {void}
	 */
	render() {
		this.elements.tabs.forEach((button, i) => {
			if (i === this.Tabs.state.active) {
				button.setAttribute("aria-selected", "true");
				button.removeAttribute("tabindex");
				button.focus();
			} else {
				button.setAttribute("aria-selected", "false");
				button.setAttribute("tabindex", -1);
			}
		});
	}
}

export default TabsList;
