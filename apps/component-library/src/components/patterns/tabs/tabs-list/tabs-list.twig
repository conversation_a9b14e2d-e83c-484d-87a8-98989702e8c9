<ol class="TabsList"{% if is_interactive %} role="tablist"{% endif %}>
	{% for tab in tabs %}
		{% if is_interactive %}
			<li role="presentation">
				<button
					type="button"
					class="TabsList-tab"
					role="tab"
					aria-controls="{{ tab.id }}"
					aria-selected="{% if tab.is_selected %}true{% else %}false{% endif %}"
					{% if not tab.is_selected %}tabindex="-1"{% endif %}
				>{{ tab.title }}</button>
			</li>
		{% else %}
			<li>
				<a
					class="TabsList-tab"
					href="{{ tab.url }}"
					aria-current="{% if tab.is_selected %}page{% else %}false{% endif %}"
				>{{ tab.title }}</a>
			</li>
		{% endif %}
	{% endfor %}
</ol>
