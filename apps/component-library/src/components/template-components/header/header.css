/** @define Header; */

.Header {
	align-items: center;
	background: var(--Header-background, var(--shared-color-surface-primary));
	border-block-end: var(--Header-border-block-end-width, 0) solid
		var(--shared-color-border-subtle-transparent);
	display: flex;
	flex-direction: var(--Header-flex-direction, row);
	gap: var(--Header-gap, var(--size-24));
	inset: 0 0 auto;
	justify-content: var(--Header-justify-content, start);
	min-block-size: var(--header-min-block-size);
	padding: var(--Header-padding-block, var(--size-24))
		var(--Header-padding-inline, var(--component-spacing-inline));
	position: var(--Header-position);
	transition:
		background-color var(--default-transition-duration),
		padding var(--default-transition-duration);
	z-index: var(--top-layer);
}

.Header--transparent {
	--Header-background: linear-gradient(
		180deg,
		rgba(0, 0, 0, 0.2) 0%,
		rgba(0, 0, 0, 0) 84.37%
	);
	--Header-logoImage-filter: brightness(0) invert(1);
	--Header-menuIcon-color: var(--shared-color-text-invert);
	--Header-position: fixed;
}

.Header:not(.Header--transparent) {
	--Header-logoImage-filter: none;
	--Header-menuIcon-color: var(--shared-color-text-primary);
	--Header-position: sticky;
}

.Header--disableSticky {
	--Header-position: absolute;
}

.Header.is-sticky {
	--Header-background: var(--shared-color-surface-primary);
	--Header-border-block-end-width: 0.0625rem;
	--Header-logoImage-filter: none;
	--Header-menuIcon-color: var(--shared-color-text-primary);
	--Header-padding-block: var(--size-16);
}

.Header:has(.SideNav.is-open) {
	--Header-logoImage-filter: none;
	--Header-menuIcon-color: var(--shared-color-text-primary);
}

.Header-logoImage {
	block-size: var(--Header-logoImage-block-size, auto);
	filter: var(--Header-logoImage-filter, none);
	inline-size: auto;
	transition: filter var(--default-transition-duration);
}

.Header-menuIcon {
	color: var(--Header-menuIcon-color, var(--shared-color-text-invert));
	transition: color var(--default-transition-duration);
}

.Header-sideNavButton,
.Header-logoLink {
	z-index: var(--Header-entries-z-index);
}

.Header-logoLink {
	flex-shrink: 0;
	margin-right: auto;
}

@media (width < 48rem) {
	.Header-sideNavButton,
	.Header-logoLink {
		--Header-entries-z-index: var(--middle-layer);
	}
}

@media (width >= 48rem) {
	.Header-sideNavButton,
	.Header-logoLink {
		--Header-entries-z-index: var(--top-layer);
	}
}

.Header-sideNavButton[aria-expanded="true"] .Header-menuIconOpen,
.Header-sideNavButton[aria-expanded="false"] .Header-menuIconClose {
	display: none;
}

.Header-sideNavButton[aria-expanded="false"] .Header-menuIconOpen,
.Header-sideNavButton[aria-expanded="true"] .Header-menuIconClose {
	display: block;
}

.Header-button {
	min-block-size: var(--size-48);
}

@media (width >= 48rem) {
	.Header-cardUserProfileCta {
		align-items: center;
		display: flex;
		gap: var(--size-48);
	}
}

@media (width <= 20rem) {
	.Header {
		--Header-padding-inline: var(--size-12);
		--Header-gap: var(--size-12);
	}
}

@media (64em > width) {
	.Header {
		--Header-flex-direction: row-reverse;
		--Header-justify-content: space-between;
		--Header-logoImage-block-size: var(--size-24);
		--Header-padding-block: var(--size-16);
	}

	.Header--disableSticky {
		--Header-flex-direction: initial;
	}

	.Header-button {
		inset: auto 0 0;
		position: fixed;
	}

	.Header-logoLink {
		order: 3;
	}

	.Header-cardUserProfileCta {
		margin-block: -0.25rem;
	}

	.Header-button:not(.Header-button--visibleOnMobile) {
		display: none;
	}
}

@media (width >= 64em) {
	.Header {
		--Header-padding-inline: var(--size-64);
	}

	.Header.is-sticky {
		--Header-padding-inline: var(--size-16);
	}

	.Header-button {
		margin-inline-start: auto;
	}

	.Header-menuIcon {
		--Icon-size: var(--size-32);
	}
}
