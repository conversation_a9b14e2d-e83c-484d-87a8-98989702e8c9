{% if items %}
	<nav class="Pagination" aria-label="{{ label }}">
		<ul class="Pagination-items">
			{# Print first item if we are not on the first page. #}
			{% if items.first %}
				<li class="Pagination-item Pagination-item--first">
					<a class="Pagination-link" href="{{ items.first.href }}">
						<span class="u-hiddenVisually">{{ 'First page'|tc }}</span>
						<span aria-hidden="true">{{ items.first.text|default('« First'|tc) }}</span>
					</a>
				</li>
			{% endif %}
			{# Print previous item if we are not on the first page. #}
			{% if items.previous %}
				<li class="Pagination-item Pagination-item--previous">
					<a class="Pagination-link" href="{{ items.previous.href }}" rel="prev">
						<span class="u-hiddenVisually">{{ 'Previous page'|tc }}</span>
						<span aria-hidden="true">{{ items.previous.text|default('‹ Previous'|tc) }}</span>
					</a>
				</li>
			{% endif %}
			{# Add an ellipsis if there are further previous pages. #}
			{% if ellipses.previous %}
				<li class="Pagination-item Pagination-item--ellipsis">&hellip;</li>
			{% endif %}
			{# Now generate the actual pager piece. #}
			{% for key, item in items.pages %}
				{% set is_current = current == key %}
				<li class="Pagination-item">
					<a class="Pagination-link" href="{{ item.href }}" {% if is_current %}aria-current="page"{% endif %}>
						<span class="u-hiddenVisually">{{ 'Page'|tc }}</span>
						<span class="Pagination-number">{{ key }}</span>
					</a>
				</li>
			{% endfor %}
			{# Add an ellipsis if there are further next pages. #}
			{% if ellipses.next %}
				<li class="Pagination-item Pagination-item--ellipsis">&hellip;</li>
			{% endif %}
			{# Print next item if we are not on the last page. #}
			{% if items.next %}
				<li class="Pagination-item Pagination-item--next">
					<a class="Pagination-link" href="{{ items.next.href }}" rel="next">
						<span class="u-hiddenVisually">{{ 'Next page'|tc }}</span>
						<span aria-hidden="true">{{ items.next.text|default('Next ›'|tc) }}</span>
					</a>
				</li>
			{% endif %}
			{# Print last item if we are not on the last page. #}
			{% if items.last %}
				<li class="Pagination-item Pagination-item--last">
					<a class="Pagination-link" href="{{ items.last.href }}">
						<span class="u-hiddenVisually">{{ 'Last page'|tc }}</span>
						<span aria-hidden="true">{{ items.last.text|default('Last »'|tc) }}</span>
					</a>
				</li>
			{% endif %}
		</ul>
	</nav>
{% endif %}
