/** @define Dropdown; */

.Dropdown-button {
	align-items: center;
	background: var(--shared-color-surface-primary);
	border: 0.0625rem solid
		var(--Dropdown-button-border-color, var(--shared-color-border-disabled));
	display: flex;
	inline-size: 100%;
	justify-content: space-between;
	line-height: var(--input-button-lh);
	max-block-size: var(--input-min-block-size);
	outline: var(--Dropdown-outline, none);
	padding: var(--Dropdown-button-padding);
}

.Dropdown-button[aria-expanded="true"] .Dropdown-icon {
	transform: rotate(180deg);
}

.Dropdown-icon {
	flex-shrink: 0;
}

@media (48em > width) {
	.Dropdown-button {
		--Dropdown-button-border-color: transparent;
		--Dropdown-button-padding: var(--size-24) 0;

		font: var(--typo-HeadlineS);
		letter-spacing: var(--typo-HeadlineS-letter-spacing);
	}

	.Dropdown-tags {
		display: none;
	}

	.Dropdown-list {
		display: grid;
		grid-template-rows: 0fr;
		transition:
			grid-template-rows var(--default-transition-duration),
			padding-block-end var(--default-transition-duration);

		&.is-active {
			grid-template-rows: 1fr;
			padding-block-end: var(--size-32);
		}
	}

	.Dropdown-listContent {
		overflow: hidden;
	}

	.Dropdown-label {
		padding: var(--size-12) 0;
	}

	.Dropdown-optionLabel {
		color: var(--shared-color-text-secondary);
		padding: var(--size-4) 0;
	}
}

@media (width >= 48em) {
	.Dropdown {
		position: relative;
	}

	.Dropdown-button {
		--Dropdown-button-padding: var(--size-12) var(--size-8) var(--size-12)
			var(--size-16);

		&:focus-visible,
		&[aria-expanded="true"] {
			--Dropdown-button-border-color: transparent;
			--focus-outline-offset: 0;
			--Dropdown-outline: var(--focus-outline);
		}
	}

	.Dropdown-list {
		background: var(--shared-color-surface-primary);
		border: solid var(--Dropdown-border-color, transparent);
		border-width: 0 0.125rem 0.125rem;
		inset: 100% -0.125rem auto -0.125rem;
		max-block-size: 20rem;
		opacity: var(--opacity, 0);
		overflow-y: auto;
		pointer-events: var(--pointer-events, none);
		position: absolute;
		z-index: 2;

		&.is-active {
			--Dropdown-border-color: var(--shared-color-border-full);
			--opacity: 1;
			--pointer-events: auto;
		}
	}

	.Dropdown-optionLabel {
		color: var(--shared-color-text-secondary);
		padding: var(--size-4) var(--size-16);
	}

	.Dropdown-label {
		background: var(--Dropdown-label-background-color, transparent);
		cursor: pointer;
		display: block;
		padding: var(--size-12) var(--size-16);
		position: relative;
		transition: background var(--default-transition-duration);

		&:hover,
		&.is-current,
		&.is-active,
		.Dropdown-option.is-current & {
			--Dropdown-label-background-color: var(--shared-color-surface-subtle);
		}
	}

	.Dropdown-tagsContainer {
		background: var(--shared-color-surface-primary);
		display: flex;
		inline-size: calc(100% - var(--size-40));
		inset: 0.0625rem auto 0.0625rem 0.0625rem;
		overflow: hidden;
		position: absolute;

		&:empty,
		&:has(template:only-child),
		&:has(.Dropdown-tags:empty) {
			display: none;
		}

		&::before,
		&::after {
			content: "";
			position: absolute;
		}

		&::before {
			background: linear-gradient(
				to left,
				transparent,
				var(--shared-color-surface-primary)
			);
			inline-size: var(--size-16);
			inset: 0 auto 0 0;
		}

		&::after {
			background: linear-gradient(
				to right,
				transparent,
				var(--shared-color-surface-primary)
			);
			inline-size: var(--size-24);
			inset: 0 0 0 auto;
		}
	}

	.Dropdown-tags {
		align-items: center;
		display: flex;
		gap: var(--size-4);
		margin-inline-end: var(--size-8);
		overflow-x: auto;
		overflow-y: hidden;
		padding: var(--size-8);
		scrollbar-width: thin;
		white-space: nowrap;
	}
}
