{% set combobox_label = "combobox-label-" ~ random() %}

{% set checkbox_label %}
	{% include "@elements/form-element/label/label.twig" with {
		title: "",
		for: "checkbox",
		classes: ["Dropdown-label", "js-Dropdown-label"],
	} only %}
{% endset %}

{% set checkbox %}
	{% include "@elements/form-element/option/option.twig" with {
		type: "checkbox",
		id: "checkbox",
	} only %}
{% endset %}

{% set template_input %}
	{% include "@elements/form-element/form-element.twig" with {
		children: checkbox,
		label: checkbox_label,
		label_display: "after",
		type: "checkbox",
	} only %}
{% endset %}

<div class="Dropdown js-Dropdown {{ classes|join(" ") }}">
	<button
		role="combobox"
		aria-controls="{{ label|clean_id }}_listbox"
		tabindex="0"
		aria-expanded="false"
		aria-labelledby="{{ combobox_label }}"
		class="Dropdown-button js-Dropdown-button"
		type="button"
	>
		<span id="{{ combobox_label }}">{{ label }}</span>
		{% include "@elements/icon/icon.twig" with {
			classes: ["Dropdown-icon"],
			name: "chevron_down",
		} only %}
	</button>

	<div class="Dropdown-tagsContainer">
		<div class="Dropdown-tags {{ tagsClasses|join(" ") }}"></div>
	</div>

	<template class="js-Dropdown-tagTemplate">
		{% include "@patterns/tags/tag/tag.twig" with {
			button: true,
			label: "Tag",
		} only %}
	</template>

	<div role="listbox" id="{{ label|clean_id }}_listbox" class="Dropdown-list js-Dropdown-list">
		<div class="Dropdown-listContent js-Dropdown-listContent">
			{% for option in options %}
				<div role="option" class="Dropdown-option js-Dropdown-option">
					<label class="Dropdown-label js-Dropdown-label">
						<input type="checkbox" name="name" value="{{ option }}">
						{{ option }}
					</label>
				</div>
			{% endfor %}
		</div>
	</div>
	<template class="js-Dropdown-optionTemplate">
		<div role="option" class="Dropdown-option js-Dropdown-option">
			{{ template_input }}
		</div>
	</template>
</div>
