import { liteClient as algoliasearch } from "algoliasearch/lite";
import Dropdown from "./dropdown/dropdown.js";

const FILTER_DEPARTMENT = "department";
const FILTER_LOCATION = "location";
const ID = "Search-filter";
const HIDDEN_CLASS = "is-hidden";

export default class Search {
	#debounce;

	#elements;

	/** @type {import("algoliasearch").SearchIndex} */
	#client;

	/** @type {import("@algolia/client-search").SearchResponse} */
	#results;

	/** @type {number} */
	#page = 0;

	#state = {
		/** @type {string[]} */
		facetFilters: [],
		/** @type {boolean} */
		isInitialRender: true,
		/** @type {boolean} */
		isPartnerJob: false,
		/** @type {string} */
		searchLanguage: "de",
		/** @type {string[]} */
		selectedDepartments: [],
		/** @type {string[]} */
		selectedLocations: [],
	};

	/**
	 * @param {HTMLElement} container
	 */
	constructor(container) {
		this.selectors = {
			bar: ".js-Search-bar",
			defaultFilters: ".js-Search-default-filters",
			departmentDropdown: ".js-Search-dropdownDepartment",
			departmentDropdownList:
				".js-Search-dropdownDepartment .js-Dropdown-listContent",
			departmentTags: ".js-Search-dropdownDepartment .js-Search-tags",
			dropdownOptionTemplate: ".js-Dropdown-optionTemplate",
			loadMoreButton: ".js-Search-loadMoreButton",
			locationDropdown: ".js-Search-dropdownLocation",
			locationDropdownList:
				".js-Search-dropdownLocation .js-Dropdown-listContent",
			locationTags: ".js-Search-dropdownLocation .js-Search-tags",
			mobileFiltersOverlay: ".js-Search-mobileFiltersOverlay",
			mobileFiltersToggle: ".js-Search-mobileFiltersToggle",
			mobileFiltersOpener: ".js-Search-mobileFiltersOpener",
			mobileDepartmentTags: ".js-Search-mobileDepartmentTags",
			mobileLocationTags: ".js-Search-mobileLocationTags",
			skeletonLoader: ".SkeletonLoader",
			searchResultsContainer: ".Search-results",
			searchEmptyState: ".Search-emptyState",
			searchResults: ".js-CardsList-list",
			showResultsButton: ".js-Search-showResultsButton",
			stats: ".js-Search-stats",
			strings: ".js-Search-strings",
			tagTemplate: ".js-Dropdown-tagTemplate",
		};

		this.#elements = this.#createUIElementsMap(container, this.selectors);
		this.toggleMobileFilters = container.querySelectorAll(
			this.selectors.mobileFiltersToggle,
		);

		this.translationStrings = JSON.parse(this.#elements.strings.innerText);

		this.algoliaAppId = container.dataset.algoliaAppId;
		this.algoliaIndexName = container.dataset.algoliaIndexName;
		this.alogiaApiKey = container.dataset.algoliaApiKey;

		this.#init(container);
	}

	/**
	 *
	 * @param {HTMLElement} container - The container in which to search for the selectors.
	 * @param {object} selectors - The selectors to use.
	 * @returns {object} An object containing the elements selected from the container.
	 */
	#createUIElementsMap(container, selectors) {
		return Object.keys(selectors).reduce((elements, key) => {
			elements[key] = container.querySelector(selectors[key]);
			return elements;
		}, {});
	}

	/**
	 * Sets the initial facet filters for the search.
	 * @private
	 */
	#setInitialFacetFilters() {
		const { selectedDepartments, selectedLocations } = this.#state;

		this.#state.facetFilters = [
			this.#state.searchLanguage,
			this.#state.isPartnerJob,
		];

		if (selectedDepartments.length > 0) {
			this.#state.facetFilters.push(
				selectedDepartments.map((value) => `department:${value}`),
			);
		}

		if (selectedLocations.length > 0) {
			this.#state.facetFilters.push(
				selectedLocations.map((value) => `location:${value}`),
			);
		}
	}

	/**
	 * Sets the initial state of the search component.
	 * @param {HTMLElement} container - The container element.
	 * @private
	 * @returns {void}
	 */
	#setInitialState(container) {
		this.defaultFilters = this.#elements.defaultFilters
			? JSON.parse(this.#elements.defaultFilters.textContent)
			: {};

		if (this.defaultFilters.department) {
			this.#state.selectedDepartments = this.defaultFilters.department;
		}

		if (this.defaultFilters.location) {
			this.#state.selectedLocations = this.defaultFilters.location;
		}

		// used as facetFilters with Algolia search
		this.#state.isPartnerJob = `is_partner_job:${this.defaultFilters.is_partner_job}`;
		this.#state.searchLanguage = `search_api_language:${container.dataset.searchLanguage || "de"}`;
	}

	// eslint-disable-next-line jsdoc/require-jsdoc
	#initDropdowns() {
		new Dropdown(this.#elements.departmentDropdown);
		new Dropdown(this.#elements.locationDropdown);
	}

	/**
	 * Initialize
	 * @param {HTMLElement} container
	 */
	async #init(container) {
		const { skeletonLoader } = this.#elements;

		if (skeletonLoader) {
			skeletonLoader.hidden = false;
		}

		this.#client = algoliasearch(this.algoliaAppId, this.alogiaApiKey);

		this.#setInitialState(container);
		this.#setInitialFacetFilters();

		await this.#getResults({
			query: "",
			facetFilters: this.#state.facetFilters,
		});

		this.#renderDropdown(FILTER_DEPARTMENT);
		this.#renderDropdown(FILTER_LOCATION);

		if (this.#state.selectedDepartments.length) {
			this.#renderDepartmentTags();
		}

		if (this.#state.selectedLocations.length) {
			this.#renderLocationTags();
		}

		this.#initDropdowns();

		await this.#renderResults();

		if (skeletonLoader) {
			skeletonLoader.hidden = true;
		}

		this.#updateLoadMoreButton();

		this.isInitialRender = false;

		this.#addEventListeners();
		this.#toggleMobileFilters();
	}

	/**
	 * Updates the UI state by rendering dropdowns, tags, filter count, load more button, and search results.
	 * @private
	 * @returns {void}
	 */
	#updateUIState() {
		this.#renderDropdown(FILTER_DEPARTMENT);
		this.#renderDropdown(FILTER_LOCATION);

		this.#renderDepartmentTags();
		this.#renderLocationTags();

		this.#updateLoadMoreButton();

		this.#elements.searchResults.innerHTML = "";
		this.#renderResults();
	}

	/**
	 * Add event listeners
	 */
	#addEventListeners() {
		this.#elements.bar?.addEventListener(
			"input",
			this.#onFilterChange.bind(this),
		);

		this.#elements.bar?.addEventListener("submit", (event) => {
			event.preventDefault();
		});

		this.#elements.loadMoreButton?.addEventListener(
			"click",
			this.#loadPages.bind(this),
		);
	}

	/**
	 * Handle filter change
	 */
	async #onFilterChange() {
		// clear previous debounce
		clearTimeout(this.#debounce);

		// very simple debounce
		this.#debounce = setTimeout(async () => {
			// restart pagination
			this.#page = 0;
			await this.#getResults();
			this.#updateUIState();
		}, 500);
	}

	/**
	 * Handle tag removal
	 * @param {Event} event
	 */
	#onTagRemove(event) {
		/** @type {HTMLButtonElement} */
		const button = event.currentTarget;

		const { name, value } = button.dataset;

		/** @type {HTMLInputElement} */
		const checkbox = this.#elements.bar?.querySelector(
			`[name="${name}"][value="${value}"]`,
		);

		if (checkbox) {
			checkbox.checked = false;
		}

		button.parentElement.remove(); // to remove mobile tag

		this.#onFilterChange();
	}

	/**
	 * Asynchronously retrieves search results based on the filter values.
	 * The results are stored in this.#results.
	 * @param {{query: string, facetFilters: string[][]}} filterValues - The filter values to use. [Optional]
	 * @private
	 * @returns {Promise<void>} A promise that resolves when the search results are retrieved.
	 */
	async #getResults(filterValues) {
		const { query, facetFilters } = filterValues || this.#getFilterValues();

		try {
			this.#results = await this.#client.search({
				requests: [
					{
						attributesToRetrieve: ["rendered_card"],
						facets: ["*"],
						indexName: this.algoliaIndexName,
						page: this.#page,
						facetFilters,
						query,
					},
				],
			});
		} catch (error) {
			throw new Error("Error fetching search results", { cause: error });
		}
	}

	/**
	 * Get filter values from filter bar
	 * @returns {{query: string, facetFilters: string[][]}}
	 */
	#getFilterValues() {
		const formData = new FormData(this.#elements.bar);

		this.#state.selectedDepartments = formData.getAll(FILTER_DEPARTMENT);
		this.#state.selectedLocations = formData.getAll(FILTER_LOCATION);
		const query = formData.get("query")?.toString() ?? "";

		const facetFilters = [this.#state.searchLanguage, this.#state.isPartnerJob];

		if (this.#state.selectedDepartments.length) {
			facetFilters.push(
				this.#state.selectedDepartments.map((value) => `department:${value}`),
			);
		}

		if (this.#state.selectedLocations.length) {
			facetFilters.push(
				this.#state.selectedLocations.map((value) => `location:${value}`),
			);
		}

		return { query, facetFilters };
	}

	/**
	 * Render search results
	 */
	#renderResults() {
		this.#elements.searchEmptyState.hidden = true;
		const results = this.#results?.results[0];

		if (!this.#elements.searchResults || !this.#elements.stats) {
			return;
		}

		if (!results.nbHits) {
			this.#elements.searchResultsContainer.classList.add(HIDDEN_CLASS);
			this.#elements.searchEmptyState.hidden = false;
			this.#elements.searchEmptyState.focus();
			return;
		}

		const cardsHTML = results.hits
			.map((hit) => `<li class="CardsList-item">${hit.rendered_card}</li>`)
			.join("");

		let statsHTML = "";
		let showResultsHTML = "";

		switch (results.nbHits) {
			case 1:
				statsHTML = this.translationStrings.stats_results_single;
				showResultsHTML = this.translationStrings.show_results_single;
				this.#elements.searchResultsContainer.classList.remove(HIDDEN_CLASS);
				break;
			default:
				statsHTML = this.translationStrings.stats_results_multiply;
				showResultsHTML = this.translationStrings.show_results_multiply;
				this.#elements.searchResultsContainer.classList.remove(HIDDEN_CLASS);
				break;
		}

		statsHTML = statsHTML.replace("{{count}}", results.nbHits.toString());
		showResultsHTML = showResultsHTML.replace(
			"{{count}}",
			results.nbHits.toString(),
		);

		this.#elements.searchResults.innerHTML += cardsHTML;
		this.#elements.stats.innerHTML = statsHTML;
		this.#elements.showResultsButton.innerHTML = showResultsHTML;
	}

	/**
	 * Renders a dropdown based on the given type.
	 * @param {string} type - The type of dropdown to render.
	 * @private
	 * @returns {void}
	 */
	#renderDropdown(type) {
		const key =
			type === FILTER_DEPARTMENT ? FILTER_DEPARTMENT : FILTER_LOCATION;
		const hasListForType = this.#elements[`${key}DropdownList`];
		const results = this.#results?.results[0];

		if (!results?.facets?.[key]) {
			console.debug(`No results to render for ${key}`);
			return;
		}

		if (!hasListForType) {
			console.debug(`${key} dropdown list not found`);
			return;
		}

		const values = Object.keys(results.facets[key]);

		if (key === FILTER_DEPARTMENT) {
			this.#renderDepartmentDropdown(values);
		} else {
			this.#renderLocationDropdown(values);
		}
	}

	/**
	 * Renders the department dropdown list with checkboxes.
	 * @param {Array} values - The values to be rendered as checkboxes.
	 * @private
	 * @returns {void}
	 */
	#renderDepartmentDropdown(values) {
		const departmentDropdownContainer = this.#elements.departmentDropdownList;
		departmentDropdownContainer.innerHTML = "";

		for (const value of values) {
			// Check if the value exists in the default filters for the department
			let isChecked = false;
			if (
				this.#state.isInitialRender &&
				this.#state.selectedDepartments.length
			) {
				isChecked = this.#state.selectedDepartments.includes(value);
			}

			const checkbox = this.#createCheckbox({
				label: value,
				name: FILTER_DEPARTMENT,
				value,
				isChecked,
			});
			this.#elements.departmentDropdownList.appendChild(checkbox);
		}
	}

	/**
	 * Renders the location dropdown list with checkboxes.
	 * @param {Array} values - The values to be rendered as checkboxes.
	 * @private
	 * @returns {void}
	 */
	#renderLocationDropdown(values) {
		const locationDropdownContainer = this.#elements.locationDropdownList;
		locationDropdownContainer.innerHTML = "";

		/** @type {Map<string, {label: string, value: string}[]>} */
		const groups = new Map();

		for (const value of values) {
			const [group, label] = value.split(" > ");

			if (!groups.has(group)) {
				groups.set(group, []);
			}

			// Check if the value exists in the default filters for the location
			let isChecked = false;
			if (this.#state.isInitialRender && this.#state.selectedLocations.length) {
				isChecked = this.#state.selectedLocations.includes(value);
			}

			const checkbox = this.#createCheckbox({
				label,
				name: FILTER_LOCATION,
				value,
				isChecked,
			});

			groups.get(group)?.push({ checkbox, value });
		}

		for (const [group, item] of groups) {
			const optGroup = document.createElement("div");
			optGroup.dataset.role = "optgroup";
			const label = document.createElement("div");
			label.classList.add("Dropdown-optionLabel", "u-typo-TextS");
			label.innerHTML = group;
			optGroup.appendChild(label);

			for (const { checkbox } of item) {
				optGroup.appendChild(checkbox);
			}

			this.#elements.locationDropdownList.appendChild(optGroup);
		}
	}

	/**
	 * Render department tags
	 */
	#renderDepartmentTags() {
		if (
			!this.#elements.departmentTags ||
			!this.#elements.mobileDepartmentTags
		) {
			return;
		}

		const formData = new FormData(this.#elements.bar);
		const key = FILTER_DEPARTMENT;
		const values = formData.getAll(key);

		this.#elements.departmentTags.innerHTML = "";
		this.#elements.mobileDepartmentTags.innerHTML = "";

		for (const value of values) {
			const [dropdownTag, mobileTag] = this.#createTag({
				label: String(value),
				name: key,
				value: String(value),
			});

			this.#elements.departmentTags.appendChild(dropdownTag);
			this.#elements.mobileDepartmentTags.appendChild(mobileTag);
		}
	}

	/**
	 * Render location tags
	 */
	#renderLocationTags() {
		if (!this.#elements.locationTags || !this.#elements.mobileLocationTags) {
			return;
		}

		const formData = new FormData(this.#elements.bar);
		const key = FILTER_LOCATION;
		const values = formData.getAll(key);

		this.#elements.locationTags.innerHTML = "";
		this.#elements.mobileLocationTags.innerHTML = "";

		for (const value of values) {
			const [, city] = String(value).split(" > ");
			const [dropdownTag, mobileTag] = this.#createTag({
				label: `${city}`,
				name: key,
				value: String(value),
			});
			this.#elements.locationTags.appendChild(dropdownTag);
			this.#elements.mobileLocationTags.appendChild(mobileTag);
		}
	}

	/**
	 * Create tag element
	 * @param {object} options
	 * @param {string} options.label
	 * @param {string} options.name
	 * @param {string} options.value
	 * @returns {HTMLElement}
	 */
	#createTag({ label, name, value }) {
		const clone = this.#elements.tagTemplate.content.cloneNode(true);

		const dropdownTag = clone.querySelector(".js-Tag");
		const dropdownTagButton = clone.querySelector(".js-Tag-button");
		dropdownTagButton.addEventListener("click", this.#onTagRemove.bind(this));

		const clone2 = this.#elements.tagTemplate.content.cloneNode(true);
		const mobileTag = clone2.querySelector(".js-Tag");
		const mobileTagButton = clone2.querySelector(".js-Tag-button");
		mobileTagButton.addEventListener("click", this.#onTagRemove.bind(this));

		const dropdownTagLabel = clone.querySelector(".js-Tag-label");
		const mobileTagLabel = clone2.querySelector(".js-Tag-label");

		dropdownTagLabel.textContent = label;
		mobileTagLabel.textContent = label;
		dropdownTagButton.setAttribute("data-name", name);
		mobileTagButton.setAttribute("data-name", name);
		dropdownTagButton.setAttribute("data-value", value);
		mobileTagButton.setAttribute("data-value", value);

		return [dropdownTag, mobileTag];
	}

	/**
	 * Create checkbox element
	 * @param {object} options
	 * @param {string} options.label
	 * @param {string} options.name
	 * @param {string} options.value
	 * @param {boolean} options.isChecked
	 * @returns {HTMLElement}
	 */
	#createCheckbox({ label, name, value, isChecked }) {
		const id = `${ID}-${value.replace(/[^a-zA-Z0-9]/g, "_").toLocaleLowerCase()}`;

		// Clone the template and access its content
		const clone = this.#elements.dropdownOptionTemplate.content.cloneNode(true);

		// Select cloned elements and set attributes
		const option = clone.querySelector(".js-Dropdown-option");
		const optionLabel = clone.querySelector("label");
		const checkbox = clone.querySelector('input[type="checkbox"]');

		optionLabel.setAttribute("for", id);
		checkbox.setAttribute("id", id);
		checkbox.setAttribute("name", name);
		checkbox.setAttribute("value", value);
		checkbox.checked = isChecked ?? false;

		optionLabel.append(document.createTextNode(label));

		return option;
	}

	/**
	 * Loads the next page of results based on the value from this.#page.
	 * It also updates the load more button text based on the number of remaining pages.
	 * @private
	 * @async
	 * @returns {Promise<void>} A promise that resolves when the pages are loaded.
	 */
	async #loadPages() {
		this.#page += 1;

		await this.#getResults();
		await this.#renderResults();

		this.#updateLoadMoreButton();
	}

	/**
	 * Update Load more button text
	 */
	#updateLoadMoreButton() {
		if (!this.#elements.loadMoreButton) {
			return;
		}

		const results = this.#results?.results[0];

		const remainingHits =
			results.nbHits - (this.#page + 1) * results.hitsPerPage;

		if (remainingHits <= 0) {
			this.#elements.loadMoreButton.classList.add(HIDDEN_CLASS);
		} else {
			// Update the button text based on the number of remaining hits to load
			if (remainingHits > 1) {
				this.#elements.loadMoreButton.innerHTML =
					this.translationStrings.load_more_multiply.replace(
						"{{count}}",
						remainingHits,
					);
			} else {
				this.#elements.loadMoreButton.innerHTML =
					this.translationStrings.load_more_single.replace(
						"{{count}}",
						remainingHits,
					);
			}

			this.#elements.loadMoreButton.classList.remove(HIDDEN_CLASS); // display the button if there are remaining pages
		}
	}

	/**
	 * Toggles the display of the mobile filters overlay
	 */
	#toggleMobileFilters() {
		const { mobileFiltersOverlay } = this.#elements;
		this.toggleMobileFilters.forEach((toggle) => {
			toggle.addEventListener("click", () => {
				if (mobileFiltersOverlay.hasAttribute("open")) {
					mobileFiltersOverlay.close();
				} else {
					mobileFiltersOverlay.showModal();
				}
			});
		});
	}
}
