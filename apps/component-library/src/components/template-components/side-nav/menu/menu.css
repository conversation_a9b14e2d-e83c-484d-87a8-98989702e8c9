/** @define SideNavMenu; */

.SideNavMenu-item {
	padding-block: var(--SideNavMenu-item-padding-block, var(--size-32));
	text-wrap: balance;
}

.SideNavMenu-item,
.SideNavMenu-list,
.SideNavMenu-list--lv1 {
	transition: all var(--default-transition-duration) ease-in-out;
}

.SideNavMenu-list--lv1 {
	--SideNavMenu-item-padding-block: var(--size-32);
}

.SideNavMenu-list--lv1 > .SideNavMenu-item {
	border-block-start: var(--border-distinct);
}

.SideNavMenu-list--lv1.is-visible > .SideNavMenu-item:last-child {
	border-block-end: var(--border-distinct);
}

.SideNavMenu-toggle {
	align-items: center;
	display: flex;
	justify-content: space-between;
	transition: all var(--default-transition-duration) ease-in-out;
}

.SideNavMenu-toggle[aria-expanded] {
	inline-size: 100%;
}

.SideNavMenu-toggle[aria-expanded="true"] {
	color: var(--shared-color-text-secondary);
	flex-direction: row-reverse;
	font: var(--typo-TextM);
	gap: var(--SideNavMenu--toggle-gap);
	inset: 0 auto auto 0;
	justify-content: flex-end;
	position: absolute;
}

.SideNavMenu-list--lv2 {
	--SideNavMenu-item-padding-block: var(--size-12);
	--SideNavMenu-nestedMenu-margin-block-start: var(--size-40);

	inline-size: 100%;
	inset: 0 auto auto 100%;
	margin-block-start: var(--SideNavMenu-nestedMenu-margin-block-start);
	padding-inline-start: var(--SideNavMenu-item-padding-inline-start);
	position: absolute;
}

.SideNavMenu-toggleIcon {
	flex-shrink: 0;
}

.SideNavMenu-toggle[aria-expanded="true"] .SideNavMenu-toggleIcon {
	color: var(--shared-color-text-primary);
	transform: rotate(180deg);
}

.SideNavMenu-item:has(.SideNavMenu-toggle[aria-expanded="true"]) {
	border-block-start: none;
}

.SideNavMenu-list--lv1:not(.is-visible) {
	transform: translateX(-100%);
}

.SideNavMenu-list--lv1:not(.is-visible) > .SideNavMenu-item:not(.is-visible),
.SideNavMenu-list--lv1.is-visible .SideNavMenu-list--lv2 {
	opacity: 0;
	visibility: hidden; /* to prevent links get focus */
}

.SideNavMenu-list--lv1:not(.is-visible)
	.SideNavMenu-item.is-visible
	.SideNavMenu-toggle {
	inset-inline-start: 100%;
	transform: translateX(var(--SideNavMenu--toggle-translate));
}

@media (64em > width) {
	.SideNavMenu {
		--SideNavMenu--toggle-gap: var(--size-24);
		--SideNavMenu--toggle-translate: 0;
		--SideNavMenu-item-padding-inline-start: var(--size-56);
	}
}

@media (width >= 64em) {
	.SideNavMenu {
		--SideNavMenu--toggle-gap: var(--size-32);
		--SideNavMenu--toggle-translate: calc(-1 * var(--size-64));
		--SideNavMenu-item-padding-inline-start: 0;
	}
}
