<nav {% if label %} aria-label="{{ label }}"{% endif %} itemscope itemtype="http://schema.org/SiteNavigationElement" class="SideNavMenu">
	<ul class="SideNavMenu-list SideNavMenu-list--lv1 js-SideNavMenu-list--lv1 is-visible">
		{%- for item in items -%}
			{% set target_id = random() %}
			<li class="SideNavMenu-item is-visible" itemprop="name">
				{% if item.below %}
					<button
						aria-controls="{{ target_id }}"
						aria-expanded="false"
						aria-label="Open sub-menu: {{ item.title }}"
						class="SideNavMenu-toggle u-typo-HeadlineM js-SideNavMenu-toggle u-link"
						id="label-{{ target_id }}"
						type="button"
					>
						<span class="SideNavMenu-title">
							{{ item.title }}
						</span>
						{% include "@elements/icon/icon.twig" with {
							classes: ["SideNavMenu-toggleIcon"],
							name: "chevron_right",
							size: 'large'
						} only %}
					</button>
				{% else %}
					{% include "@elements/navigation-link/navigation-link.twig" with item|merge({
						label: item.title,
						itemprop: "url",
						level: "one"
					}) only %}
				{% endif %}

				{%- if item.below -%}
					<ul class="SideNavMenu-list SideNavMenu-list--lv2" role="menu" id="{{ target_id }}" aria-labelledby="label-{{ target_id }}">
						{%- for child in item.below -%}
							<li class="SideNavMenu-item" role="menuitem">
								{% include "@elements/navigation-link/navigation-link.twig" with child|merge({
									label: child.title,
									itemprop: 'url',
									level: "two"
								}) only %}
							</li>
						{%- endfor -%}
					</ul>
				{%- endif -%}
			</li>
		{%- endfor -%}
	</ul>
</nav>
