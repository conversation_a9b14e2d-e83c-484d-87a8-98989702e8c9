@import "./menu/menu.css";
@import "./_nav/_nav.css";

/** @define SideNav; */

.SideNav {
	/* --SideNav-offset-top is needed for the correct position for the sideNav when the admin toolbar is present */

	/* stylelint-disable-next-line length-zero-no-unit */
	--SideNav-offset-top: var(--header-offset-top, 0px);

	background: var(--shared-color-surface-primary);
	block-size: 100dvh;
	display: flex;
	flex-direction: column;
	inline-size: 100%;
	inset-block-start: 0;
	inset-inline-start: 0;
	max-inline-size: var(--SideNav-max-inline-size);
	min-inline-size: var(--SideNav-min-inline-size, 0);
	overflow-x: hidden;
	overflow-y: auto;
	padding-block: calc(
			var(--SideNav-offset-top) + var(--SideNav-padding-block-start)
		)
		var(--size-24);
	padding-inline: var(--SideNav-padding-inline-start)
		var(--SideNav-padding-inline-end);
	position: fixed;
	transition: transform var(--slower-transition-duration) ease-out;
	z-index: var(--Sidenav-z-index);
}

@media (width < 48rem) {
	.SideNav {
		--Sidenav-z-index: var(--bottom-layer);
	}
}

@media (width >= 48rem) {
	.SideNav {
		--Sidenav-z-index: var(--middle-layer);
	}
}

.SideNav:not(.is-open) {
	transform: translateX(-100%);
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
body:has(.SideNav.is-open) {
	overflow: hidden;
}

.SideNav-overlay {
	background: var(--shared-color-surface-invert);
	display: var(--SideNav-overlay-display, none);
	inset: 0;
	opacity: var(--SideNav-overlay-opacity, 0);
	position: fixed;
	transition: opacity var(--default-transition-duration);
}

.SideNav-menu {
	block-size: 100%;
	flex: 1;
	margin-inline: calc(var(--SideNav-padding-inline-start) * -1)
		calc(var(--SideNav-padding-inline-end) * -1);
	overflow: auto;
	padding-inline: var(--SideNav-padding-inline-start)
		var(--SideNav-padding-inline-end);
}

.SideNav-footer {
	align-items: center;
	display: flex;
	gap: var(--size-24);
	justify-content: space-between;
	padding-block-start: var(--SideNav-footer-padding-block-start);
}

@media (64em > width) {
	.SideNav {
		--SideNav-footer-padding-block-start: var(--size-16);
		--SideNav-max-inline-size: 100%;
		--SideNav-padding-block-end: var(--size-24);
		--SideNav-padding-block-start: var(--size-104);
		--SideNav-padding-inline-end: var(--size-24);
		--SideNav-padding-inline-start: var(--size-24);
	}
}

@media (width >= 64em) {
	.SideNav {
		--SideNav-footer-padding-block-start: var(--size-24);
		--SideNav-max-inline-size: max-content;
		--SideNav-min-inline-size: 34.375rem; /* 550px - magic number, it was decided to use this value after discussion */
		--SideNav-padding-block-end: var(--size-24);
		--SideNav-padding-block-start: var(--size-144);
		--SideNav-padding-inline-end: var(--size-112);
		--SideNav-padding-inline-start: var(--size-128);
	}

	/* stylelint-disable-next-line plugin/selector-bem-pattern */
	.Header.is-sticky .SideNav {
		--SideNav-padding-block-end: var(--size-24);
		--SideNav-padding-block-start: var(--size-112);
		--SideNav-padding-inline-end: var(--size-112);
		--SideNav-padding-inline-start: var(--size-80);
	}

	.SideNav.is-open + .SideNav-overlay {
		--SideNav-overlay-display: block;
		--SideNav-overlay-opacity: 0.5;
		z-index: var(--bottom-layer);
	}
}
