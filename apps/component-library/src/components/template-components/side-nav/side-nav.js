import Language from "./language/language.js";

/**
 * Class representing a Side Navigation.
 * @class
 * @param {HTMLElement} element - The HTML element.
 */
export default class SideNav {
	/**
	 * @param {HTMLElement} element
	 */
	constructor(element) {
		this.element = element;

		this.element.querySelectorAll(".js-SideNavMenu-toggle").forEach((item) => {
			item.addEventListener("click", (event) => this.#onClick(event));
		});

		this.headerElement = document.querySelector(".js-Header");

		const toggleButton = document.querySelector(".js-Header-sideNavButton");
		toggleButton?.addEventListener("click", () =>
			this.#toggleMenu(toggleButton),
		);

		document
			.querySelector(".js-SideNav-overlay")
			.addEventListener("click", () => this.#closeMenu(toggleButton));

		const langToggle = document.querySelector(".js-SideNavLanguage");
		if (langToggle) {
			new Language(langToggle);
		}

		// Add event listener for the 'esc' key
		window.addEventListener("keydown", this.#escListener.bind(this));

		// Set tabindex for inside menu elements to -1 initially
		this.#toggleTabindexInsideMenu(false);
	}

	/**
	 * Listens for the escape key press event.
	 * @param {KeyboardEvent} event - The keyboard event.
	 * @private
	 */
	#escListener(event) {
		if (
			event.key === "Escape" ||
			event.key === "Esc" ||
			event.code === "Escape"
		) {
			const toggleButton = document.querySelector(".js-Header-sideNavButton");
			if (this.element.classList.contains("is-open")) {
				this.#closeMenu(toggleButton);

				// Remove event listener for the 'esc' key after closing the menu
				window.removeEventListener("keydown", this.#escListener);
			}
		}
	}

	/**
	 * Toggles the side navigation menu.
	 * @param {HTMLElement} toggleButton - The toggle button element.
	 * @private
	 */
	#toggleMenu(toggleButton) {
		const currentAriaExpanded = toggleButton.getAttribute("aria-expanded");
		if (currentAriaExpanded === "true") {
			this.#closeMenu(toggleButton);
		} else {
			this.#openMenu(toggleButton);
		}
	}

	/**
	 * Opens the side navigation menu.
	 * @param {HTMLElement} button - The button element.
	 * @private
	 */
	#openMenu(button) {
		button.setAttribute("aria-expanded", "true");
		this.element.classList.add("is-open");
		SideNav.#toggleTabindexOutsideMenu(true);
		this.#toggleTabindexInsideMenu(true);

		const headerElementPositionTop =
			this.headerElement.getBoundingClientRect().top;

		// Needed for the correct position for the sideNav when the admin toolbar is present
		if (headerElementPositionTop > 0) {
			this.headerElement.style = `--header-offset-top: ${headerElementPositionTop}px`;
		}
	}

	/**
	 * Closes the side navigation menu.
	 * @param {HTMLElement} button - The button element.
	 * @private
	 */
	#closeMenu(button) {
		button.setAttribute("aria-expanded", "false");
		this.element.classList.remove("is-open");
		SideNav.#toggleTabindexOutsideMenu(false);
		this.#toggleTabindexInsideMenu(false);
	}

	/**
	 * Toggles the tabindex for the all focusable elements outside the menu.
	 * @param { boolean } isOpen - Boolean flag to determine if the menu is open.
	 * @private
	 */
	static #toggleTabindexOutsideMenu(isOpen) {
		document
			.querySelectorAll(
				'a, button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])',
			)
			.forEach((element) => {
				if (
					!element.closest(".js-SideNav") &&
					!element.classList.contains("js-Header-logoLink") &&
					!element.classList.contains("js-Header-sideNavButton")
				) {
					if (isOpen) {
						element.setAttribute("tabindex", "-1");
					} else {
						element.removeAttribute("tabindex");
					}
				}
			});
	}

	/**
	 * Toggles the tabindex for all focusable elements inside the menu.
	 * @param {boolean} isOpen - Boolean flag to determine if the menu is open.
	 * @private
	 */
	#toggleTabindexInsideMenu(isOpen) {
		this.element.querySelectorAll("a, button").forEach((element) => {
			if (isOpen) {
				element.removeAttribute("tabindex");
			} else {
				element.setAttribute("tabindex", "-1");
			}
		});
	}

	/**
	 * Handles the onClick event for the navigation menu.
	 * @param {MouseEvent} event - The mouse event.
	 * @private
	 */
	#onClick(event) {
		event.preventDefault();
		const toggle = event.currentTarget.closest(".js-SideNavMenu-toggle");
		const isOpen = toggle.getAttribute("aria-expanded") === "true";

		toggle.setAttribute("aria-expanded", String(!isOpen));
		const menu = this.element.querySelector(".js-SideNavMenu-list--lv1");
		this.element
			.querySelector(".js-SideNavMenu-list--lv1")
			.classList.toggle("is-visible");

		if (!isOpen) {
			menu
				.querySelectorAll(".js-SideNavMenu-list--lv1 > .SideNavMenu-item")
				.forEach((item) => {
					item.classList.remove("is-visible");
				});
			toggle.parentElement.classList.add("is-visible");
		} else {
			menu
				.querySelectorAll(".js-SideNavMenu-list--lv1 > .SideNavMenu-item")
				.forEach((item) => {
					item.classList.add("is-visible");
				});
		}
	}
}
