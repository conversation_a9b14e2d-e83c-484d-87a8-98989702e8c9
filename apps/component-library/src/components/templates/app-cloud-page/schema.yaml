$schema: http://json-schema.org/draft-07/schema
$id: /templates/app-cloud-page

type: object

required:
  - header
  - footer
  - content

additionalProperties: false

properties:
  content:
    type: string
    format: html
    description: patterns/*, apps/cloud/*

  header:
    type: string
    format: html
    description: template-components/header

  info_messages:
    type: array
    items:
      type: string
      format: html
      description: elements/info-message

  footer:
    type: string
    format: html
    description: template-components/footer
